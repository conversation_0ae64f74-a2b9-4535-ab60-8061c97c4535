/** @type {import('next').NextConfig} */
const nextConfig = {
  // 开发环境不使用静态导出，以支持中间件和API路由
  ...(process.env.NODE_ENV === "production" &&
  process.env.STATIC_EXPORT === "true"
    ? {
        output: "export",
        trailingSlash: true,
      }
    : process.env.NODE_ENV === "production"
      ? {
          output: "standalone",
        }
      : {}),

  images: {
    // 静态导出时禁用图片优化
    ...(process.env.NODE_ENV === "production" &&
    process.env.STATIC_EXPORT === "true"
      ? {
          unoptimized: true,
        }
      : {}),
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**",
      },
      {
        protocol: "http",
        hostname: "**",
      },
    ],
  },

  env: {
    CUSTOM_KEY: "my-value",
  },

  // 安全HTTP头部配置（仅在非静态导出模式下）
  ...(!(
    process.env.NODE_ENV === "production" &&
    process.env.STATIC_EXPORT === "true"
  )
    ? {
        async headers() {
          return [
            {
              source: "/(.*)",
              headers: [
                // 内容安全策略 (CSP)
                {
                  key: "Content-Security-Policy",
                  value: [
                    "default-src 'self'",
                    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com",
                    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
                    "font-src 'self' https://fonts.gstatic.com",
                    "img-src 'self' data: https: http:",
                    "connect-src 'self' https://www.google-analytics.com https://cloud.tencent.com",
                    "frame-src 'none'",
                    "object-src 'none'",
                    "base-uri 'self'",
                    "form-action 'self'",
                    "frame-ancestors 'none'",
                  ].join("; "),
                },
                // 防止点击劫持
                {
                  key: "X-Frame-Options",
                  value: "DENY",
                },
                // 防止MIME类型嗅探
                {
                  key: "X-Content-Type-Options",
                  value: "nosniff",
                },
                // XSS防护
                {
                  key: "X-XSS-Protection",
                  value: "1; mode=block",
                },
                // 引用策略
                {
                  key: "Referrer-Policy",
                  value: "strict-origin-when-cross-origin",
                },
                // 权限策略
                {
                  key: "Permissions-Policy",
                  value: [
                    "camera=()",
                    "microphone=()",
                    "geolocation=()",
                    "payment=()",
                    "usb=()",
                    "magnetometer=()",
                    "gyroscope=()",
                    "accelerometer=()",
                  ].join(", "),
                },
              ],
            },
          ];
        },
      }
    : {}),

  // 禁用X-Powered-By头部
  poweredByHeader: false,

  // 启用压缩
  compress: true,

  // 生成ETag
  generateEtags: true,
};

module.exports = nextConfig;
