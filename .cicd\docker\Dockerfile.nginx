# 🚀 上海荷阁科技 - Nginx 反向代理镜像
# 用于负载均衡和 SSL 终止

FROM nginx:1.25-alpine

# 安装必要工具
RUN apk add --no-cache \
    curl \
    openssl \
    certbot \
    certbot-nginx

# 创建必要目录
RUN mkdir -p /etc/nginx/ssl \
    && mkdir -p /var/log/nginx \
    && mkdir -p /var/cache/nginx

# 复制配置文件
COPY nginx/nginx.conf /etc/nginx/nginx.conf
COPY nginx/default.conf /etc/nginx/conf.d/default.conf

# 创建自签名证书（开发环境使用）
RUN openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/nginx-selfsigned.key \
    -out /etc/nginx/ssl/nginx-selfsigned.crt \
    -subj "/C=CN/ST=Shanghai/L=Shanghai/O=Hege Tech/OU=IT Department/CN=localhost"

# 创建 DH 参数文件
RUN openssl dhparam -out /etc/nginx/ssl/dhparam.pem 2048

# 创建健康检查脚本
RUN echo '#!/bin/sh\nnginx -t && curl -f http://localhost/health || exit 1' > /health-check.sh
RUN chmod +x /health-check.sh

# 设置正确的权限
RUN chown -R nginx:nginx /var/cache/nginx \
    && chown -R nginx:nginx /var/log/nginx \
    && chown -R nginx:nginx /etc/nginx/ssl

# 暴露端口
EXPOSE 80 443

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD /health-check.sh

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]

# 元数据标签
LABEL maintainer="<EMAIL>"
LABEL version="1.0.0"
LABEL description="上海荷阁科技 Nginx 反向代理服务"
