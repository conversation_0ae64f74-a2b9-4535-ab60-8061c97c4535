# Shanghai Hege Technology - Dependency Verification and Installation Script
# Ensures dependencies are available before running jobs

$ErrorActionPreference = "Continue"

Write-Host "Checking if dependencies are available..." -ForegroundColor Green

# Check if node_modules exists and has content
if (Test-Path "node_modules" -PathType Container) {
    $nodeModulesCount = (Get-ChildItem "node_modules" -Directory | Measure-Object).Count
    if ($nodeModulesCount -gt 0) {
        Write-Host "Dependencies found: $nodeModulesCount packages in node_modules" -ForegroundColor Green
        Write-Host "Verifying key dependencies..." -ForegroundColor Yellow
        
        # Check for critical dependencies
        $criticalDeps = @("next", "react", "typescript")
        $missingDeps = @()
        
        foreach ($dep in $criticalDeps) {
            if (-not (Test-Path "node_modules\$dep")) {
                $missingDeps += $dep
            }
        }
        
        if ($missingDeps.Count -eq 0) {
            Write-Host "All critical dependencies are available" -ForegroundColor Green
            exit 0
        } else {
            Write-Host "Missing critical dependencies: $($missingDeps -join ', ')" -ForegroundColor Yellow
            Write-Host "Running dependency installation..." -ForegroundColor Yellow
        }
    } else {
        Write-Host "node_modules directory exists but is empty" -ForegroundColor Yellow
        Write-Host "Running dependency installation..." -ForegroundColor Yellow
    }
} else {
    Write-Host "node_modules directory not found" -ForegroundColor Yellow
    Write-Host "Running dependency installation..." -ForegroundColor Yellow
}

# Run the dependency installation script
Write-Host "Executing dependency installation script..." -ForegroundColor Cyan
PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\install-dependencies.ps1"

if ($LASTEXITCODE -eq 0) {
    Write-Host "Dependencies are now available" -ForegroundColor Green
    exit 0
} else {
    Write-Host "Failed to install dependencies" -ForegroundColor Red
    exit 1
}
