#!/bin/bash
# 🚀 上海荷阁科技 - 应用健康检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
APP_URL="${APP_URL:-http://localhost:3000}"
HEALTH_ENDPOINT="${HEALTH_ENDPOINT:-/api/health}"
TIMEOUT="${TIMEOUT:-10}"
MAX_RETRIES="${MAX_RETRIES:-3}"
RETRY_INTERVAL="${RETRY_INTERVAL:-5}"

# 帮助信息
show_help() {
    cat << EOF
🚀 上海荷阁科技应用健康检查脚本

用法: $0 [选项]

选项:
    -u, --url URL              应用 URL (默认: $APP_URL)
    -e, --endpoint ENDPOINT    健康检查端点 (默认: $HEALTH_ENDPOINT)
    -t, --timeout TIMEOUT     请求超时时间(秒) (默认: $TIMEOUT)
    -r, --retries RETRIES     最大重试次数 (默认: $MAX_RETRIES)
    -i, --interval INTERVAL   重试间隔(秒) (默认: $RETRY_INTERVAL)
    -h, --help                显示此帮助信息

示例:
    $0                                          # 使用默认配置
    $0 -u http://app:3000 -e /health           # 自定义 URL 和端点
    $0 -t 30 -r 5 -i 10                       # 自定义超时和重试配置

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--url)
            APP_URL="$2"
            shift 2
            ;;
        -e|--endpoint)
            HEALTH_ENDPOINT="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -r|--retries)
            MAX_RETRIES="$2"
            shift 2
            ;;
        -i|--interval)
            RETRY_INTERVAL="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 构建完整的健康检查 URL
FULL_URL="${APP_URL}${HEALTH_ENDPOINT}"

log_info "开始健康检查..."
log_info "目标 URL: $FULL_URL"
log_info "超时时间: ${TIMEOUT}s"
log_info "最大重试: $MAX_RETRIES 次"
log_info "重试间隔: ${RETRY_INTERVAL}s"

# 健康检查函数
check_health() {
    local url="$1"
    local timeout="$2"
    
    # 使用 curl 进行健康检查
    if command -v curl >/dev/null 2>&1; then
        curl -f -s --max-time "$timeout" "$url" >/dev/null 2>&1
        return $?
    fi
    
    # 如果没有 curl，尝试使用 wget
    if command -v wget >/dev/null 2>&1; then
        wget -q --timeout="$timeout" --tries=1 -O /dev/null "$url" >/dev/null 2>&1
        return $?
    fi
    
    log_error "未找到 curl 或 wget 命令"
    return 1
}

# 执行健康检查
retry_count=0
while [[ $retry_count -lt $MAX_RETRIES ]]; do
    log_info "执行健康检查 (第 $((retry_count + 1)) 次)..."
    
    if check_health "$FULL_URL" "$TIMEOUT"; then
        log_success "✅ 应用健康检查通过！"
        
        # 获取详细信息
        if command -v curl >/dev/null 2>&1; then
            log_info "获取应用详细信息..."
            response=$(curl -s --max-time "$TIMEOUT" "$FULL_URL" 2>/dev/null || echo "无法获取响应内容")
            log_info "响应内容: $response"
        fi
        
        exit 0
    else
        retry_count=$((retry_count + 1))
        
        if [[ $retry_count -lt $MAX_RETRIES ]]; then
            log_warning "❌ 健康检查失败，${RETRY_INTERVAL}s 后重试..."
            sleep "$RETRY_INTERVAL"
        else
            log_error "❌ 健康检查失败，已达到最大重试次数"
        fi
    fi
done

# 健康检查失败
log_error "🚨 应用健康检查失败！"
log_error "URL: $FULL_URL"
log_error "重试次数: $MAX_RETRIES"

# 尝试获取更多诊断信息
log_info "尝试获取诊断信息..."

# 检查网络连接
if command -v ping >/dev/null 2>&1; then
    host=$(echo "$APP_URL" | sed -E 's|^https?://([^:/]+).*|\1|')
    log_info "检查网络连接到 $host..."
    if ping -c 1 -W 3 "$host" >/dev/null 2>&1; then
        log_info "✅ 网络连接正常"
    else
        log_warning "❌ 网络连接失败"
    fi
fi

# 检查端口连接
if command -v nc >/dev/null 2>&1; then
    host=$(echo "$APP_URL" | sed -E 's|^https?://([^:/]+).*|\1|')
    port=$(echo "$APP_URL" | sed -E 's|^https?://[^:/]+:?([0-9]+)?.*|\1|')
    port=${port:-80}
    
    log_info "检查端口连接 $host:$port..."
    if nc -z -w3 "$host" "$port" 2>/dev/null; then
        log_info "✅ 端口连接正常"
    else
        log_warning "❌ 端口连接失败"
    fi
fi

exit 1
