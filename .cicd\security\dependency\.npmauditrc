# 🚀 上海荷阁科技 - npm audit 配置文件
# 用于依赖漏洞扫描配置

# ==========================================
# 审计级别配置
# ==========================================

# 设置审计级别 (info, low, moderate, high, critical)
audit-level=moderate

# ==========================================
# 忽略特定漏洞
# ==========================================

# 格式: vulnerability-id
# 示例: 1234567

# 临时忽略的漏洞 ID (需要定期审查)
# 注意: 只有在确认风险可接受时才添加到此列表

# ==========================================
# 生产环境依赖检查
# ==========================================

# 只检查生产环境依赖
production=true

# ==========================================
# 输出格式配置
# ==========================================

# 输出格式 (json, table)
format=json

# ==========================================
# 注意事项
# ==========================================

# 1. 定期更新此配置文件
# 2. 忽略的漏洞需要有明确的业务理由
# 3. 建议每月审查忽略列表
# 4. 高危和严重漏洞不应被忽略
