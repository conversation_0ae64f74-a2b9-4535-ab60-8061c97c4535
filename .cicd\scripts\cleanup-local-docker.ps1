# Shanghai Hege Technology - Local Docker Cleanup Script
# Clean up local Docker environment

param(
    [string]$ImageNameFilter = "hege-tech-web"
)

$ErrorActionPreference = "Continue"

Write-Host "Starting Docker cleanup..." -ForegroundColor Green

# Function to safely execute Docker commands
function Invoke-DockerCleanup {
    param(
        [string]$Command,
        [string]$Description,
        [string]$FailureMessage = "No items to process"
    )
    
    Write-Host $Description -ForegroundColor Yellow
    
    try {
        $result = Invoke-Expression $Command 2>$null
        if ($LASTEXITCODE -eq 0 -and $result) {
            Write-Host "✓ $Description completed" -ForegroundColor Green
            return $result
        } else {
            Write-Host "- $FailureMessage" -ForegroundColor Gray
            return $null
        }
    } catch {
        Write-Host "- $FailureMessage" -ForegroundColor Gray
        return $null
    }
}

# Step 1: Stop all related containers
Write-Host "`n=== Step 1: Stopping containers ===" -ForegroundColor Magenta
$runningContainers = docker ps -q -f name=$ImageNameFilter 2>$null
if ($runningContainers) {
    foreach ($container in $runningContainers) {
        Invoke-DockerCleanup -Command "docker stop $container" -Description "Stopping container $container"
    }
} else {
    Write-Host "- No running containers found" -ForegroundColor Gray
}

# Step 2: Remove all related containers
Write-Host "`n=== Step 2: Removing containers ===" -ForegroundColor Magenta
$allContainers = docker ps -aq -f name=$ImageNameFilter 2>$null
if ($allContainers) {
    foreach ($container in $allContainers) {
        Invoke-DockerCleanup -Command "docker rm $container" -Description "Removing container $container"
    }
} else {
    Write-Host "- No containers found" -ForegroundColor Gray
}

# Step 3: Clean up unused images
Write-Host "`n=== Step 3: Cleaning unused images ===" -ForegroundColor Magenta
Invoke-DockerCleanup -Command "docker image prune -f" -Description "Removing unused images" -FailureMessage "No unused images to remove"

# Step 4: Display current status
Write-Host "`n=== Cleanup Summary ===" -ForegroundColor Green
Write-Host "Current containers:" -ForegroundColor Cyan
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Image}}" 2>$null

Write-Host "`nCurrent images:" -ForegroundColor Cyan
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" 2>$null

Write-Host "`nDocker cleanup completed!" -ForegroundColor Green
