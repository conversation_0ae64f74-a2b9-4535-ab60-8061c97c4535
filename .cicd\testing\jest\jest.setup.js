// 🚀 上海荷阁科技 - Jest 测试环境设置
// 用于配置测试环境和全局设置

import '@testing-library/jest-dom'

// ==========================================
// 全局测试配置
// ==========================================

// 设置测试超时
jest.setTimeout(30000)

// ==========================================
// 环境变量设置
// ==========================================
process.env.NODE_ENV = 'test'
process.env.NEXT_PUBLIC_SITE_URL = 'http://localhost:3000'
process.env.NEXT_PUBLIC_SITE_NAME = '上海荷阁科技有限公司'

// ==========================================
// 全局模拟
// ==========================================

// 模拟 Next.js 路由
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      push: jest.fn(),
      pop: jest.fn(),
      reload: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn().mockResolvedValue(undefined),
      beforePopState: jest.fn(),
      events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
      },
      isFallback: false,
      isLocaleDomain: true,
      isReady: true,
      defaultLocale: 'zh-CN',
      domainLocales: [],
      isPreview: false,
    }
  },
}))

// 模拟 Next.js 导航
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return '/'
  },
}))

// 模拟 Next.js Image 组件
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} />
  },
}))

// 模拟 Next.js Link 组件
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ children, ...props }) => {
    return <a {...props}>{children}</a>
  },
}))

// ==========================================
// Web APIs 模拟
// ==========================================

// 模拟 IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
  root: null,
  rootMargin: '',
  thresholds: [],
}))

// 模拟 ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// 模拟 matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// 模拟 localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.localStorage = localStorageMock

// 模拟 sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.sessionStorage = sessionStorageMock

// 模拟 fetch
global.fetch = jest.fn()

// ==========================================
// 测试工具函数
// ==========================================

// 创建模拟的 React 组件
global.createMockComponent = (name) => {
  const MockComponent = (props) => <div data-testid={name} {...props} />
  MockComponent.displayName = name
  return MockComponent
}

// 创建模拟的 API 响应
global.createMockApiResponse = (data, status = 200) => {
  return Promise.resolve({
    ok: status >= 200 && status < 300,
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
    headers: new Headers(),
  })
}

// ==========================================
// 测试数据工厂
// ==========================================

// 创建测试用户数据
global.createTestUser = (overrides = {}) => ({
  id: '1',
  name: '测试用户',
  email: '<EMAIL>',
  role: 'user',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

// 创建测试文章数据
global.createTestPost = (overrides = {}) => ({
  id: '1',
  title: '测试文章',
  content: '这是一篇测试文章的内容',
  slug: 'test-post',
  published: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

// ==========================================
// 自定义匹配器
// ==========================================

// 检查元素是否可见
expect.extend({
  toBeVisible(received) {
    const pass = received.style.display !== 'none' && 
                 received.style.visibility !== 'hidden' &&
                 received.style.opacity !== '0'
    
    if (pass) {
      return {
        message: () => `expected element not to be visible`,
        pass: true,
      }
    } else {
      return {
        message: () => `expected element to be visible`,
        pass: false,
      }
    }
  },
})

// ==========================================
// 测试生命周期钩子
// ==========================================

// 每个测试前执行
beforeEach(() => {
  // 清除所有模拟
  jest.clearAllMocks()
  
  // 重置 localStorage
  localStorageMock.getItem.mockClear()
  localStorageMock.setItem.mockClear()
  localStorageMock.removeItem.mockClear()
  localStorageMock.clear.mockClear()
  
  // 重置 sessionStorage
  sessionStorageMock.getItem.mockClear()
  sessionStorageMock.setItem.mockClear()
  sessionStorageMock.removeItem.mockClear()
  sessionStorageMock.clear.mockClear()
  
  // 重置 fetch 模拟
  global.fetch.mockClear()
})

// 每个测试后执行
afterEach(() => {
  // 清理 DOM
  document.body.innerHTML = ''
  
  // 清理定时器
  jest.clearAllTimers()
})

// 所有测试前执行
beforeAll(() => {
  // 设置假定时器
  jest.useFakeTimers()
})

// 所有测试后执行
afterAll(() => {
  // 恢复真实定时器
  jest.useRealTimers()
})

// ==========================================
// 错误处理
// ==========================================

// 捕获未处理的 Promise 拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// 捕获未处理的异常
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
})

// ==========================================
// 控制台输出过滤
// ==========================================

// 过滤掉一些不重要的警告
const originalError = console.error
console.error = (...args) => {
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('Warning: ReactDOM.render is no longer supported') ||
     args[0].includes('Warning: validateDOMNesting'))
  ) {
    return
  }
  originalError.call(console, ...args)
}

// ==========================================
// 测试报告配置
// ==========================================

// 添加测试元数据
global.testMetadata = {
  project: '上海荷阁科技企业官网',
  version: '1.0.0',
  environment: 'test',
  timestamp: new Date().toISOString(),
}
