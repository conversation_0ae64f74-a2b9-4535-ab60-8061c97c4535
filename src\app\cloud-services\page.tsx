'use client'

import { Cloud, Shield, Zap, Users, ArrowRight, CheckCircle, ArrowUp } from 'lucide-react'
import Link from 'next/link'
import { useState, useEffect } from 'react'
import Navigation from '@/components/Navigation'
import TencentNews from '@/components/TencentNews'

export default function CloudServicesPage() {
  const [showScrollTop, setShowScrollTop] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }
  return (
    <div className="min-h-screen">
      <Navigation />

      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              云服务与代理
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              荷阁科技作为腾讯云官方代理商，为企业提供专业的云服务咨询、迁移和技术支持
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/services" className="inline-flex items-center bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors">
                查看产品
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <Link href="/contact" className="inline-flex items-center border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                了解更多
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 代理业务优势 */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              为什么选择荷阁科技
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              作为腾讯云官方代理商，我们提供超越标准服务的专业支持
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center group">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                <Shield className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-4">专属折扣</h3>
              <p className="text-gray-600">
                享受官方代理商专属优惠价格，为企业节省云服务成本
              </p>
            </div>
            
            <div className="text-center group">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                <Zap className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-4">免费迁移</h3>
              <p className="text-gray-600">
                提供专业的云迁移服务，确保业务平滑过渡到云端
              </p>
            </div>
            
            <div className="text-center group">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                <Users className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold mb-4">7x24技术支持</h3>
              <p className="text-gray-600">
                专业技术团队提供全天候技术支持和故障处理
              </p>
            </div>
            
            <div className="text-center group">
              <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-orange-200 transition-colors">
                <Cloud className="h-8 w-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold mb-4">架构设计</h3>
              <p className="text-gray-600">
                资深架构师提供云架构设计和优化方案
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 腾讯云产品专区 */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              腾讯云核心产品
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              全面覆盖计算、存储、网络、数据库等云服务需求
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                  <Cloud className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold">云服务器 CVM</h3>
              </div>
              <p className="text-gray-600 mb-4">
                弹性、安全、稳定、高性能的云端计算服务
              </p>
              <ul className="text-sm text-gray-500 space-y-2 mb-6">
                <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />多种实例规格</li>
                <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />按需付费</li>
                <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />秒级创建</li>
              </ul>
              <Link href="/contact" className="inline-flex items-center justify-center w-full border-2 border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                了解详情
              </Link>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                  <Shield className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold">对象存储 COS</h3>
              </div>
              <p className="text-gray-600 mb-4">
                安全、稳定、海量、便捷的云端存储服务
              </p>
              <ul className="text-sm text-gray-500 space-y-2 mb-6">
                <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />无限容量</li>
                <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />多重备份</li>
                <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />CDN加速</li>
              </ul>
              <Link href="/contact" className="inline-flex items-center justify-center w-full border-2 border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                了解详情
              </Link>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                  <Zap className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold">云数据库 CDB</h3>
              </div>
              <p className="text-gray-600 mb-4">
                高性能、高可用的云端数据库服务
              </p>
              <ul className="text-sm text-gray-500 space-y-2 mb-6">
                <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />自动备份</li>
                <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />读写分离</li>
                <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />监控告警</li>
              </ul>
              <Link href="/contact" className="inline-flex items-center justify-center w-full border-2 border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                了解详情
              </Link>
            </div>
          </div>

          <div className="text-center mt-12">
            <Link href="/services" className="inline-flex items-center bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors">
              查看更多产品
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* 合作流程 */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              合作流程
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              简单四步，开启您的云端之旅
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                1
              </div>
              <h3 className="text-lg font-semibold mb-2">需求分析</h3>
              <p className="text-gray-600">深入了解您的业务需求和技术要求</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                2
              </div>
              <h3 className="text-lg font-semibold mb-2">方案设计</h3>
              <p className="text-gray-600">制定专属的云服务解决方案</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                3
              </div>
              <h3 className="text-lg font-semibold mb-2">部署实施</h3>
              <p className="text-gray-600">专业团队协助部署和迁移</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                4
              </div>
              <h3 className="text-lg font-semibold mb-2">持续支持</h3>
              <p className="text-gray-600">提供长期的技术支持和优化</p>
            </div>
          </div>
        </div>
      </section>

      {/* 腾讯云最新资讯 */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              腾讯云最新资讯
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              实时获取腾讯云最新产品动态、技术资讯和行业趋势，助您把握云计算发展脉搏
            </p>
          </div>

          <TencentNews />
        </div>
      </section>

      {/* CTA区域 */}
      <section className="bg-primary text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            准备开始您的云端之旅？
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            联系我们的云服务专家，获取专属的解决方案和优惠报价
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="inline-flex items-center bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors">
              了解更多
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
            <Link href="/services" className="inline-flex items-center border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
              查看案例
            </Link>
          </div>
        </div>
      </section>



      {/* 页脚 */}
      <footer className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
            <div className="md:col-span-2">
              <h3 className="text-lg font-semibold mb-4">上海荷阁科技有限公司</h3>
              <p className="text-gray-400 mb-4">
                专注于3D引擎技术、云服务代理、智能座舱解决方案，
                为企业数字化转型提供专业技术支持。
              </p>
            </div>
            
            <div>
              <h4 className="text-lg font-semibold mb-4">产品服务</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/products" className="hover:text-white">3D HMI工具链</Link></li>
                <li><Link href="/cloud-services" className="hover:text-white">云服务代理</Link></li>
                <li><Link href="/products" className="hover:text-white">3D建模服务</Link></li>
                <li><Link href="/products" className="hover:text-white">智能座舱方案</Link></li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-lg font-semibold mb-4">解决方案</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/solutions" className="hover:text-white">智能座舱</Link></li>
                <li><Link href="/solutions" className="hover:text-white">虚拟数字人</Link></li>
                <li><Link href="/solutions" className="hover:text-white">MCU Autosar</Link></li>
                <li><Link href="/solutions" className="hover:text-white">云原生架构</Link></li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-lg font-semibold mb-4">联系我们</h4>
              <div className="space-y-2 text-gray-400">
                <p>📞 021-1234-5678</p>
                <p>✉️ <EMAIL></p>
                <p>📍 上海市浦东新区张江高科技园区</p>
              </div>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 上海荷阁科技有限公司. 保留所有权利.</p>
          </div>
        </div>
      </footer>

      {/* 回到顶部按钮 */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 z-50 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
          aria-label="回到顶部"
        >
          <ArrowUp className="w-6 h-6" />
        </button>
      )}
    </div>
  )
}
