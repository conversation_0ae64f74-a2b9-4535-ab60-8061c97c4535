# 🚀 上海荷阁科技 - 依赖安全检查配置
# 用于 OWASP Dependency Check 工具

# ==========================================
# 基础配置
# ==========================================
project:
  name: "hege-tech-web"
  version: "1.0.0"
  description: "上海荷阁科技有限公司企业官网"

# ==========================================
# 扫描配置
# ==========================================
scan:
  # 扫描路径
  paths:
    - "package.json"
    - "package-lock.json"
    - "node_modules/"

  # 排除路径
  exclude:
    - "node_modules/**/test/**"
    - "node_modules/**/tests/**"
    - "node_modules/**/*.test.js"
    - "node_modules/**/*.spec.js"
    - ".next/"
    - "out/"
    - "coverage/"

  # 文件类型
  include_extensions:
    - "js"
    - "ts"
    - "tsx"
    - "json"

# ==========================================
# 漏洞数据库配置
# ==========================================
database:
  # 自动更新漏洞数据库
  auto_update: true

  # 数据库更新间隔 (小时)
  update_interval: 24

  # 数据源
  sources:
    - "NVD" # National Vulnerability Database
    - "NPM" # NPM Security Advisories
    - "OSS Index" # Sonatype OSS Index

# ==========================================
# 报告配置
# ==========================================
reporting:
  # 报告格式
  formats:
    - "JSON"
    - "HTML"
    - "XML"
    - "CSV"

  # 输出目录
  output_directory: "./security-reports"

  # 报告文件名
  filename: "dependency-check-report"

  # 包含详细信息
  include_details: true

  # 包含 CVE 详情
  include_cve_details: true

# ==========================================
# 严重性级别配置
# ==========================================
severity:
  # 失败阈值 (CVSS 分数)
  fail_threshold: 7.0 # High severity

  # 警告阈值
  warn_threshold: 4.0 # Medium severity

  # 严重性映射
  levels:
    critical: 9.0
    high: 7.0
    medium: 4.0
    low: 0.1

# ==========================================
# 抑制规则
# ==========================================
suppressions:
  # 抑制文件路径
  file: ".cicd/security/dependency/suppressions.xml"

  # 自动生成抑制规则
  auto_generate: false

# ==========================================
# 通知配置
# ==========================================
notifications:
  # 启用通知
  enabled: true

  # 通知级别
  level: "high"

  # 通知方式
  methods:
    - "email"
    - "slack"

  # 邮件配置
  email:
    recipients:
      - "<EMAIL>"
      - "<EMAIL>"
    subject: "[安全警告] 依赖漏洞检测报告"

  # Slack 配置
  slack:
    webhook_url: "${SLACK_WEBHOOK_URL}"
    channel: "#security-alerts"

# ==========================================
# 缓存配置
# ==========================================
cache:
  # 启用缓存
  enabled: true

  # 缓存目录
  directory: ".dependency-check-cache"

  # 缓存过期时间 (小时)
  expiry: 168 # 7 days

# ==========================================
# 代理配置
# ==========================================
proxy:
  # HTTP 代理
  http: "${HTTP_PROXY}"

  # HTTPS 代理
  https: "${HTTPS_PROXY}"

  # 不使用代理的主机
  no_proxy: "localhost,127.0.0.1"

# ==========================================
# 高级配置
# ==========================================
advanced:
  # 启用实验性分析器
  experimental_analyzers: false

  # 启用退役组件检测
  retired_components: true

  # 启用 Node.js 审计
  node_audit: true

  # 启用 NPM 审计
  npm_audit: true

  # 超时设置 (分钟)
  timeout: 30

  # 最大线程数
  max_threads: 4

# ==========================================
# 集成配置
# ==========================================
integrations:
  # GitLab CI/CD
  gitlab:
    # 生成 GitLab 安全报告格式
    security_report: true

    # 报告文件名
    report_file: "dependency-scanning-report.json"

  # SonarQube
  sonarqube:
    # 启用 SonarQube 集成
    enabled: false

    # SonarQube 项目键
    project_key: "hege-tech-web"

# ==========================================
# 自定义规则
# ==========================================
custom_rules:
  # 自定义 CVE 检查
  custom_cve_checks:
    - pattern: "lodash"
      version: "<4.17.21"
      severity: "high"
      description: "已知的 lodash 原型污染漏洞"

  # 许可证检查
  license_checks:
    # 允许的许可证
    allowed:
      - "MIT"
      - "Apache-2.0"
      - "BSD-3-Clause"
      - "ISC"

    # 禁止的许可证
    forbidden:
      - "GPL-3.0"
      - "AGPL-3.0"
      - "LGPL-3.0"
