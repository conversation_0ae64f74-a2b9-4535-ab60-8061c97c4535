# 🔧 依赖安装故障排除指南

## 问题描述

在 GitLab CI/CD 流水线中遇到 npm 依赖解析错误，特别是 `ERESOLVE unable to resolve dependency tree` 错误。

## 常见错误类型

### 1. React DOM 版本冲突

```
npm ERR! ERESOLVE unable to resolve dependency tree
npm ERR! Found: react-dom@undefined
npm ERR! Could not resolve dependency:
npm ERR! peer react-dom@"^18.0.0" from @testing-library/react@14.3.1
```

### 2. 网络连接问题

- 国外镜像源访问缓慢或失败
- DNS 解析问题
- 防火墙限制

## 解决方案

### 方案1: 使用国内镜像源

```bash
npm config set registry https://registry.npmmirror.com/
npm config set @types:registry https://registry.npmmirror.com/
npm config set @testing-library:registry https://registry.npmmirror.com/
```

### 方案2: 清理缓存

```bash
npm cache clean --force
```

### 方案3: 使用 Legacy Peer Dependencies

```bash
npm ci --legacy-peer-deps --no-audit --no-fund
```

### 方案4: 强制安装

```bash
npm ci --force --no-audit --no-fund
```

### 方案5: 重新生成 package-lock.json

```bash
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps
```

## 预防措施

### 1. 项目配置

- 创建 `.npmrc` 文件配置镜像源
- 使用精确版本号而非范围版本

### 2. CI/CD 配置

- 配置多重安装策略
- 使用缓存优化安装速度
- 设置合理的超时时间

### 3. 依赖管理

- 定期更新依赖版本
- 检查 peer dependencies 兼容性
- 使用 `npm audit` 检查安全漏洞

## 自动化脚本

项目提供了自动化安装脚本：

- Linux/macOS: `.cicd/scripts/install-dependencies.sh`
- Windows: `.cicd/scripts/install-dependencies.ps1`

这些脚本会自动尝试多种安装策略，确保依赖安装成功。

## 监控和报告

- 使用 GitLab CI/CD 的 artifacts 保存安装日志
- 配置失败通知机制
- 定期检查依赖更新

## 联系支持

如果问题持续存在，请联系开发团队或查看：

- GitLab Issues
- 项目文档
- 团队知识库
