#!/bin/bash

# 🚀 上海荷阁科技 - 依赖验证脚本
# 验证项目依赖是否正确安装和配置

set -e

echo "🔍 开始验证项目依赖..."

# 检查 Node.js 和 npm 版本
echo "📋 检查环境版本..."
echo "Node.js 版本: $(node --version)"
echo "npm 版本: $(npm --version)"

# 检查 npm 配置
echo "🔧 检查 npm 配置..."
echo "Registry: $(npm config get registry)"
echo "@types Registry: $(npm config get @types:registry)"
echo "@testing-library Registry: $(npm config get @testing-library:registry)"

# 检查关键依赖
echo "📦 检查关键依赖..."
if [ -d "node_modules" ]; then
    echo "✅ node_modules 目录存在"
    
    # 检查 React 相关包
    if [ -d "node_modules/react" ]; then
        echo "✅ React 已安装: $(cat node_modules/react/package.json | grep '"version"' | head -1)"
    else
        echo "❌ React 未安装"
    fi
    
    if [ -d "node_modules/react-dom" ]; then
        echo "✅ React DOM 已安装: $(cat node_modules/react-dom/package.json | grep '"version"' | head -1)"
    else
        echo "❌ React DOM 未安装"
    fi
    
    # 检查测试库
    if [ -d "node_modules/@testing-library/react" ]; then
        echo "✅ Testing Library React 已安装: $(cat node_modules/@testing-library/react/package.json | grep '"version"' | head -1)"
    else
        echo "❌ Testing Library React 未安装"
    fi
    
    # 检查 Next.js
    if [ -d "node_modules/next" ]; then
        echo "✅ Next.js 已安装: $(cat node_modules/next/package.json | grep '"version"' | head -1)"
    else
        echo "❌ Next.js 未安装"
    fi
else
    echo "❌ node_modules 目录不存在"
fi

# 检查 package-lock.json
if [ -f "package-lock.json" ]; then
    echo "✅ package-lock.json 存在"
else
    echo "❌ package-lock.json 不存在"
fi

# 尝试运行基本命令
echo "🧪 测试基本命令..."
if npm run lint --dry-run > /dev/null 2>&1; then
    echo "✅ lint 命令可用"
else
    echo "❌ lint 命令不可用"
fi

if npm run build --dry-run > /dev/null 2>&1; then
    echo "✅ build 命令可用"
else
    echo "❌ build 命令不可用"
fi

if npm run test --dry-run > /dev/null 2>&1; then
    echo "✅ test 命令可用"
else
    echo "❌ test 命令不可用"
fi

echo "✅ 依赖验证完成"
