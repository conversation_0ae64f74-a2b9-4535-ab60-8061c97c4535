#!/bin/bash
# 🚀 上海荷阁科技 - 测试环境部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
DEPLOY_DIR="/opt/hege-tech-web"
BACKUP_DIR="/opt/backups/hege-tech-web"
DOCKER_COMPOSE_FILE="$PROJECT_ROOT/.cicd/docker/docker-compose.staging.yml"

# 环境变量
ENVIRONMENT="staging"
DOCKER_REGISTRY="${DOCKER_REGISTRY:-registry.gitlab.com/hege-tech/hege-tech-web}"
IMAGE_TAG="${CI_COMMIT_SHA:-latest}"
STAGING_HOST="${STAGING_HOST:-staging.hege-tech.cn}"

# 帮助信息
show_help() {
    cat << EOF
🚀 上海荷阁科技测试环境部署脚本

用法: $0 [选项]

选项:
    -t, --tag TAG              Docker 镜像标签 (默认: $IMAGE_TAG)
    -h, --host HOST            目标主机 (默认: $STAGING_HOST)
    -b, --backup               部署前创建备份
    -r, --rollback             回滚到上一个版本
    -c, --check                仅检查部署状态
    --help                     显示此帮助信息

示例:
    $0 -t v1.0.0 -b                    # 部署指定版本并创建备份
    $0 -r                              # 回滚到上一个版本
    $0 -c                              # 检查当前部署状态

EOF
}

# 解析命令行参数
BACKUP=false
ROLLBACK=false
CHECK_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -h|--host)
            STAGING_HOST="$2"
            shift 2
            ;;
        -b|--backup)
            BACKUP=true
            shift
            ;;
        -r|--rollback)
            ROLLBACK=true
            shift
            ;;
        -c|--check)
            CHECK_ONLY=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

log_info "开始测试环境部署..."
log_info "环境: $ENVIRONMENT"
log_info "主机: $STAGING_HOST"
log_info "镜像标签: $IMAGE_TAG"
log_info "部署目录: $DEPLOY_DIR"

# 检查部署状态
check_deployment_status() {
    log_info "检查部署状态..."
    
    if docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "Up"; then
        log_success "✅ 应用正在运行"
        
        # 检查健康状态
        if curl -f -s "http://$STAGING_HOST/api/health" >/dev/null; then
            log_success "✅ 健康检查通过"
        else
            log_warning "⚠️ 健康检查失败"
        fi
        
        # 显示容器状态
        log_info "容器状态:"
        docker-compose -f "$DOCKER_COMPOSE_FILE" ps
        
    else
        log_warning "⚠️ 应用未运行"
    fi
}

# 如果只是检查状态，直接返回
if [[ "$CHECK_ONLY" == "true" ]]; then
    check_deployment_status
    exit 0
fi

# 创建备份
create_backup() {
    if [[ "$BACKUP" == "true" ]]; then
        log_info "创建部署备份..."
        
        BACKUP_NAME="staging-backup-$(date +%Y%m%d-%H%M%S)"
        BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"
        
        mkdir -p "$BACKUP_DIR"
        
        # 备份当前部署
        if [[ -d "$DEPLOY_DIR" ]]; then
            cp -r "$DEPLOY_DIR" "$BACKUP_PATH"
            log_success "✅ 备份创建完成: $BACKUP_PATH"
        else
            log_warning "⚠️ 部署目录不存在，跳过备份"
        fi
        
        # 清理旧备份 (保留最近10个)
        find "$BACKUP_DIR" -maxdepth 1 -type d -name "staging-backup-*" | sort -r | tail -n +11 | xargs rm -rf
    fi
}

# 回滚部署
rollback_deployment() {
    log_info "开始回滚部署..."
    
    # 查找最新的备份
    LATEST_BACKUP=$(find "$BACKUP_DIR" -maxdepth 1 -type d -name "staging-backup-*" | sort -r | head -n 1)
    
    if [[ -z "$LATEST_BACKUP" ]]; then
        log_error "❌ 未找到可用的备份"
        exit 1
    fi
    
    log_info "回滚到备份: $LATEST_BACKUP"
    
    # 停止当前服务
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # 恢复备份
    rm -rf "$DEPLOY_DIR"
    cp -r "$LATEST_BACKUP" "$DEPLOY_DIR"
    
    # 启动服务
    cd "$DEPLOY_DIR"
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    log_success "✅ 回滚完成"
}

# 如果是回滚操作
if [[ "$ROLLBACK" == "true" ]]; then
    rollback_deployment
    check_deployment_status
    exit 0
fi

# 预部署检查
pre_deployment_checks() {
    log_info "执行预部署检查..."
    
    # 检查 Docker
    if ! command -v docker >/dev/null 2>&1; then
        log_error "❌ Docker 未安装"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose >/dev/null 2>&1; then
        log_error "❌ Docker Compose 未安装"
        exit 1
    fi
    
    # 检查镜像是否存在
    if ! docker pull "$DOCKER_REGISTRY:$IMAGE_TAG"; then
        log_error "❌ 无法拉取镜像: $DOCKER_REGISTRY:$IMAGE_TAG"
        exit 1
    fi
    
    log_success "✅ 预部署检查通过"
}

# 部署应用
deploy_application() {
    log_info "开始部署应用..."
    
    # 创建部署目录
    mkdir -p "$DEPLOY_DIR"
    cd "$DEPLOY_DIR"
    
    # 复制配置文件
    cp -r "$PROJECT_ROOT/.cicd" ./
    cp "$PROJECT_ROOT/package.json" ./
    cp "$PROJECT_ROOT/next.config.js" ./
    
    # 设置环境变量
    export DOCKER_IMAGE="$DOCKER_REGISTRY"
    export IMAGE_TAG="$IMAGE_TAG"
    export ENVIRONMENT="$ENVIRONMENT"
    
    # 停止旧服务
    if [[ -f "$DOCKER_COMPOSE_FILE" ]]; then
        log_info "停止旧服务..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" down
    fi
    
    # 启动新服务
    log_info "启动新服务..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    if docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "Up"; then
        log_success "✅ 服务启动成功"
    else
        log_error "❌ 服务启动失败"
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs
        exit 1
    fi
}

# 部署后验证
post_deployment_verification() {
    log_info "执行部署后验证..."
    
    # 健康检查
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "健康检查 (第 $attempt 次)..."
        
        if curl -f -s "http://$STAGING_HOST/api/health" >/dev/null; then
            log_success "✅ 健康检查通过"
            break
        else
            if [[ $attempt -eq $max_attempts ]]; then
                log_error "❌ 健康检查失败"
                exit 1
            fi
            log_warning "⚠️ 健康检查失败，10秒后重试..."
            sleep 10
            ((attempt++))
        fi
    done
    
    # 功能测试
    log_info "执行基本功能测试..."
    
    # 测试主页
    if curl -f -s "http://$STAGING_HOST/" >/dev/null; then
        log_success "✅ 主页访问正常"
    else
        log_error "❌ 主页访问失败"
        exit 1
    fi
    
    # 测试 API
    if curl -f -s "http://$STAGING_HOST/api/tencent-news" >/dev/null; then
        log_success "✅ API 访问正常"
    else
        log_warning "⚠️ API 访问异常"
    fi
}

# 清理资源
cleanup_resources() {
    log_info "清理旧资源..."
    
    # 清理旧镜像
    docker image prune -f
    
    # 清理旧容器
    docker container prune -f
    
    log_success "✅ 资源清理完成"
}

# 主部署流程
main() {
    # 创建备份
    create_backup
    
    # 预部署检查
    pre_deployment_checks
    
    # 部署应用
    deploy_application
    
    # 部署后验证
    post_deployment_verification
    
    # 清理资源
    cleanup_resources
    
    # 最终状态检查
    check_deployment_status
    
    log_success "🎉 测试环境部署完成！"
    log_info "访问地址: http://$STAGING_HOST"
    log_info "部署时间: $(date)"
    log_info "镜像版本: $DOCKER_REGISTRY:$IMAGE_TAG"
}

# 错误处理
trap 'log_error "❌ 部署过程中发生错误"; exit 1' ERR

# 执行主流程
main
