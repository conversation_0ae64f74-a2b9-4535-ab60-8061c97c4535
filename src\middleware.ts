/**
 * Next.js中间件 - 网络安全防护
 * 处理请求级别的安全检查
 */

import { NextRequest, NextResponse } from "next/server";
import {
  getClientIP,
  checkRateLimit,
  isIPBlacklisted,
  logSecurityEvent,
} from "./lib/security";

// 需要保护的API路径
const protectedPaths = [
  "/api/contact",
  "/api/visitor-stats",
  "/api/admin",
  "/api/upload",
];

// 静态资源路径
const staticPaths = [
  "/_next",
  "/images",
  "/icons",
  "/favicon.ico",
  "/robots.txt",
  "/sitemap.xml",
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const ip = getClientIP(request);
  const userAgent = request.headers.get("user-agent") || "unknown";

  // 跳过静态资源
  if (staticPaths.some((path) => pathname.startsWith(path))) {
    return NextResponse.next();
  }

  // 检查IP黑名单
  if (isIPBlacklisted(ip)) {
    logSecurityEvent({
      type: "suspicious_activity",
      ip,
      userAgent,
      details: { reason: "blacklisted_ip", path: pathname },
    });

    return new NextResponse("Forbidden", { status: 403 });
  }

  // 检查可疑的User-Agent
  if (isSuspiciousUserAgent(userAgent)) {
    logSecurityEvent({
      type: "suspicious_activity",
      ip,
      userAgent,
      details: { reason: "suspicious_user_agent", path: pathname },
    });

    // 对可疑请求进行速率限制
    const rateLimit = checkRateLimit(`suspicious_${ip}`, 10, 60000);
    if (!rateLimit.allowed) {
      return new NextResponse("Too Many Requests", { status: 429 });
    }
  }

  // API路径保护
  if (protectedPaths.some((path) => pathname.startsWith(path))) {
    return handleProtectedAPI(request, ip, userAgent);
  }

  // 检查恶意路径访问
  if (isMaliciousPath(pathname)) {
    logSecurityEvent({
      type: "suspicious_activity",
      ip,
      userAgent,
      details: { reason: "malicious_path", path: pathname },
    });

    return new NextResponse("Not Found", { status: 404 });
  }

  // 添加安全头部
  const response = NextResponse.next();
  addSecurityHeaders(response);

  return response;
}

/**
 * 处理受保护的API请求
 */
function handleProtectedAPI(
  request: NextRequest,
  ip: string,
  userAgent: string,
) {
  const { pathname } = request.nextUrl;

  // 速率限制检查
  const rateLimit = checkRateLimit(ip, 100, 60000); // 每分钟100次请求

  if (!rateLimit.allowed) {
    logSecurityEvent({
      type: "rate_limit_exceeded",
      ip,
      userAgent,
      details: { path: pathname, limit: 100 },
    });

    return new NextResponse(
      JSON.stringify({ error: "请求过于频繁，请稍后再试" }),
      {
        status: 429,
        headers: {
          "Content-Type": "application/json",
          "X-RateLimit-Limit": "100",
          "X-RateLimit-Remaining": rateLimit.remaining.toString(),
          "X-RateLimit-Reset": new Date(rateLimit.resetTime).toISOString(),
        },
      },
    );
  }

  // CSRF保护 (对于POST、PUT、DELETE请求)
  if (["POST", "PUT", "DELETE"].includes(request.method)) {
    const contentType = request.headers.get("content-type");
    const origin = request.headers.get("origin");
    const referer = request.headers.get("referer");

    // 检查Content-Type
    if (!contentType || !contentType.includes("application/json")) {
      if (!contentType?.includes("multipart/form-data")) {
        return new NextResponse("Invalid Content-Type", { status: 400 });
      }
    }

    // 检查Origin和Referer
    if (origin && !isValidOrigin(origin)) {
      logSecurityEvent({
        type: "suspicious_activity",
        ip,
        userAgent,
        details: { reason: "invalid_origin", origin, path: pathname },
      });

      return new NextResponse("Invalid Origin", { status: 403 });
    }
  }

  // 继续处理请求
  const response = NextResponse.next();

  // 添加速率限制头部
  response.headers.set("X-RateLimit-Limit", "100");
  response.headers.set("X-RateLimit-Remaining", rateLimit.remaining.toString());
  response.headers.set(
    "X-RateLimit-Reset",
    new Date(rateLimit.resetTime).toISOString(),
  );

  return response;
}

/**
 * 添加安全头部
 */
function addSecurityHeaders(response: NextResponse) {
  // 防止点击劫持
  response.headers.set("X-Frame-Options", "DENY");

  // 防止MIME类型嗅探
  response.headers.set("X-Content-Type-Options", "nosniff");

  // XSS防护
  response.headers.set("X-XSS-Protection", "1; mode=block");

  // 引用策略
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

  // 禁用DNS预取
  response.headers.set("X-DNS-Prefetch-Control", "off");

  // 权限策略
  response.headers.set(
    "Permissions-Policy",
    "camera=(), microphone=(), geolocation=(), payment=(), usb=()",
  );
}

/**
 * 检查是否为可疑的User-Agent
 */
function isSuspiciousUserAgent(userAgent: string): boolean {
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /java/i,
    /go-http/i,
    /scanner/i,
    /exploit/i,
  ];

  // 允许的搜索引擎爬虫
  const allowedBots = [/googlebot/i, /bingbot/i, /baiduspider/i, /yandexbot/i];

  // 检查是否为允许的爬虫
  if (allowedBots.some((pattern) => pattern.test(userAgent))) {
    return false;
  }

  // 检查是否为可疑模式
  return suspiciousPatterns.some((pattern) => pattern.test(userAgent));
}

/**
 * 检查是否为恶意路径
 */
function isMaliciousPath(pathname: string): boolean {
  const maliciousPatterns = [
    // 常见的攻击路径
    /\/\.env/,
    /\/\.git/,
    /\/admin/,
    /\/wp-admin/,
    /\/wp-login/,
    /\/phpmyadmin/,
    /\/config/,
    /\/backup/,
    /\/database/,
    /\/sql/,
    /\/shell/,
    /\/cmd/,
    /\/eval/,
    /\/exec/,
    // SQL注入尝试
    /union.*select/i,
    /select.*from/i,
    /insert.*into/i,
    /delete.*from/i,
    /drop.*table/i,
    // XSS尝试
    /<script/i,
    /javascript:/i,
    /on\w+=/i,
    // 路径遍历
    /\.\.\//,
    /\.\.%2f/i,
    /\.\.%5c/i,
  ];

  return maliciousPatterns.some((pattern) => pattern.test(pathname));
}

/**
 * 验证Origin是否有效
 */
function isValidOrigin(origin: string): boolean {
  const allowedOrigins = [
    "http://localhost:3000",
    "http://localhost:3001",
    "http://*************:3000",
    "https://hege-tech.cn",
    "https://www.hege-tech.cn",
  ];

  return allowedOrigins.includes(origin);
}

// 配置中间件匹配路径
export const config = {
  matcher: [
    /*
     * 匹配所有请求路径，除了:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
};
