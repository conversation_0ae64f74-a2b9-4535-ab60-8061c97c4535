"use client";

import {
  Users,
  Target,
  Award,
  Building,
  MapPin,
  ChevronRight,
  ArrowUp,
} from "lucide-react";
import Link from "next/link";
import { useState, useEffect } from "react";
import Navigation from "@/components/Navigation";
import Timeline from "@/components/Timeline";

export default function AboutPage() {
  const [activeSection, setActiveSection] = useState("company-intro");
  const [showScrollTop, setShowScrollTop] = useState(false);

  const menuItems = [
    { id: "company-intro", label: "公司简介", icon: "🏢" },
    { id: "vision-mission", label: "愿景及使命", icon: "🎯" },
    { id: "development-history", label: "发展历程", icon: "📈" },
    { id: "business-structure", label: "业务架构", icon: "🏗️" },
    { id: "corporate-culture", label: "企业文化", icon: "🌟" },
    { id: "office-location", label: "办公地点", icon: "📍" },
  ];

  useEffect(() => {
    const handleScroll = () => {
      const sections = menuItems.map((item) =>
        document.getElementById(item.id),
      );
      const scrollPosition = window.scrollY + 200;

      for (let i = sections.length - 1; i >= 0; i--) {
        const section = sections[i];
        if (section && section.offsetTop <= scrollPosition) {
          setActiveSection(menuItems[i].id);
          break;
        }
      }

      // 控制回到顶部按钮的显示
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 100;
      window.scrollTo({
        top: offsetTop,
        behavior: "smooth",
      });
      setActiveSection(sectionId);
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <div className="min-h-screen">
      <Navigation />

      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              关于荷阁科技
            </h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              专注智能汽车、数字化创新和人工智能领域的高新科技企业
            </p>
          </div>
        </div>
      </section>

      {/* 水平标签页导航 */}
      <div className="bg-white border-b border-gray-200 sticky top-16 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8 overflow-x-auto">
            {menuItems.map((item) => (
              <button
                key={item.id}
                onClick={() => scrollToSection(item.id)}
                className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                  activeSection === item.id
                    ? "border-blue-600 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                {item.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="space-y-16">
          {/* 公司简介 */}
          <section id="company-intro" className="scroll-mt-24">
            <div className="bg-white rounded-lg shadow-sm border p-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 flex items-center">
                <span className="text-2xl mr-3">🏢</span>
                公司简介
              </h2>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">
                    上海荷阁科技有限公司
                  </h3>
                  <div className="space-y-4 text-gray-600">
                    <p className="text-lg leading-relaxed">
                      上海荷阁科技有限公司（简称"荷阁科技"）成立于2021年，总部位于上海，
                      是一家专注于智能汽车、数字化创新和人工智能领域的高新科技企业。
                    </p>
                    <p className="text-lg leading-relaxed">
                      我们专注于智能座舱和辅助驾驶技术，提供全面的3D
                      HMI解决方案。
                      荷阁科技核心技术团队在汽车座舱领域拥有丰富的量产开发经验，
                      能够为汽车制造商提供高度定制化的智能座舱解决方案。
                    </p>
                    <p className="text-lg leading-relaxed">
                      作为腾讯云核心代理商，我们还为企业提供全方位的云服务解决方案，
                      助力企业数字化转型和智能化升级。
                    </p>
                  </div>
                </div>

                <div className="relative">
                  <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8">
                    <div className="grid grid-cols-2 gap-6">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-600 mb-2">
                          2021
                        </div>
                        <div className="text-sm text-gray-600">成立年份</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-purple-600 mb-2">
                          50+
                        </div>
                        <div className="text-sm text-gray-600">团队成员</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-green-600 mb-2">
                          100+
                        </div>
                        <div className="text-sm text-gray-600">服务客户</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-orange-600 mb-2">
                          3
                        </div>
                        <div className="text-sm text-gray-600">核心业务</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* 愿景及使命 */}
          <section id="vision-mission" className="scroll-mt-24">
            <div className="bg-white rounded-lg shadow-sm border p-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 flex items-center">
                <span className="text-2xl mr-3">🎯</span>
                愿景及使命
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6">
                  <div className="flex items-center mb-4">
                    <Target className="w-8 h-8 text-blue-600 mr-3" />
                    <h3 className="text-xl font-bold text-gray-900">
                      企业愿景
                    </h3>
                  </div>
                  <p className="text-gray-700 leading-relaxed">
                    成为智能汽车和数字化创新领域的领军企业，
                    通过前沿技术推动行业变革，创造更智能、更安全、更便捷的出行体验。
                  </p>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6">
                  <div className="flex items-center mb-4">
                    <Award className="w-8 h-8 text-purple-600 mr-3" />
                    <h3 className="text-xl font-bold text-gray-900">
                      企业使命
                    </h3>
                  </div>
                  <p className="text-gray-700 leading-relaxed">
                    致力于为客户提供卓越的智能座舱解决方案和云服务，
                    以技术创新驱动产业升级，为构建智能化未来贡献力量。
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* 发展历程 */}
          <section id="development-history" className="scroll-mt-24">
            <div className="bg-white rounded-lg shadow-sm border p-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 flex items-center">
                <span className="text-2xl mr-3">📈</span>
                发展历程
              </h2>
              <Timeline />
            </div>
          </section>

          {/* 业务架构 */}
          <section id="business-structure" className="scroll-mt-24">
            <div className="bg-white rounded-lg shadow-sm border p-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 flex items-center">
                <span className="text-2xl mr-3">🏗️</span>
                业务架构
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                    <span className="text-3xl">🚗</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    3D HMI解决方案
                  </h3>
                  <p className="text-gray-600">
                    智能座舱、3D交互界面、一镜到底体验、高性能渲染引擎
                  </p>
                </div>

                <div className="text-center">
                  <div className="bg-purple-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                    <span className="text-3xl">☁️</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    云服务代理
                  </h3>
                  <p className="text-gray-600">
                    腾讯云全线产品代理、技术支持、优惠政策、7x24小时服务
                  </p>
                </div>

                <div className="text-center">
                  <div className="bg-green-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                    <span className="text-3xl">🤝</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    技术咨询
                  </h3>
                  <p className="text-gray-600">
                    项目咨询、技术支持、解决方案设计、实施指导
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* 企业文化 */}
          <section id="corporate-culture" className="scroll-mt-24">
            <div className="bg-white rounded-lg shadow-sm border p-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 flex items-center">
                <span className="text-2xl mr-3">🌟</span>
                企业文化
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                  <div className="text-4xl mb-3">💡</div>
                  <h3 className="font-bold text-gray-900 mb-2">创新驱动</h3>
                  <p className="text-sm text-gray-600">
                    持续技术创新，引领行业发展
                  </p>
                </div>

                <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                  <div className="text-4xl mb-3">🎯</div>
                  <h3 className="font-bold text-gray-900 mb-2">客户至上</h3>
                  <p className="text-sm text-gray-600">
                    以客户需求为导向，提供优质服务
                  </p>
                </div>

                <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                  <div className="text-4xl mb-3">🤝</div>
                  <h3 className="font-bold text-gray-900 mb-2">团队协作</h3>
                  <p className="text-sm text-gray-600">
                    凝聚团队力量，共创美好未来
                  </p>
                </div>

                <div className="text-center p-6 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg">
                  <div className="text-4xl mb-3">⚡</div>
                  <h3 className="font-bold text-gray-900 mb-2">高效执行</h3>
                  <p className="text-sm text-gray-600">
                    快速响应，高效执行，追求卓越
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* 办公地点 */}
          <section id="office-location" className="scroll-mt-24">
            <div className="bg-white rounded-lg shadow-sm border p-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 flex items-center">
                <span className="text-2xl mr-3">📍</span>
                办公地点
              </h2>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    总部地址
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <MapPin className="w-5 h-5 text-blue-600 mt-1" />
                      <div>
                        <p className="font-medium text-gray-900">
                          上海荷阁科技有限公司
                        </p>
                        <p className="text-gray-600">
                          上海市青浦区崧华路950号1号楼310
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <span className="text-blue-600">📞</span>
                      <span className="text-gray-900">18221165813</span>
                    </div>

                    <div className="flex items-center space-x-3">
                      <span className="text-blue-600">✉️</span>
                      <span className="text-gray-900">
                        <EMAIL>
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-100 rounded-lg h-64 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <Building className="w-16 h-16 mx-auto mb-4" />
                    <p>地图位置</p>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>

      {/* 回到顶部按钮 */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 z-50 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
          aria-label="回到顶部"
        >
          <ArrowUp className="w-6 h-6" />
        </button>
      )}
    </div>
  );
}
