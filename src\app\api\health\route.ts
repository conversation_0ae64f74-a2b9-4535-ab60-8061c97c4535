// 🚀 上海荷阁科技 - 健康检查 API
// 用于 CI/CD 流水线和监控系统的健康状态检查

import { NextRequest, NextResponse } from 'next/server'

// 健康检查响应接口
interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded'
  timestamp: string
  version: string
  environment: string
  uptime: number
  checks: {
    database?: {
      status: 'up' | 'down'
      responseTime?: number
      error?: string
    }
    redis?: {
      status: 'up' | 'down'
      responseTime?: number
      error?: string
    }
    external?: {
      status: 'up' | 'down'
      responseTime?: number
      error?: string
    }
  }
  system: {
    memory: {
      used: number
      total: number
      percentage: number
    }
    cpu: {
      usage: number
    }
    disk: {
      used: number
      total: number
      percentage: number
    }
  }
}

// 获取系统信息
function getSystemInfo() {
  const memoryUsage = process.memoryUsage()
  
  return {
    memory: {
      used: memoryUsage.heapUsed,
      total: memoryUsage.heapTotal,
      percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)
    },
    cpu: {
      usage: Math.round(process.cpuUsage().user / 1000000) // 转换为毫秒
    },
    disk: {
      used: 0, // 在生产环境中可以使用 fs.statSync 获取
      total: 0,
      percentage: 0
    }
  }
}

// 检查数据库连接
async function checkDatabase(): Promise<{ status: 'up' | 'down'; responseTime?: number; error?: string }> {
  try {
    const startTime = Date.now()
    
    // 这里应该实际连接数据库进行检查
    // 例如：await prisma.$queryRaw`SELECT 1`
    // 目前返回模拟结果
    
    const responseTime = Date.now() - startTime
    
    return {
      status: 'up',
      responseTime
    }
  } catch (error) {
    return {
      status: 'down',
      error: error instanceof Error ? error.message : 'Unknown database error'
    }
  }
}

// 检查 Redis 连接
async function checkRedis(): Promise<{ status: 'up' | 'down'; responseTime?: number; error?: string }> {
  try {
    const startTime = Date.now()
    
    // 这里应该实际连接 Redis 进行检查
    // 例如：await redis.ping()
    // 目前返回模拟结果
    
    const responseTime = Date.now() - startTime
    
    return {
      status: 'up',
      responseTime
    }
  } catch (error) {
    return {
      status: 'down',
      error: error instanceof Error ? error.message : 'Unknown Redis error'
    }
  }
}

// 检查外部服务
async function checkExternalServices(): Promise<{ status: 'up' | 'down'; responseTime?: number; error?: string }> {
  try {
    const startTime = Date.now()
    
    // 检查关键外部服务
    // 例如：第三方 API、CDN 等
    
    const responseTime = Date.now() - startTime
    
    return {
      status: 'up',
      responseTime
    }
  } catch (error) {
    return {
      status: 'down',
      error: error instanceof Error ? error.message : 'External service error'
    }
  }
}

// 确定整体健康状态
function determineOverallStatus(checks: HealthCheckResponse['checks']): 'healthy' | 'unhealthy' | 'degraded' {
  const statuses = Object.values(checks).map(check => check?.status)
  
  if (statuses.every(status => status === 'up')) {
    return 'healthy'
  } else if (statuses.every(status => status === 'down')) {
    return 'unhealthy'
  } else {
    return 'degraded'
  }
}

// GET 请求处理器
export async function GET(request: NextRequest) {
  try {
    const startTime = Date.now()
    
    // 获取查询参数
    const { searchParams } = new URL(request.url)
    const detailed = searchParams.get('detailed') === 'true'
    const includeSystem = searchParams.get('system') === 'true'
    
    // 基础健康检查响应
    const baseResponse = {
      status: 'healthy' as const,
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime()
    }
    
    // 如果不需要详细信息，返回简单响应
    if (!detailed) {
      return NextResponse.json(baseResponse, { status: 200 })
    }
    
    // 执行详细健康检查
    const checks: HealthCheckResponse['checks'] = {}
    
    // 并行执行所有检查
    const [databaseCheck, redisCheck, externalCheck] = await Promise.allSettled([
      checkDatabase(),
      checkRedis(),
      checkExternalServices()
    ])
    
    // 处理数据库检查结果
    if (databaseCheck.status === 'fulfilled') {
      checks.database = databaseCheck.value
    } else {
      checks.database = { status: 'down', error: 'Database check failed' }
    }
    
    // 处理 Redis 检查结果
    if (redisCheck.status === 'fulfilled') {
      checks.redis = redisCheck.value
    } else {
      checks.redis = { status: 'down', error: 'Redis check failed' }
    }
    
    // 处理外部服务检查结果
    if (externalCheck.status === 'fulfilled') {
      checks.external = externalCheck.value
    } else {
      checks.external = { status: 'down', error: 'External service check failed' }
    }
    
    // 构建完整响应
    const response: HealthCheckResponse = {
      ...baseResponse,
      status: determineOverallStatus(checks),
      checks,
      system: includeSystem ? getSystemInfo() : {
        memory: { used: 0, total: 0, percentage: 0 },
        cpu: { usage: 0 },
        disk: { used: 0, total: 0, percentage: 0 }
      }
    }
    
    // 根据健康状态确定 HTTP 状态码
    const httpStatus = response.status === 'healthy' ? 200 : 
                      response.status === 'degraded' ? 200 : 503
    
    // 添加响应头
    const headers = {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      'X-Health-Check-Duration': `${Date.now() - startTime}ms`
    }
    
    return NextResponse.json(response, { 
      status: httpStatus,
      headers
    })
    
  } catch (error) {
    // 处理未预期的错误
    const errorResponse = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      checks: {}
    }
    
    return NextResponse.json(errorResponse, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  }
}

// HEAD 请求处理器（用于简单的存活检查）
export async function HEAD() {
  return new NextResponse(null, { 
    status: 200,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  })
}
