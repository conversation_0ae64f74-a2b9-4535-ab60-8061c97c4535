#!/bin/bash
# 🚀 上海荷阁科技 - 手动镜像拉取脚本
# 当自动配置失败时的备用方案

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 镜像列表和备用源
declare -A IMAGES_MAP=(
    ["postgres:15-alpine"]="postgres:15-alpine"
    ["redis:7-alpine"]="redis:7-alpine"
    ["nginx:1.25-alpine"]="nginx:1.25-alpine"
    ["node:18-alpine"]="node:18-alpine"
)

# 国内镜像源列表
MIRROR_REGISTRIES=(
    "docker.mirrors.ustc.edu.cn"
    "hub-mirror.c.163.com"
    "mirror.baidubce.com"
    "dockerproxy.com"
)

# 尝试从不同镜像源拉取镜像
pull_with_mirrors() {
    local image=$1
    local success=false
    
    log_info "尝试拉取镜像: $image"
    
    # 首先尝试直接拉取
    if docker pull "$image" >/dev/null 2>&1; then
        log_success "✅ 直接拉取成功: $image"
        return 0
    fi
    
    # 如果直接拉取失败，尝试使用镜像源
    for registry in "${MIRROR_REGISTRIES[@]}"; do
        log_info "尝试镜像源: $registry"
        
        # 临时设置 Docker 配置
        local temp_config=$(mktemp -d)
        cat > "$temp_config/config.json" << EOF
{
  "auths": {},
  "registry-mirrors": ["https://$registry"]
}
EOF
        
        # 使用临时配置拉取镜像
        if DOCKER_CONFIG="$temp_config" docker pull "$image" >/dev/null 2>&1; then
            log_success "✅ 通过镜像源拉取成功: $image (via $registry)"
            rm -rf "$temp_config"
            return 0
        fi
        
        rm -rf "$temp_config"
    done
    
    log_error "❌ 所有镜像源都无法拉取: $image"
    return 1
}

# 使用 Docker Hub 代理服务
pull_with_proxy() {
    local image=$1
    
    log_info "尝试使用代理服务拉取: $image"
    
    # 使用 dockerproxy.com 代理
    local proxy_image="dockerproxy.com/library/$image"
    
    if docker pull "$proxy_image" >/dev/null 2>&1; then
        # 重新标记为原始镜像名
        docker tag "$proxy_image" "$image"
        docker rmi "$proxy_image" >/dev/null 2>&1
        log_success "✅ 代理拉取成功: $image"
        return 0
    fi
    
    return 1
}

# 主拉取函数
pull_image() {
    local image=$1
    
    # 检查镜像是否已存在
    if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$image$"; then
        log_info "镜像已存在，跳过: $image"
        return 0
    fi
    
    # 尝试不同的拉取方法
    if pull_with_mirrors "$image"; then
        return 0
    elif pull_with_proxy "$image"; then
        return 0
    else
        log_error "❌ 无法拉取镜像: $image"
        return 1
    fi
}

# 拉取所有必需镜像
pull_all_images() {
    local failed_images=()
    
    log_info "开始手动拉取所有必需镜像..."
    echo ""
    
    for image in "${!IMAGES_MAP[@]}"; do
        if ! pull_image "$image"; then
            failed_images+=("$image")
        fi
        echo ""
    done
    
    # 报告结果
    if [ ${#failed_images[@]} -eq 0 ]; then
        log_success "🎉 所有镜像拉取成功！"
        return 0
    else
        log_warning "部分镜像拉取失败:"
        for img in "${failed_images[@]}"; do
            echo "  - $img"
        done
        return 1
    fi
}

# 显示镜像信息
show_images() {
    log_info "当前已拉取的镜像:"
    echo ""
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | head -10
}

# 清理函数
cleanup() {
    log_info "清理临时文件和悬空镜像..."
    docker image prune -f >/dev/null 2>&1
    log_success "✅ 清理完成"
}

# 测试连接
test_connection() {
    log_info "测试网络连接..."
    
    local test_urls=(
        "https://registry-1.docker.io"
        "https://docker.mirrors.ustc.edu.cn"
        "https://hub-mirror.c.163.com"
        "https://dockerproxy.com"
    )
    
    for url in "${test_urls[@]}"; do
        if curl -s --connect-timeout 5 "$url" >/dev/null 2>&1; then
            log_success "✅ 可以连接: $url"
        else
            log_warning "⚠️  无法连接: $url"
        fi
    done
}

# 帮助信息
show_help() {
    cat << EOF
🚀 上海荷阁科技 - 手动镜像拉取脚本

用法: $0 [选项]

选项:
    -h, --help     显示此帮助信息
    -t, --test     测试网络连接
    -s, --show     显示当前镜像
    -c, --cleanup  清理悬空镜像

示例:
    $0              # 拉取所有镜像
    $0 -t           # 测试网络连接
    $0 -s           # 显示当前镜像

说明:
    此脚本会尝试多种方法拉取镜像：
    1. 直接从 Docker Hub 拉取
    2. 通过国内镜像源拉取
    3. 通过代理服务拉取

EOF
}

# 主函数
main() {
    log_info "🚀 上海荷阁科技 - 手动镜像拉取工具"
    echo ""
    
    # 检查 Docker
    if ! docker info >/dev/null 2>&1; then
        log_error "❌ Docker 未运行或无法访问"
        exit 1
    fi
    
    # 拉取镜像
    if pull_all_images; then
        echo ""
        show_images
        echo ""
        cleanup
        echo ""
        log_success "🎉 镜像拉取完成！现在可以运行:"
        log_info "docker-compose -f .cicd/docker/docker-compose.yml up -d"
    else
        echo ""
        log_error "❌ 部分镜像拉取失败"
        log_info "请检查网络连接或尝试手动拉取失败的镜像"
        exit 1
    fi
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -t|--test)
        test_connection
        exit 0
        ;;
    -s|--show)
        show_images
        exit 0
        ;;
    -c|--cleanup)
        cleanup
        exit 0
        ;;
    "")
        main
        ;;
    *)
        log_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
