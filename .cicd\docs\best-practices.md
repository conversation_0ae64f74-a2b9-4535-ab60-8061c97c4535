# 🏆 CI/CD 最佳实践

本文档总结了上海荷阁科技企业官网 CI/CD 流水线的最佳实践和建议。

## 📋 目录

- [代码管理](#代码管理)
- [分支策略](#分支策略)
- [CI/CD 流水线](#cicd-流水线)
- [安全实践](#安全实践)
- [测试策略](#测试策略)
- [部署策略](#部署策略)
- [监控和日志](#监控和日志)
- [性能优化](#性能优化)

## 📝 代码管理

### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```bash
# 功能开发
git commit -m "feat: 添加用户登录功能"

# 问题修复
git commit -m "fix: 修复导航菜单在移动端的显示问题"

# 文档更新
git commit -m "docs: 更新 API 文档"

# 样式调整
git commit -m "style: 调整按钮样式"

# 重构代码
git commit -m "refactor: 重构用户认证模块"

# 性能优化
git commit -m "perf: 优化图片加载性能"

# 测试相关
git commit -m "test: 添加用户注册功能测试"

# 构建相关
git commit -m "build: 更新 webpack 配置"

# CI/CD 相关
git commit -m "ci: 添加自动化部署脚本"
```

### 代码审查

#### 必须审查的内容

- [ ] 代码逻辑正确性
- [ ] 安全漏洞检查
- [ ] 性能影响评估
- [ ] 测试覆盖率
- [ ] 文档更新
- [ ] 向后兼容性

#### 审查清单

```markdown
## 代码审查清单

### 功能性
- [ ] 功能按预期工作
- [ ] 边界条件处理正确
- [ ] 错误处理完善

### 安全性
- [ ] 输入验证充分
- [ ] 敏感信息未泄露
- [ ] 权限控制正确

### 性能
- [ ] 无明显性能问题
- [ ] 数据库查询优化
- [ ] 缓存策略合理

### 可维护性
- [ ] 代码结构清晰
- [ ] 命名规范一致
- [ ] 注释充分

### 测试
- [ ] 单元测试覆盖
- [ ] 集成测试通过
- [ ] E2E 测试验证
```

## 🌿 分支策略

### Git Flow 模型

```
main (生产环境)
├── develop (开发环境)
│   ├── feature/user-auth (功能分支)
│   ├── feature/payment-system (功能分支)
│   └── hotfix/critical-bug (热修复分支)
└── release/v1.2.0 (发布分支)
```

### 分支命名规范

```bash
# 功能分支
feature/feature-name
feature/user-authentication
feature/payment-integration

# 修复分支
fix/bug-description
fix/login-error
fix/mobile-layout

# 热修复分支
hotfix/critical-issue
hotfix/security-patch

# 发布分支
release/v1.0.0
release/v1.2.0-beta
```

### 分支保护规则

```yaml
# .gitlab-ci.yml 中的分支保护
only:
  - main
  - develop
  - /^release\/.*$/
  - /^hotfix\/.*$/

# 合并请求要求
merge_requests:
  required_approvals: 2
  dismiss_stale_reviews: true
  require_code_owner_reviews: true
```

## 🔄 CI/CD 流水线

### 流水线设计原则

1. **快速反馈**：尽早发现问题
2. **并行执行**：提高执行效率
3. **失败快速**：快速失败，快速修复
4. **可重复性**：确保结果一致
5. **可观测性**：提供详细日志和指标

### 阶段划分

```yaml
stages:
  - validate    # 代码验证 (2-3分钟)
  - test        # 测试执行 (5-10分钟)
  - security    # 安全扫描 (3-5分钟)
  - build       # 构建打包 (3-5分钟)
  - deploy      # 部署发布 (2-3分钟)
  - verify      # 部署验证 (1-2分钟)
```

### 作业优化

```yaml
# 使用缓存加速构建
cache:
  key: 
    files:
      - package-lock.json
  paths:
    - node_modules/
    - .next/cache/
  policy: pull-push

# 并行执行测试
test:parallel:
  parallel: 3
  script:
    - npm run test:parallel

# 条件执行
deploy:production:
  only:
    - main
  when: manual
```

## 🔒 安全实践

### 密钥管理

#### 环境变量安全

```bash
# ✅ 正确：使用环境变量
DATABASE_URL=postgresql://user:${DB_PASSWORD}@localhost/db

# ❌ 错误：硬编码密码
DATABASE_URL=postgresql://user:password123@localhost/db
```

#### 密钥轮换

```bash
# 定期轮换密钥
NEXTAUTH_SECRET=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -base64 64)
API_SECRET_KEY=$(openssl rand -base64 32)
```

### 依赖安全

#### 定期更新依赖

```bash
# 检查过期依赖
npm outdated

# 更新依赖
npm update

# 安全更新
npm audit fix
```

#### 依赖锁定

```json
{
  "dependencies": {
    "react": "18.2.0",
    "next": "14.0.4"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=8.0.0"
  }
}
```

### 容器安全

#### 最小权限原则

```dockerfile
# 使用非 root 用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

# 只复制必要文件
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
```

#### 安全扫描

```yaml
# 容器安全扫描
security:container:
  stage: security
  script:
    - trivy image --exit-code 1 --severity HIGH,CRITICAL $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
```

## 🧪 测试策略

### 测试金字塔

```
    E2E Tests (10%)
   ┌─────────────────┐
   │  Integration    │ (20%)
   │     Tests       │
   ├─────────────────┤
   │   Unit Tests    │ (70%)
   └─────────────────┘
```

### 测试分类

#### 单元测试

```javascript
// 组件测试
describe('Button Component', () => {
  it('should render with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByText('Click me')).toBeInTheDocument()
  })
})

// 工具函数测试
describe('formatDate', () => {
  it('should format date correctly', () => {
    expect(formatDate('2024-01-01')).toBe('2024年1月1日')
  })
})
```

#### 集成测试

```javascript
// API 集成测试
describe('User API', () => {
  it('should create user successfully', async () => {
    const response = await request(app)
      .post('/api/users')
      .send({ name: 'Test User', email: '<EMAIL>' })
    
    expect(response.status).toBe(201)
    expect(response.body.name).toBe('Test User')
  })
})
```

#### E2E 测试

```javascript
// 用户流程测试
test('user can complete registration', async ({ page }) => {
  await page.goto('/register')
  await page.fill('[data-testid=name]', 'Test User')
  await page.fill('[data-testid=email]', '<EMAIL>')
  await page.click('[data-testid=submit]')
  
  await expect(page.locator('[data-testid=success]')).toBeVisible()
})
```

### 测试数据管理

```javascript
// 测试工厂
const createTestUser = (overrides = {}) => ({
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
  ...overrides
})

// 数据库清理
beforeEach(async () => {
  await db.user.deleteMany()
  await db.post.deleteMany()
})
```

## 🚀 部署策略

### 蓝绿部署

```bash
# 部署到绿色环境
deploy_green() {
  docker-compose -f docker-compose.green.yml up -d
  wait_for_health_check "green"
}

# 切换流量
switch_traffic() {
  nginx_config_switch "green"
  nginx -s reload
}

# 清理蓝色环境
cleanup_blue() {
  docker-compose -f docker-compose.blue.yml down
}
```

### 金丝雀部署

```yaml
# 逐步增加流量
deploy:canary:
  script:
    - deploy_canary 10%   # 10% 流量
    - monitor_metrics 5m
    - deploy_canary 50%   # 50% 流量
    - monitor_metrics 10m
    - deploy_canary 100%  # 100% 流量
```

### 回滚策略

```bash
# 快速回滚
rollback() {
  local previous_version=$(get_previous_version)
  docker-compose down
  docker-compose -f docker-compose.${previous_version}.yml up -d
  verify_deployment
}
```

## 📊 监控和日志

### 应用监控

#### 关键指标

```javascript
// 性能指标
const metrics = {
  responseTime: 'avg(http_request_duration_seconds)',
  errorRate: 'rate(http_requests_total{status=~"5.."}[5m])',
  throughput: 'rate(http_requests_total[5m])',
  availability: 'up'
}

// 业务指标
const businessMetrics = {
  userRegistrations: 'increase(user_registrations_total[1h])',
  pageViews: 'increase(page_views_total[1h])',
  conversionRate: 'conversion_rate'
}
```

#### 告警规则

```yaml
# Prometheus 告警规则
groups:
  - name: hege-tech-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
      
      - alert: HighResponseTime
        expr: avg(http_request_duration_seconds) > 2
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
```

### 日志管理

#### 结构化日志

```javascript
// 使用结构化日志
const logger = winston.createLogger({
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: {
    service: 'hege-tech-web',
    version: process.env.APP_VERSION
  }
})

// 记录关键事件
logger.info('User login', {
  userId: user.id,
  ip: req.ip,
  userAgent: req.get('User-Agent')
})
```

#### 日志级别

```javascript
// 日志级别使用
logger.error('Database connection failed', { error: err.message })
logger.warn('API rate limit approaching', { userId, requestCount })
logger.info('User registered successfully', { userId })
logger.debug('Cache hit', { key, ttl })
```

## ⚡ 性能优化

### 构建优化

#### 缓存策略

```yaml
# GitLab CI 缓存
cache:
  - key: node-modules
    paths:
      - node_modules/
  - key: next-cache
    paths:
      - .next/cache/
```

#### 并行构建

```yaml
# 并行执行作业
test:unit:
  parallel: 3
  script:
    - npm run test:unit -- --shard=$CI_NODE_INDEX/$CI_NODE_TOTAL

build:assets:
  parallel:
    matrix:
      - ASSET_TYPE: [css, js, images]
  script:
    - npm run build:$ASSET_TYPE
```

### 运行时优化

#### 资源优化

```javascript
// Next.js 配置优化
module.exports = {
  // 启用 SWC 编译器
  swcMinify: true,
  
  // 图片优化
  images: {
    domains: ['example.com'],
    formats: ['image/webp', 'image/avif']
  },
  
  // 压缩配置
  compress: true,
  
  // 实验性功能
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lodash', 'date-fns']
  }
}
```

#### 数据库优化

```sql
-- 添加索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_posts_created_at ON posts(created_at DESC);

-- 查询优化
EXPLAIN ANALYZE SELECT * FROM users WHERE email = $1;
```

### 监控性能

```javascript
// 性能监控
const performanceObserver = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    if (entry.entryType === 'navigation') {
      logger.info('Page load performance', {
        loadTime: entry.loadEventEnd - entry.loadEventStart,
        domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart
      })
    }
  }
})

performanceObserver.observe({ entryTypes: ['navigation'] })
```

## 📈 持续改进

### 指标驱动

#### 关键指标 (KPIs)

- **部署频率**：每周部署次数
- **变更前置时间**：从提交到生产的时间
- **平均恢复时间**：故障恢复时间
- **变更失败率**：部署失败的百分比

#### 改进循环

```
Plan → Do → Check → Act
  ↑                   ↓
  ←←←←←←←←←←←←←←←←←←←←←
```

### 团队协作

#### 定期回顾

- **每日站会**：同步进度和问题
- **周度回顾**：分析指标和改进点
- **月度总结**：评估整体效果

#### 知识分享

- **技术分享**：新技术和最佳实践
- **故障复盘**：从失败中学习
- **文档更新**：保持文档最新

---

© 2024 上海荷阁科技有限公司 - CI/CD 最佳实践
