# 🚀 上海荷阁科技 CI/CD 设置指南

本指南将帮助您完整设置上海荷阁科技企业官网的 CI/CD 流水线。

## 📋 目录

- [前置要求](#前置要求)
- [快速开始](#快速开始)
- [GitLab CI/CD 配置](#gitlab-cicd-配置)
- [Docker 环境设置](#docker-环境设置)
- [安全扫描配置](#安全扫描配置)
- [测试环境配置](#测试环境配置)
- [Windows 环境设置](#windows-环境设置)
- [部署配置](#部署配置)
- [监控和告警](#监控和告警)

## ⚡ 快速开始

### 1. 一键设置 CI/CD

```bash
# 复制主配置文件
cp .cicd/gitlab-ci/.gitlab-ci.yml .gitlab-ci.yml

# 设置必要的环境变量 (在 GitLab 项目设置中)
# - DOCKER_REGISTRY
# - SSH_PRIVATE_KEY
# - DATABASE_URL
# - NEXTAUTH_SECRET

# 提交并推送代码
git add .gitlab-ci.yml
git commit -m "feat: 添加 CI/CD 配置"
git push origin main
```

### 2. 本地开发环境

```bash
# 启动本地开发环境
docker-compose -f .cicd/docker/docker-compose.yml up -d

# 访问应用
open http://localhost:3000
```

## 🔧 前置要求

### 系统要求

- **GitLab**: 14.0+ (支持 GitLab CI/CD)
- **Docker**: 20.10+ (用于容器化)
- **Node.js**: 18+ (用于应用构建)
- **Git**: 2.30+ (版本控制)

### 权限要求

- GitLab 项目的 Maintainer 权限
- Docker Registry 的推送权限
- 目标服务器的部署权限

## 🔄 GitLab CI/CD 配置

### 1. 复制主配置文件

```bash
# 将 CI/CD 配置复制到项目根目录
cp .cicd/gitlab-ci/.gitlab-ci.yml .gitlab-ci.yml
```

### 2. 设置环境变量

在 GitLab 项目设置中配置以下环境变量：

#### 必需变量

```bash
# Docker 镜像仓库
DOCKER_REGISTRY=registry.gitlab.com/hege-tech/hege-tech-web
**********************-web

# 部署服务器
STAGING_HOST=staging.hege-tech.cn
STAGING_USER=deploy
PRODUCTION_HOST=hege-tech.cn
PRODUCTION_USER=deploy

# SSH 密钥
SSH_PRIVATE_KEY=<your-ssh-private-key>

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/hege_tech_db
DB_NAME=hege_tech_db
DB_USER=hege_user
DB_PASSWORD=<secure-password>

# 应用密钥
NEXTAUTH_SECRET=<random-secret-key>
NEXTAUTH_URL=https://hege-tech.cn
JWT_SECRET=<jwt-secret-key>
CSRF_SECRET=<csrf-secret-key>
API_SECRET_KEY=<api-secret-key>

# 缓存配置
REDIS_PASSWORD=<redis-password>

# 监控配置
GRAFANA_PASSWORD=<grafana-admin-password>
```

#### 可选变量

```bash
# 通知配置
SLACK_WEBHOOK_URL=<slack-webhook-url>
SLACK_SECURITY_WEBHOOK=<security-alerts-webhook>
SLACK_PERFORMANCE_WEBHOOK=<performance-alerts-webhook>
SLACK_DEVOPS_WEBHOOK=<devops-alerts-webhook>

# 代理配置
HTTP_PROXY=<http-proxy-url>
HTTPS_PROXY=<https-proxy-url>

# 日志级别
LOG_LEVEL=info
ENABLE_SECURITY_LOGS=true

# 加密配置
BCRYPT_ROUNDS=12
```

### 3. 配置 GitLab Runner

#### 注册 Runner

```bash
# 使用 Docker 执行器
gitlab-runner register \
  --url "http://home.spinach.cool:11991/" \
  --registration-token "your-token" \
  --name "hege-tech-docker" \
  --executor "docker" \
  --docker-image "node:18-alpine" \
  --tag-list "docker,nodejs"

# 使用 Shell 执行器 (Windows)
gitlab-runner register \
  --url "http://home.spinach.cool:11991/" \
  --registration-token "your-token" \
  --name "hege-tech-windows" \
  --executor "shell" \
  --shell "powershell" \
  --tag-list "windows,shell"
```

## 🐳 Docker 环境设置

### 1. 构建基础镜像

```bash
# 构建应用镜像
docker build -f .cicd/docker/Dockerfile -t hege-tech-web:latest .

# 构建 Nginx 镜像
docker build -f .cicd/docker/Dockerfile.nginx -t hege-tech-nginx:latest .cicd/docker/
```

### 2. 本地开发环境

```bash
# 启动开发环境
docker-compose -f .cicd/docker/docker-compose.yml up -d

# 查看服务状态
docker-compose -f .cicd/docker/docker-compose.yml ps

# 查看日志
docker-compose -f .cicd/docker/docker-compose.yml logs -f app
```

### 3. 生产环境部署

```bash
# 部署到生产环境
docker-compose -f .cicd/docker/docker-compose.prod.yml up -d

# 健康检查
curl -f http://localhost:3000/api/health
```

## 🔒 安全扫描配置

### 1. 静态代码分析 (SAST)

```bash
# 运行 ESLint 安全检查
npx eslint . --config .cicd/security/sast/.eslintrc.security.js

# 运行 npm audit
npm audit --audit-level=moderate
```

### 2. 依赖漏洞扫描

```bash
# 配置 npm audit
cp .cicd/security/dependency/.npmauditrc .npmauditrc

# 运行依赖扫描
npm audit --config .npmauditrc
```

### 3. 容器安全扫描

```bash
# 安装 Trivy
curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin

# 扫描镜像
trivy image --config .cicd/security/container/trivy.yml hege-tech-web:latest

# 扫描文件系统
trivy fs --config .cicd/security/container/trivy.yml .
```

### 4. Dockerfile 检查

```bash
# 安装 Hadolint
docker pull hadolint/hadolint

# 检查 Dockerfile
docker run --rm -i hadolint/hadolint < .cicd/docker/Dockerfile
```

## 🧪 测试环境配置

### 1. 单元测试

```bash
# 安装测试依赖
npm install --save-dev @testing-library/jest-dom @testing-library/react @testing-library/user-event jest jest-environment-jsdom

# 运行测试
npm run test

# 生成覆盖率报告
npm run test:coverage
```

### 2. 端到端测试

```bash
# 安装 Playwright
npm install --save-dev @playwright/test

# 安装浏览器
npx playwright install

# 运行 E2E 测试
npm run test:e2e

# 运行带 UI 的测试
npm run test:e2e:ui
```

### 3. 性能测试

```bash
# 安装 Lighthouse CI
npm install --save-dev @lhci/cli

# 运行性能测试
npm run test:performance

# 查看报告
open coverage/lighthouse-reports/index.html
```

## 🪟 Windows 环境设置

### 1. 安装开发环境

```powershell
# 以管理员身份运行 PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope LocalMachine

# 运行环境设置脚本
.\.cicd\windows\scripts\setup.ps1 -InstallAll
```

### 2. 安装 GitLab Runner

```powershell
# 安装 GitLab Runner
.\.cicd\windows\runner\install.ps1 -RegistrationToken "your-token"

# 验证安装
Get-Service gitlab-runner
```

### 3. 构建应用

```powershell
# 运行构建脚本
.\.cicd\windows\scripts\build.ps1 -Environment production -Clean

# 构建 Docker 镜像
.\.cicd\windows\scripts\build.ps1 -Docker
```

## 🚀 部署配置

### 1. 测试环境部署

```bash
# 部署到测试环境
./.cicd/deploy/scripts/deploy-staging.sh -t v1.0.0 -b

# 检查部署状态
./.cicd/deploy/scripts/deploy-staging.sh -c

# 回滚部署
./.cicd/deploy/scripts/deploy-staging.sh -r
```

### 2. 生产环境部署

```bash
# 部署到生产环境 (需要确认)
./.cicd/deploy/scripts/deploy-production.sh -t v1.0.0

# 强制部署 (跳过确认)
./.cicd/deploy/scripts/deploy-production.sh -t v1.0.0 -f

# 启用维护模式
./.cicd/deploy/scripts/deploy-production.sh -m
```

### 3. 健康检查

```bash
# 运行健康检查
./.cicd/docker/scripts/health-check.sh

# 自定义健康检查
./.cicd/docker/scripts/health-check.sh -u http://hege-tech.cn -e /api/health
```

## 📊 监控和告警

### 1. 应用监控

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3001
- **应用健康检查**: http://localhost:3000/api/health

### 2. 日志监控

```bash
# 查看应用日志
docker-compose logs -f app

# 查看 Nginx 日志
docker-compose logs -f nginx

# 查看数据库日志
docker-compose logs -f database
```

### 3. 性能监控

```bash
# 查看容器资源使用
docker stats

# 查看系统资源
htop

# 查看网络连接
netstat -tulpn
```

## 🔧 故障排除

### 常见问题

1. **构建失败**: 检查 Node.js 版本和依赖
2. **测试失败**: 检查测试环境配置
3. **部署失败**: 检查服务器连接和权限
4. **容器启动失败**: 检查 Docker 配置和资源

### 调试命令

```bash
# 检查 GitLab Runner 状态
gitlab-runner verify

# 检查 Docker 容器
docker ps -a
docker logs <container-id>

# 检查网络连接
curl -v http://localhost:3000/api/health

# 检查文件权限
ls -la .cicd/
```

## 📞 支持

如果您在设置过程中遇到问题，请联系：

- **邮箱**: <EMAIL>
- **电话**: 18221165813
- **项目地址**: http://home.spinach.cool:11991/hege-tech/hege-tech-web

---

© 2024 上海荷阁科技有限公司 - CI/CD 设置指南
