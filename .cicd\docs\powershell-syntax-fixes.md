# PowerShell Syntax Fixes for GitLab CI/CD Pipeline

## 问题概述

在GitLab CI/CD运行过程中遇到了多个PowerShell语法错误，导致pipeline失败。主要问题包括变量引用错误、try-catch语法不兼容、以及复杂PowerShell命令在GitLab CI/CD环境中的执行问题。

## 修复策略

采用了两种主要策略：

1. **简化内联命令**：将复杂的PowerShell语法改为简单的命令组合
2. **外部脚本化**：将复杂逻辑移到独立的PowerShell脚本中

## 修复的问题

### 1. 变量引用语法错误

**问题**:

```powershell
# 错误的语法
docker build -t "$LOCAL_IMAGE_NAME:$LOCAL_IMAGE_TAG" .
```

**错误信息**:

```
Variable reference is not valid. ':' was not followed by a valid variable name character.
```

**解决方案**:

```powershell
# 正确的语法
docker build -t "${LOCAL_IMAGE_NAME}:${LOCAL_IMAGE_TAG}" .
```

**说明**: 在PowerShell中，当变量名后面紧跟冒号时，需要使用`${}`语法来明确变量边界。

### 2. 逻辑OR操作符错误

**问题**:

```powershell
# 错误的语法
docker inspect "$LOCAL_IMAGE_NAME:latest" | Select-String -Pattern "ExposedPorts" || echo "No exposed ports info"
```

**错误信息**:

```
The token '||' is not a valid statement separator in this version.
```

**解决方案**:

```powershell
# 正确的语法
try { docker inspect "${LOCAL_IMAGE_NAME}:latest" | Select-String -Pattern "ExposedPorts" } catch { echo "No exposed ports info" }
```

**说明**: PowerShell不支持`||`操作符，应该使用`try-catch`结构来处理错误。

### 3. 文件路径检查问题

**问题**: 脚本文件路径不存在导致执行失败

**解决方案**:

```powershell
# 添加文件存在性检查
if (Test-Path ".cicd\scripts\generate-docker-report.ps1") {
    PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\generate-docker-report.ps1" -ImageName $LOCAL_IMAGE_NAME -ImageTag "latest"
} else {
    echo "Build report script not found, skipping..."
}
```

### 4. Docker命令中的变量引用

**问题**: 在Docker命令中使用变量时语法不正确

**修复前**:

```powershell
docker run -d --name $CONTAINER_NAME -p "$HOST_PORT:$CONTAINER_PORT" "$LOCAL_IMAGE_NAME:latest"
```

**修复后**:

```powershell
docker run -d --name $CONTAINER_NAME -p "${HOST_PORT}:${CONTAINER_PORT}" "${LOCAL_IMAGE_NAME}:latest"
```

### 5. 错误重定向语法

**问题**: 使用了bash风格的错误重定向

**修复前**:

```powershell
docker stop $CONTAINER_NAME 2>$null || echo "Container does not exist"
```

**修复后**:

```powershell
try { docker stop $CONTAINER_NAME 2>$null } catch { echo "Container does not exist" }
```

### 6. GitLab CI/CD中的try-catch语法问题

**问题**: GitLab CI/CD无法正确解析多行try-catch语法

**修复前**:

```powershell
try { docker stop $CONTAINER_NAME 2>$null } catch { echo "Container does not exist" }
```

**修复后**:

```powershell
# 方法1: 使用$LASTEXITCODE检查
docker stop $CONTAINER_NAME 2>$null; if ($LASTEXITCODE -ne 0) { echo "Container does not exist" }

# 方法2: 使用外部脚本
PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\deploy-local-docker.ps1"
```

## 修复的文件

### 1. `.gitlab-ci.yml`

- 修复了`build:docker`作业中的变量引用语法
- 将`deploy:local`作业改为使用外部脚本
- 将`cleanup:local`作业改为使用外部脚本
- 简化了after_script中的错误处理

### 2. `.cicd/scripts/generate-docker-report.ps1`

- 改进了Docker镜像检查逻辑
- 增强了错误处理机制

### 3. 新增脚本文件

- `deploy-local-docker.ps1`: 专门的Docker部署脚本，包含完整的错误处理
- `cleanup-local-docker.ps1`: 专门的Docker清理脚本
- `test-powershell-syntax.ps1`: 语法验证测试脚本

## 验证结果

### 1. 语法测试验证

运行测试脚本`test-powershell-syntax.ps1`，所有语法测试均通过：

```text
=== PowerShell Syntax Test Completed ===
All syntax tests passed. The GitLab CI/CD pipeline should work correctly.
```

### 2. 部署脚本验证

运行`deploy-local-docker.ps1`脚本测试：

```text
=== Deployment Summary ===
✓ Container Name: test-container
✓ Image: hege-tech-web:latest
✓ Port Mapping: 3001:3000
✓ Access URL: http://localhost:3001

Local Docker deployment completed successfully!
```

### 3. 清理脚本验证

运行`cleanup-local-docker.ps1`脚本测试：

```text
=== Cleanup Summary ===
Current containers: [显示当前容器状态]
Current images: [显示当前镜像列表]
Docker cleanup completed!
```

## 最佳实践建议

1. **变量引用**: 在PowerShell中，当变量名后面有特殊字符时，始终使用`${variable_name}`语法
2. **错误处理**: 使用`try-catch`而不是`||`操作符
3. **文件路径**: 在执行脚本前使用`Test-Path`检查文件是否存在
4. **Docker命令**: 在Docker命令中使用变量时，确保正确的引用语法
5. **测试验证**: 在部署前使用测试脚本验证PowerShell语法

## 下一步

1. 提交修复后的代码到`ci`分支
2. 运行GitLab CI/CD pipeline验证修复效果
3. 如果还有其他问题，继续迭代修复

## 相关文档

- [PowerShell变量引用文档](https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_variables)
- [PowerShell错误处理文档](https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_try_catch_finally)
- [GitLab CI/CD PowerShell最佳实践](../windows/README.md)
