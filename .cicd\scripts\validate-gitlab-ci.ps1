# Shanghai Hege Technology - GitLab CI/CD Validation Script
# Validate .gitlab-ci.yml syntax and configuration

$ErrorActionPreference = "Stop"

Write-Host "Validating GitLab CI/CD configuration..." -ForegroundColor Green

try {
    # Check if .gitlab-ci.yml exists
    $ciFile = ".gitlab-ci.yml"
    if (-not (Test-Path $ciFile)) {
        Write-Host "Error: .gitlab-ci.yml file not found" -ForegroundColor Red
        exit 1
    }

    Write-Host "Found .gitlab-ci.yml file" -ForegroundColor Green

    # Basic YAML syntax validation
    Write-Host "Performing basic YAML syntax checks..." -ForegroundColor Yellow
    
    # Check for common YAML issues
    $content = Get-Content $ciFile -Raw
    
    # Check for tab characters (should use spaces)
    if ($content -match "`t") {
        Write-Host "Warning: Found tab characters in YAML file. Use spaces instead." -ForegroundColor Yellow
    }
    
    # Check for proper indentation patterns
    $lines = Get-Content $ciFile
    $lineNumber = 0
    foreach ($line in $lines) {
        $lineNumber++
        
        # Check for mixed indentation
        if ($line -match "^( *)([^ ])" -and $line -match "`t") {
            Write-Host "Warning: Mixed spaces and tabs on line $lineNumber" -ForegroundColor Yellow
        }
        
        # Check for trailing spaces
        if ($line -match " $") {
            Write-Host "Warning: Trailing spaces on line $lineNumber" -ForegroundColor Yellow
        }
    }
    
    # Check for PowerShell variable assignment syntax
    Write-Host "Checking PowerShell syntax in scripts..." -ForegroundColor Yellow
    
    $scriptSections = $content | Select-String -Pattern "script:" -Context 0,20
    foreach ($section in $scriptSections) {
        $sectionText = $section.Context.PostContext -join "`n"
        
        # Check for incorrect variable assignment
        if ($sectionText -match '\$\w+\s*=\s*\$env:') {
            Write-Host "Warning: Found PowerShell variable assignment in script section. Use environment variables directly." -ForegroundColor Yellow
        }
    }
    
    Write-Host "Basic validation completed" -ForegroundColor Green
    
    # Display file statistics
    $totalLines = $lines.Count
    $nonEmptyLines = ($lines | Where-Object { $_.Trim() -ne "" }).Count
    $commentLines = ($lines | Where-Object { $_.Trim().StartsWith("#") }).Count
    
    Write-Host "`nFile Statistics:" -ForegroundColor Cyan
    Write-Host "Total lines: $totalLines" -ForegroundColor White
    Write-Host "Non-empty lines: $nonEmptyLines" -ForegroundColor White
    Write-Host "Comment lines: $commentLines" -ForegroundColor White
    
    Write-Host "`nValidation completed successfully" -ForegroundColor Green
    Write-Host "Note: For complete validation, use GitLab's CI Lint tool" -ForegroundColor Yellow
}
catch {
    Write-Host "Validation failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
