# 🪟 Windows 统一 CI/CD 配置说明

## 📋 概述

本配置将整个 CI/CD 流水线统一到 Windows 环境，最终产物为本地 Docker 镜像。

## 🏗️ 流水线架构

### 阶段设计

1. **prepare** - 环境准备和依赖安装
2. **test** - 单元测试和集成测试
3. **security** - 安全扫描和代码质量检查
4. **build** - 构建应用和 Docker 镜像
5. **deploy** - 本地 Docker 部署
6. **report** - 生成和发布报告

### 环境要求

- Windows Runner with Shell Executor
- Node.js 18+
- Docker Desktop for Windows
- PowerShell 5.1+

## 🔧 关键配置变更

### 1. 统一 Windows 环境

```yaml
default:
  tags:
    - windows
    - shell
  before_script:
    - echo "🚀 开始执行 CI/CD 流水线 - $(Get-Date)"
```

### 2. 依赖安装优化

- 使用 PowerShell 脚本 `.cicd/scripts/install-dependencies.ps1`
- 多重安装策略确保成功率
- 国内镜像源配置

### 3. Docker 本地构建

```yaml
build:docker:
  variables:
    LOCAL_IMAGE_NAME: "hege-tech-web"
    LOCAL_IMAGE_TAG: "$CI_COMMIT_SHORT_SHA"
  script:
    - docker build -f .cicd/docker/Dockerfile -t "${LOCAL_IMAGE_NAME}:latest" .
```

### 4. 本地部署

```yaml
deploy:local:
  environment:
    name: local
    url: http://localhost:3000
  script:
    - docker run -d --name hege-tech-web-local -p 3000:3000 hege-tech-web:latest
```

## 📊 新增功能

### 1. 自动化脚本

- **依赖安装**: `.cicd/scripts/install-dependencies.ps1`
- **部署验证**: `.cicd/scripts/verify-local-deployment.ps1`
- **构建报告**: `.cicd/scripts/generate-docker-report.ps1`

### 2. 本地部署管理

- **部署作业**: `deploy:local` - 启动本地容器
- **清理作业**: `cleanup:local` - 清理本地环境
- **健康检查**: 自动验证应用可访问性

### 3. 报告生成

- JUnit 格式的 Docker 构建报告
- 流水线执行总结报告
- 容器运行状态报告

## 🚀 使用指南

### 手动触发部署

```bash
# 在 GitLab UI 中手动触发
# Pipeline > Run Pipeline > deploy:local
```

### 本地访问应用

```
http://localhost:3000
```

### 清理本地环境

```bash
# 在 GitLab UI 中手动触发
# Pipeline > Run Pipeline > cleanup:local
```

## 🔍 监控和调试

### 查看容器状态

```powershell
docker ps -f name=hege-tech-web
```

### 查看容器日志

```powershell
docker logs hege-tech-web-local
```

### 查看镜像列表

```powershell
docker images hege-tech-web
```

## 🛠️ 故障排除

### 常见问题

1. **Docker 服务未启动**
   - 确保 Docker Desktop 正在运行
   - 检查 Windows 服务中的 Docker 服务状态

2. **端口冲突**
   - 检查端口 3000 是否被占用
   - 修改 `HOST_PORT` 变量使用其他端口

3. **权限问题**
   - 确保 GitLab Runner 服务账户有 Docker 访问权限
   - 检查 PowerShell 执行策略

4. **网络问题**
   - 检查防火墙设置
   - 确保 localhost 访问正常

### 调试步骤

1. 检查 GitLab Runner 日志
2. 验证 Docker 环境
3. 手动执行 PowerShell 脚本
4. 检查容器日志和状态

## 📈 性能优化

### 缓存策略

- Node.js 依赖缓存
- Docker 层缓存
- 构建产物缓存

### 并行执行

- 测试作业可并行运行
- 安全扫描独立执行
- 报告生成异步处理

## 🔒 安全考虑

- 本地部署仅限开发测试
- 敏感信息使用 GitLab Variables
- 定期清理本地镜像和容器
- 安全扫描集成到流水线

## 📚 相关文档

- [依赖安装故障排除](.cicd/docs/troubleshooting-dependencies.md)
- [Docker 配置说明](.cicd/docker/README.md)
- [安全配置指南](.cicd/security/README.md)
