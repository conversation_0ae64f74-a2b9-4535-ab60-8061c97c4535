# 上海荷阁科技网站环境变量配置
# 复制此文件为 .env.local 并填入实际值

# ===========================================
# 基础配置
# ===========================================
NODE_ENV=development
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME=上海荷阁科技有限公司

# ===========================================
# 安全配置
# ===========================================
# 会话密钥 (生产环境必须更改)
NEXTAUTH_SECRET=your-super-secret-key-change-in-production
NEXTAUTH_URL=http://localhost:3000

# JWT密钥
JWT_SECRET=your-jwt-secret-key-change-in-production

# 加密盐值轮数
BCRYPT_ROUNDS=12

# CSRF密钥
CSRF_SECRET=your-csrf-secret-key

# ===========================================
# 数据库配置
# ===========================================
DATABASE_URL=postgresql://username:password@localhost:5432/hege_tech_db
DATABASE_SSL=false

# ===========================================
# 邮件服务配置
# ===========================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# ===========================================
# 文件上传配置
# ===========================================
UPLOAD_DIR=./public/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,application/pdf

# ===========================================
# API安全配置
# ===========================================
# API速率限制 (每分钟请求数)
API_RATE_LIMIT=100
API_RATE_WINDOW=60000

# API密钥 (用于内部API调用)
API_SECRET_KEY=your-api-secret-key

# ===========================================
# 第三方服务
# ===========================================
# Google Analytics
NEXT_PUBLIC_GA_ID=

# Google Maps
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=

# 腾讯云配置
TENCENT_CLOUD_SECRET_ID=
TENCENT_CLOUD_SECRET_KEY=
TENCENT_CLOUD_REGION=ap-shanghai

# ===========================================
# 监控和日志
# ===========================================
# 启用安全日志
ENABLE_SECURITY_LOGS=true
LOG_LEVEL=info

# Sentry错误监控 (可选)
SENTRY_DSN=

# ===========================================
# 开发配置
# ===========================================
# 调试模式
DEBUG=false

# 显示详细错误信息 (仅开发环境)
SHOW_DETAILED_ERRORS=false

# ===========================================
# 生产环境配置
# ===========================================
# 启用HTTPS重定向
FORCE_HTTPS=false

# 启用安全头部
ENABLE_SECURITY_HEADERS=true

# 启用内容安全策略
ENABLE_CSP=true
