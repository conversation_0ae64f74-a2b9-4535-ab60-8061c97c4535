# 🚀 上海荷阁科技 - Windows 构建脚本
# 在 Windows 环境下构建 Next.js 应用

param(
    [string]$Environment = "development",
    [string]$OutputDir = ".\out",
    [switch]$Clean,
    [switch]$Analyze,
    [switch]$Docker,
    [switch]$Help
)

# 显示帮助信息
if ($Help) {
    Write-Host @"
🚀 上海荷阁科技 Windows 构建脚本

用法: .\build.ps1 [参数]

参数:
    -Environment    构建环境 (development/staging/production) (默认: development)
    -OutputDir      输出目录 (默认: .\out)
    -Clean          清理构建缓存
    -Analyze        分析构建包大小
    -Docker         构建 Docker 镜像
    -Help           显示此帮助信息

示例:
    .\build.ps1                                    # 开发环境构建
    .\build.ps1 -Environment production -Clean    # 生产环境构建并清理缓存
    .\build.ps1 -Docker                           # 构建 Docker 镜像
    .\build.ps1 -Analyze                          # 分析构建包大小

"@
    exit 0
}

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colors = @{
        "Red" = [ConsoleColor]::Red
        "Green" = [ConsoleColor]::Green
        "Yellow" = [ConsoleColor]::Yellow
        "Blue" = [ConsoleColor]::Blue
        "White" = [ConsoleColor]::White
    }
    
    Write-Host $Message -ForegroundColor $colors[$Color]
}

function Write-Info { param([string]$Message) Write-ColorOutput "ℹ️ $Message" "Blue" }
function Write-Success { param([string]$Message) Write-ColorOutput "✅ $Message" "Green" }
function Write-Warning { param([string]$Message) Write-ColorOutput "⚠️ $Message" "Yellow" }
function Write-Error { param([string]$Message) Write-ColorOutput "❌ $Message" "Red" }

# 获取项目根目录
$ProjectRoot = Split-Path -Parent (Split-Path -Parent (Split-Path -Parent $PSScriptRoot))
Set-Location $ProjectRoot

Write-Info "🚀 开始构建上海荷阁科技企业官网"
Write-Info "项目目录: $ProjectRoot"
Write-Info "构建环境: $Environment"
Write-Info "输出目录: $OutputDir"
Write-Info "构建时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

# 检查必要工具
function Test-Requirements {
    Write-Info "检查构建要求..."
    
    # 检查 Node.js
    try {
        $nodeVersion = node --version
        Write-Success "Node.js 版本: $nodeVersion"
    } catch {
        Write-Error "Node.js 未安装或不在 PATH 中"
        exit 1
    }
    
    # 检查 npm
    try {
        $npmVersion = npm --version
        Write-Success "npm 版本: $npmVersion"
    } catch {
        Write-Error "npm 未安装或不在 PATH 中"
        exit 1
    }
    
    # 检查 package.json
    if (-not (Test-Path "package.json")) {
        Write-Error "未找到 package.json 文件"
        exit 1
    }
    Write-Success "package.json 文件存在"
    
    # 检查 Docker (如果需要)
    if ($Docker) {
        try {
            docker --version | Out-Null
            Write-Success "Docker 可用"
        } catch {
            Write-Error "Docker 未安装或不在 PATH 中"
            exit 1
        }
    }
}

# 设置环境变量
function Set-BuildEnvironment {
    Write-Info "设置构建环境变量..."
    
    switch ($Environment.ToLower()) {
        "development" {
            $env:NODE_ENV = "development"
            $env:NEXT_PUBLIC_SITE_URL = "http://localhost:3000"
            $env:NEXT_PUBLIC_SITE_NAME = "上海荷阁科技有限公司"
        }
        "staging" {
            $env:NODE_ENV = "production"
            $env:NEXT_PUBLIC_SITE_URL = "https://staging.hege-tech.cn"
            $env:NEXT_PUBLIC_SITE_NAME = "上海荷阁科技有限公司"
        }
        "production" {
            $env:NODE_ENV = "production"
            $env:NEXT_PUBLIC_SITE_URL = "https://hege-tech.cn"
            $env:NEXT_PUBLIC_SITE_NAME = "上海荷阁科技有限公司"
        }
        default {
            Write-Warning "未知环境: $Environment，使用默认配置"
            $env:NODE_ENV = "development"
        }
    }
    
    # 设置构建相关环境变量
    $env:NEXT_TELEMETRY_DISABLED = "1"
    $env:CI = "true"
    $env:FORCE_COLOR = "1"
    
    Write-Success "环境变量设置完成"
    Write-Info "NODE_ENV: $env:NODE_ENV"
    Write-Info "SITE_URL: $env:NEXT_PUBLIC_SITE_URL"
}

# 清理构建缓存
function Clear-BuildCache {
    if ($Clean) {
        Write-Info "清理构建缓存..."
        
        $cacheDirs = @(".next", "out", "dist", "build", "node_modules\.cache")
        
        foreach ($dir in $cacheDirs) {
            if (Test-Path $dir) {
                Remove-Item $dir -Recurse -Force
                Write-Success "已清理: $dir"
            }
        }
        
        # 清理 npm 缓存
        try {
            npm cache clean --force
            Write-Success "npm 缓存已清理"
        } catch {
            Write-Warning "清理 npm 缓存失败"
        }
    }
}

# 安装依赖
function Install-Dependencies {
    Write-Info "安装项目依赖..."
    
    try {
        # 使用 npm ci 进行干净安装
        npm ci --prefer-offline --no-audit
        Write-Success "依赖安装完成"
    } catch {
        Write-Error "依赖安装失败"
        exit 1
    }
}

# 运行代码检查
function Invoke-CodeCheck {
    Write-Info "运行代码检查..."
    
    try {
        # TypeScript 类型检查
        Write-Info "TypeScript 类型检查..."
        npm run type-check
        Write-Success "TypeScript 类型检查通过"
        
        # ESLint 检查
        Write-Info "ESLint 代码检查..."
        npm run lint
        Write-Success "ESLint 检查通过"
        
        # Prettier 格式检查
        Write-Info "Prettier 格式检查..."
        npm run format:check
        Write-Success "Prettier 格式检查通过"
        
    } catch {
        Write-Warning "代码检查发现问题，但继续构建"
    }
}

# 运行测试
function Invoke-Tests {
    Write-Info "运行单元测试..."
    
    try {
        npm run test:ci
        Write-Success "单元测试通过"
    } catch {
        Write-Warning "单元测试失败，但继续构建"
    }
}

# 构建应用
function Build-Application {
    Write-Info "构建 Next.js 应用..."
    
    try {
        # 记录开始时间
        $startTime = Get-Date
        
        # 执行构建
        if ($Analyze) {
            Write-Info "启用构建分析..."
            $env:ANALYZE = "true"
        }
        
        npm run build
        
        # 计算构建时间
        $endTime = Get-Date
        $buildTime = $endTime - $startTime
        
        Write-Success "应用构建完成"
        Write-Info "构建耗时: $($buildTime.TotalSeconds.ToString('F2')) 秒"
        
        # 显示构建结果
        if (Test-Path ".next") {
            $buildSize = (Get-ChildItem ".next" -Recurse | Measure-Object -Property Length -Sum).Sum
            Write-Info "构建大小: $([math]::Round($buildSize/1MB, 2)) MB"
        }
        
    } catch {
        Write-Error "应用构建失败"
        exit 1
    }
}

# 构建 Docker 镜像
function Build-DockerImage {
    if ($Docker) {
        Write-Info "构建 Docker 镜像..."
        
        $imageName = "hege-tech-web"
        $imageTag = if ($Environment -eq "production") { "latest" } else { $Environment }
        $fullImageName = "${imageName}:${imageTag}"
        
        try {
            # 构建镜像
            docker build -f .cicd/docker/Dockerfile -t $fullImageName .
            
            Write-Success "Docker 镜像构建完成: $fullImageName"
            
            # 显示镜像信息
            $imageInfo = docker images $imageName --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
            Write-Info "镜像信息:"
            Write-Host $imageInfo
            
        } catch {
            Write-Error "Docker 镜像构建失败"
            exit 1
        }
    }
}

# 生成构建报告
function Generate-BuildReport {
    Write-Info "生成构建报告..."
    
    $reportPath = "build-report.json"
    $report = @{
        project = "上海荷阁科技企业官网"
        environment = $Environment
        buildTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        nodeVersion = (node --version)
        npmVersion = (npm --version)
        platform = "Windows"
        success = $true
    }
    
    # 添加构建统计
    if (Test-Path ".next") {
        $buildStats = @{
            outputDir = ".next"
            totalSize = (Get-ChildItem ".next" -Recurse | Measure-Object -Property Length -Sum).Sum
            fileCount = (Get-ChildItem ".next" -Recurse -File).Count
        }
        $report.buildStats = $buildStats
    }
    
    # 保存报告
    $report | ConvertTo-Json -Depth 3 | Out-File $reportPath -Encoding UTF8
    Write-Success "构建报告已生成: $reportPath"
}

# 清理临时文件
function Clear-TempFiles {
    Write-Info "清理临时文件..."
    
    $tempFiles = @(
        "*.log",
        "*.tmp",
        ".env.local"
    )
    
    foreach ($pattern in $tempFiles) {
        Get-ChildItem $pattern -ErrorAction SilentlyContinue | Remove-Item -Force
    }
    
    Write-Success "临时文件清理完成"
}

# 主构建流程
function Main {
    try {
        # 检查构建要求
        Test-Requirements
        
        # 设置环境变量
        Set-BuildEnvironment
        
        # 清理构建缓存
        Clear-BuildCache
        
        # 安装依赖
        Install-Dependencies
        
        # 代码检查
        Invoke-CodeCheck
        
        # 运行测试
        Invoke-Tests
        
        # 构建应用
        Build-Application
        
        # 构建 Docker 镜像
        Build-DockerImage
        
        # 生成构建报告
        Generate-BuildReport
        
        # 清理临时文件
        Clear-TempFiles
        
        Write-Success "🎉 构建完成！"
        Write-Info ""
        Write-Info "构建结果:"
        Write-Info "  环境: $Environment"
        Write-Info "  输出: .next/"
        if ($Docker) {
            Write-Info "  Docker 镜像: hege-tech-web:$($Environment)"
        }
        Write-Info "  报告: build-report.json"
        Write-Info ""
        Write-Info "下一步操作:"
        Write-Info "  npm run start    # 启动生产服务器"
        if ($Docker) {
            Write-Info "  docker run -p 3000:3000 hege-tech-web:$($Environment)    # 运行 Docker 容器"
        }
        
    } catch {
        Write-Error "构建过程中发生错误: $($_.Exception.Message)"
        exit 1
    }
}

# 错误处理
$ErrorActionPreference = "Stop"

# 执行主流程
Main
