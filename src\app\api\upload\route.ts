import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'
import crypto from 'crypto'
import { 
  getClientIP, 
  checkRateLimit,
  logSecurityEvent,
  isAllowedFileType,
  generateSafeFilename 
} from '@/lib/security'

// 允许的文件类型
const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/webp',
  'application/pdf'
]

// 最大文件大小 (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024

// 上传目录
const UPLOAD_DIR = path.join(process.cwd(), 'public', 'uploads')

export async function POST(request: NextRequest) {
  const ip = getClientIP(request)
  const userAgent = request.headers.get('user-agent') || 'unknown'
  
  try {
    // 速率限制检查 - 每个IP每小时最多10次上传
    const rateLimit = checkRateLimit(`upload_${ip}`, 10, 3600000)
    if (!rateLimit.allowed) {
      logSecurityEvent({
        type: 'rate_limit_exceeded',
        ip,
        userAgent,
        details: { endpoint: '/api/upload', limit: 10 }
      })
      
      return NextResponse.json(
        { success: false, message: '上传过于频繁，请1小时后再试' },
        { status: 429 }
      )
    }
    
    // 检查Content-Type
    const contentType = request.headers.get('content-type')
    if (!contentType || !contentType.includes('multipart/form-data')) {
      return NextResponse.json(
        { success: false, message: '无效的请求格式' },
        { status: 400 }
      )
    }
    
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { success: false, message: '未找到上传文件' },
        { status: 400 }
      )
    }
    
    // 验证文件大小
    if (file.size > MAX_FILE_SIZE) {
      logSecurityEvent({
        type: 'suspicious_activity',
        ip,
        userAgent,
        details: { reason: 'file_too_large', size: file.size, filename: file.name }
      })
      
      return NextResponse.json(
        { success: false, message: '文件大小超过限制（最大10MB）' },
        { status: 400 }
      )
    }
    
    // 验证文件类型
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      logSecurityEvent({
        type: 'suspicious_activity',
        ip,
        userAgent,
        details: { reason: 'invalid_file_type', type: file.type, filename: file.name }
      })
      
      return NextResponse.json(
        { success: false, message: '不支持的文件类型' },
        { status: 400 }
      )
    }
    
    // 验证文件扩展名
    if (!isAllowedFileType(file.name, ALLOWED_FILE_TYPES)) {
      logSecurityEvent({
        type: 'suspicious_activity',
        ip,
        userAgent,
        details: { reason: 'invalid_file_extension', filename: file.name }
      })
      
      return NextResponse.json(
        { success: false, message: '不支持的文件扩展名' },
        { status: 400 }
      )
    }
    
    // 检查文件内容（魔数验证）
    const buffer = Buffer.from(await file.arrayBuffer())
    if (!isValidFileContent(buffer, file.type)) {
      logSecurityEvent({
        type: 'suspicious_activity',
        ip,
        userAgent,
        details: { reason: 'invalid_file_content', filename: file.name, type: file.type }
      })
      
      return NextResponse.json(
        { success: false, message: '文件内容验证失败' },
        { status: 400 }
      )
    }
    
    // 生成安全的文件名
    const safeFilename = generateSafeFilename(file.name)
    
    // 确保上传目录存在
    if (!existsSync(UPLOAD_DIR)) {
      await mkdir(UPLOAD_DIR, { recursive: true })
    }
    
    // 保存文件
    const filePath = path.join(UPLOAD_DIR, safeFilename)
    await writeFile(filePath, buffer)
    
    // 记录上传事件
    logSecurityEvent({
      type: 'file_upload',
      ip,
      userAgent,
      details: { 
        filename: safeFilename, 
        originalName: file.name,
        size: file.size,
        type: file.type
      }
    })
    
    return NextResponse.json({
      success: true,
      message: '文件上传成功',
      filename: safeFilename,
      url: `/uploads/${safeFilename}`
    })
    
  } catch (error) {
    console.error('文件上传失败:', error)
    
    logSecurityEvent({
      type: 'suspicious_activity',
      ip,
      userAgent,
      details: { error: 'file_upload_error', message: String(error) }
    })
    
    return NextResponse.json(
      { success: false, message: '文件上传失败' },
      { status: 500 }
    )
  }
}

/**
 * 验证文件内容（魔数检查）
 */
function isValidFileContent(buffer: Buffer, mimeType: string): boolean {
  if (buffer.length < 4) return false
  
  const header = buffer.subarray(0, 4)
  
  switch (mimeType) {
    case 'image/jpeg':
      // JPEG 魔数: FF D8 FF
      return header[0] === 0xFF && header[1] === 0xD8 && header[2] === 0xFF
    
    case 'image/png':
      // PNG 魔数: 89 50 4E 47
      return header[0] === 0x89 && header[1] === 0x50 && 
             header[2] === 0x4E && header[3] === 0x47
    
    case 'image/webp':
      // WebP 魔数: 52 49 46 46 (RIFF)
      return header[0] === 0x52 && header[1] === 0x49 && 
             header[2] === 0x46 && header[3] === 0x46
    
    case 'application/pdf':
      // PDF 魔数: 25 50 44 46 (%PDF)
      return header[0] === 0x25 && header[1] === 0x50 && 
             header[2] === 0x44 && header[3] === 0x46
    
    default:
      return false
  }
}

/**
 * 扫描文件中的恶意内容
 */
function scanForMaliciousContent(buffer: Buffer): boolean {
  const content = buffer.toString('utf8', 0, Math.min(buffer.length, 1024))
  
  // 检查常见的恶意脚本模式
  const maliciousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+=/i,
    /eval\(/i,
    /exec\(/i,
    /system\(/i,
    /shell_exec/i,
    /<?php/i,
    /<%/i
  ]
  
  return maliciousPatterns.some(pattern => pattern.test(content))
}
