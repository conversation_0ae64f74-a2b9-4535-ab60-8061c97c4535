/**
 * 上海荷阁科技 - 导航组件单元测试
 * 测试 Navigation 组件的基本功能
 */

import { render, screen } from "@testing-library/react";
import Navigation from "../Navigation";

// Mock Next.js router
jest.mock("next/navigation", () => ({
  usePathname: () => "/",
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
}));

describe("Navigation Component", () => {
  it("should render navigation component", () => {
    render(<Navigation />);

    // 检查是否渲染了导航元素
    const nav = screen.getByRole("navigation");
    expect(nav).toBeInTheDocument();
  });

  it("should render logo or brand name", () => {
    render(<Navigation />);

    // 检查是否有品牌相关的文本或图片
    // 这里需要根据实际的 Navigation 组件内容调整
    const navigation = screen.getByRole("navigation");
    expect(navigation).toBeInTheDocument();
  });
});
