# PowerShell 编码问题修复方案

## 问题描述

在 GitLab CI/CD 的 Windows 环境中，PowerShell 执行时出现以下编码和语法错误：

1. **编码问题**：包含 emoji 和中文字符的环境变量导致 PowerShell 解析错误
2. **语法解析错误**：特殊字符（如 `<`、`"`、`}`）在 PowerShell 中被错误解析
3. **文件路径问题**：PowerShell 脚本文件路径解析失败

## 错误示例

```
位置 行:228 字符: 25
+ $CI_COMMIT_DESCRIPTION="`n🐛 关键修复:`n- 移除所有可能导致编码问题的 emoji 字符...
+                         ~~~~
意外的标记"`n🐛"。
```

## 解决方案

### 1. 修复 GitLab CI/CD YAML 语法错误

**错误信息**：

```
jobs:deploy:local:script config should be a string or a nested array of strings up to 10 levels deep
```

**问题原因**：
在 PowerShell 脚本中使用了不正确的变量赋值语法，导致 YAML 解析器无法正确处理脚本内容。

**修复方法**：

- 移除 PowerShell 变量赋值语句（如 `$imageName = $env:LOCAL_IMAGE_NAME`）
- 直接使用环境变量（如 `$env:LOCAL_IMAGE_NAME`）
- 修复字符串插值语法

**修复前**：

```yaml
script:
  - $imageName = $env:LOCAL_IMAGE_NAME
  - $containerName = $env:CONTAINER_NAME
  - docker run -d --name $containerName -p "${hostPort}:${containerPort}" "${imageName}:latest"
```

**修复后**：

```yaml
script:
  - echo "Deploying to local Docker environment..."
  - docker stop $env:CONTAINER_NAME 2>$null || echo "Container does not exist"
  - docker run -d --name $env:CONTAINER_NAME -p "$env:HOST_PORT`:$env:CONTAINER_PORT" "$env:LOCAL_IMAGE_NAME`:latest"
```

### 2. 移除所有 Emoji 和特殊字符

**修改文件**：

- `.gitlab-ci.yml`
- `.cicd/scripts/install-dependencies.ps1`
- `.cicd/scripts/generate-docker-report.ps1`
- `.cicd/scripts/verify-local-deployment.ps1`

**修改内容**：

- 将所有包含 emoji 的 echo 语句改为纯英文
- 移除中文注释和输出信息
- 使用标准 ASCII 字符

### 2. 修复 PowerShell 脚本调用方式

**原始调用方式**：

```yaml
- "PowerShell -ExecutionPolicy Bypass -File .cicd/scripts/install-dependencies.ps1"
```

**修复后调用方式**：

```yaml
- PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\install-dependencies.ps1"
```

**关键改进**：

- 使用 `PowerShell.exe` 而不是 `PowerShell`
- 使用 Windows 路径分隔符 `\` 而不是 `/`
- 为文件路径添加双引号保护

### 3. 添加 PowerShell 环境设置脚本

**新增文件**：`.cicd/scripts/setup-powershell-environment.ps1`

**功能**：

- 设置控制台编码为 UTF-8
- 配置 PowerShell 执行策略
- 设置环境变量以提高兼容性
- 显示环境信息用于调试

### 4. 更新 GitLab CI/CD 配置

**在 `default.before_script` 中添加**：

```yaml
before_script:
  - PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\setup-powershell-environment.ps1"
  - echo "Starting CI/CD Pipeline - $(Get-Date)"
  # ... 其他命令
```

## 修复效果

### 修复前的问题

- PowerShell 解析错误导致 CI/CD 流水线失败
- 编码问题导致环境变量无法正确设置
- 文件路径解析失败

### 修复后的改进

- 所有 PowerShell 脚本使用标准 ASCII 字符
- 正确的文件路径格式和调用方式
- 统一的编码环境设置
- 更好的错误处理和调试信息

## 最佳实践

### 1. PowerShell 脚本编写规范

- 避免使用 emoji 和非 ASCII 字符
- 使用英文注释和输出信息
- 正确处理文件路径和特殊字符

### 2. GitLab CI/CD Windows 环境配置

- 使用 `PowerShell.exe` 完整路径
- 为文件路径添加引号保护
- 使用 Windows 路径分隔符
- 在脚本开始前设置正确的编码环境

### 3. 调试和监控

- 添加详细的日志输出
- 显示环境信息用于问题排查
- 使用标准化的错误处理

## 验证方法

1. **检查 PowerShell 环境**：

   ```powershell
   PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\setup-powershell-environment.ps1"
   ```

2. **测试依赖安装**：

   ```powershell
   PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\install-dependencies.ps1"
   ```

3. **运行 GitLab CI/CD 流水线**：
   - 推送代码到 `ci` 分支
   - 观察流水线执行情况
   - 检查日志输出是否正常

## 注意事项

1. **编码一致性**：确保所有脚本文件使用 UTF-8 编码保存
2. **路径格式**：在 Windows 环境中使用反斜杠 `\` 作为路径分隔符
3. **引号保护**：为包含空格或特殊字符的路径添加双引号
4. **执行策略**：确保 PowerShell 执行策略允许脚本运行

## 相关文档

- [GitLab CI/CD Windows 环境配置](./windows-unified-cicd.md)
- [PowerShell 最佳实践](./best-practices.md)
- [故障排除指南](./troubleshooting.md)
