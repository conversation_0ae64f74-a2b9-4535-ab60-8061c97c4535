npm WARN config cache-max This option has been deprecated in favor of `--prefer-online`

> enterprise-website@0.1.0 lint
> next lint


./src/app/about/page.tsx
41:6  Warning: React Hook useEffect has a missing dependency: 'menuItems'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/app/api/upload/route.ts
139:11  Warning: Found writeFile from package "fs/promises" with non literal argument at index 0  security/detect-non-literal-fs-filename

./src/app/news-integrated/page.tsx
108:6  Warning: React Hook useEffect has a missing dependency: 'fetchIntegratedNews'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/lib/security-config.ts
251:1  Warning: Assign object to a variable before exporting as module default  import/no-anonymous-default-export

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/basic-features/eslint#disabling-rules
