import { NextRequest, NextResponse } from "next/server";
import {
  sanitizeInput,
  getClientIP,
  checkRateLimit,
  logSecurityEvent,
  isValidUrl,
} from "@/lib/security";

interface VisitorData {
  visitCount: number;
  currentPage: string;
  timeOnPage: number;
  referrer: string;
  userAgent: string;
  timestamp?: string;
  ip?: string;
  sessionId?: string;
}

interface VisitorStats {
  totalVisits: number;
  uniqueVisitors: number;
  pageViews: { [key: string]: number };
  referrers: { [key: string]: number };
  userAgents: { [key: string]: number };
  visitTimes: string[];
}

// 简单的内存存储（生产环境应使用数据库）
let visitorStats: VisitorStats = {
  totalVisits: 0,
  uniqueVisitors: 0,
  pageViews: {},
  referrers: {},
  userAgents: {},
  visitTimes: [],
};

let visitorSessions = new Set<string>();

export async function POST(request: NextRequest) {
  const ip = getClientIP(request);
  const userAgent = request.headers.get("user-agent") || "unknown";

  try {
    // 速率限制检查 - 每个IP每分钟最多60次请求
    const rateLimit = checkRateLimit(`visitor_${ip}`, 60, 60000);
    if (!rateLimit.allowed) {
      logSecurityEvent({
        type: "rate_limit_exceeded",
        ip,
        userAgent,
        details: { endpoint: "/api/visitor-stats", limit: 60 },
      });

      return NextResponse.json(
        { success: false, message: "请求过于频繁" },
        { status: 429 },
      );
    }

    const data: VisitorData = await request.json();

    // 输入验证和清理
    const cleanData = {
      currentPage: sanitizeInput(data.currentPage),
      referrer: sanitizeInput(data.referrer),
      userAgent: sanitizeInput(data.userAgent),
      visitCount: Math.max(0, Math.min(1000, Number(data.visitCount) || 0)),
      timeOnPage: Math.max(0, Math.min(3600000, Number(data.timeOnPage) || 0)),
    };

    // 验证页面路径
    if (!cleanData.currentPage || cleanData.currentPage.length > 200) {
      return NextResponse.json(
        { success: false, message: "无效的页面路径" },
        { status: 400 },
      );
    }

    // 验证referrer URL（如果提供）
    if (cleanData.referrer && !isValidUrl(cleanData.referrer)) {
      cleanData.referrer = ""; // 清空无效的referrer
    }

    // 检查是否为可疑的访问模式
    if (isSuspiciousVisit(cleanData, ip)) {
      logSecurityEvent({
        type: "suspicious_activity",
        ip,
        userAgent,
        details: { reason: "suspicious_visit_pattern", data: cleanData },
      });

      // 仍然记录统计，但不返回详细信息
      return NextResponse.json({
        success: true,
        message: "访问统计已记录",
      });
    }

    // 生成会话ID
    const sessionId = `${ip}-${cleanData.userAgent.slice(0, 50)}`;

    // 更新统计数据
    visitorStats.totalVisits++;

    // 统计唯一访客
    if (!visitorSessions.has(sessionId)) {
      visitorSessions.add(sessionId);
      visitorStats.uniqueVisitors++;
    }

    // 统计页面访问
    visitorStats.pageViews[cleanData.currentPage] =
      (visitorStats.pageViews[cleanData.currentPage] || 0) + 1;

    // 统计来源
    if (cleanData.referrer) {
      try {
        const referrerDomain = new URL(cleanData.referrer).hostname;
        visitorStats.referrers[referrerDomain] =
          (visitorStats.referrers[referrerDomain] || 0) + 1;
      } catch {
        // 忽略无效的referrer URL
      }
    }

    // 统计用户代理（浏览器类型）
    const browserInfo = getBrowserInfo(cleanData.userAgent);
    visitorStats.userAgents[browserInfo] =
      (visitorStats.userAgents[browserInfo] || 0) + 1;

    // 记录访问时间
    visitorStats.visitTimes.push(new Date().toISOString());

    // 保持最近1000条访问记录
    if (visitorStats.visitTimes.length > 1000) {
      visitorStats.visitTimes = visitorStats.visitTimes.slice(-1000);
    }

    // 记录详细访问日志（仅开发环境）
    if (process.env.NODE_ENV === "development") {
      console.log(
        `访客统计: IP=${ip}, 页面=${cleanData.currentPage}, 访问次数=${cleanData.visitCount}`,
      );
    }

    return NextResponse.json({
      success: true,
      message: "访问统计已记录",
      sessionId: sessionId.slice(0, 16) + "...", // 只返回部分sessionId
    });
  } catch (error) {
    console.error("访问统计记录失败:", error);

    logSecurityEvent({
      type: "suspicious_activity",
      ip,
      userAgent,
      details: { error: "visitor_stats_error", message: String(error) },
    });

    return NextResponse.json(
      { success: false, message: "统计记录失败" },
      { status: 500 },
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type") || "summary";

    switch (type) {
      case "summary":
        return NextResponse.json({
          totalVisits: visitorStats.totalVisits,
          uniqueVisitors: visitorStats.uniqueVisitors,
          topPages: getTopEntries(visitorStats.pageViews, 5),
          topReferrers: getTopEntries(visitorStats.referrers, 5),
          topBrowsers: getTopEntries(visitorStats.userAgents, 5),
          recentVisits: visitorStats.visitTimes.slice(-10),
        });

      case "pages":
        return NextResponse.json(visitorStats.pageViews);

      case "referrers":
        return NextResponse.json(visitorStats.referrers);

      case "browsers":
        return NextResponse.json(visitorStats.userAgents);

      case "timeline":
        return NextResponse.json({
          visitTimes: visitorStats.visitTimes.slice(-100),
        });

      default:
        return NextResponse.json(visitorStats);
    }
  } catch (error) {
    console.error("获取访问统计失败:", error);
    return NextResponse.json(
      { success: false, message: "获取统计数据失败" },
      { status: 500 },
    );
  }
}

// 辅助函数：获取浏览器信息
function getBrowserInfo(userAgent: string): string {
  if (userAgent.includes("Chrome")) return "Chrome";
  if (userAgent.includes("Firefox")) return "Firefox";
  if (userAgent.includes("Safari") && !userAgent.includes("Chrome"))
    return "Safari";
  if (userAgent.includes("Edge")) return "Edge";
  if (userAgent.includes("Opera")) return "Opera";
  if (userAgent.includes("WeChat")) return "WeChat";
  return "Other";
}

// 辅助函数：获取排名前N的条目
function getTopEntries(obj: { [key: string]: number }, limit: number) {
  return Object.entries(obj)
    .sort(([, a], [, b]) => b - a)
    .slice(0, limit)
    .map(([key, value]) => ({ name: key, count: value }));
}

/**
 * 检查是否为可疑的访问模式
 */
function isSuspiciousVisit(data: any, ip: string): boolean {
  // 检查访问频率是否异常
  const recentVisits = visitorStats.visitTimes.filter(
    (time) => Date.now() - new Date(time).getTime() < 60000, // 最近1分钟
  ).length;

  if (recentVisits > 100) {
    return true;
  }

  // 检查User-Agent是否可疑
  const suspiciousUA = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
  ];

  // 允许的搜索引擎爬虫
  const allowedBots = [/googlebot/i, /bingbot/i, /baiduspider/i, /yandexbot/i];

  // 检查是否为允许的爬虫
  if (allowedBots.some((pattern) => pattern.test(data.userAgent))) {
    return false;
  }

  if (suspiciousUA.some((pattern) => pattern.test(data.userAgent))) {
    return true;
  }

  // 检查页面路径是否可疑
  const suspiciousPaths = [/admin/i, /login/i, /wp-/i, /\.php$/i, /\.asp$/i];

  if (suspiciousPaths.some((pattern) => pattern.test(data.currentPage))) {
    return true;
  }

  return false;
}

// 定期清理旧数据（可选）
setInterval(
  () => {
    const oneWeekAgo = new Date(
      Date.now() - 7 * 24 * 60 * 60 * 1000,
    ).toISOString();
    visitorStats.visitTimes = visitorStats.visitTimes.filter(
      (time) => time > oneWeekAgo,
    );
  },
  24 * 60 * 60 * 1000,
); // 每天清理一次
