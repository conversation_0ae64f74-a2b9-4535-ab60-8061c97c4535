import { NextResponse } from 'next/server'

// 模拟从荷阁科技官网爬取的案例数据
// 实际部署时可以替换为真实的爬虫逻辑
export async function GET() {
  try {
    // 这里可以添加真实的爬虫逻辑来获取 https://www.hege-tech.cn/case_show/list/14/14.html 的数据
    // 目前使用模拟数据，基于荷阁科技的实际业务场景
    
    const successCases = [
      {
        id: 1,
        title: "比亚迪智能座舱3D HMI系统",
        client: "比亚迪汽车",
        category: "智能座舱",
        description: "为比亚迪新能源汽车打造的下一代3D智能座舱人机交互系统，实现一镜到底的沉浸式体验。",
        challenge: "传统车机界面交互单一，用户体验不佳，需要打造具有科技感和未来感的3D交互界面。",
        solution: "采用Unity 3D引擎开发，结合自研的3D HMI框架，实现流畅的3D界面切换和交互动画。",
        results: [
          "用户满意度提升85%",
          "界面响应速度提升60%",
          "交互效率提升40%"
        ],
        technologies: ["Unity 3D", "C#", "NGUI", "车载Android", "CAN总线"],
        duration: "8个月",
        teamSize: "12人",
        image: "/our customers/比亚迪.svg",
        achievements: [
          { metric: "性能提升", value: "60%", description: "界面响应速度" },
          { metric: "用户满意度", value: "85%", description: "体验评分提升" },
          { metric: "开发效率", value: "40%", description: "迭代速度提升" }
        ],
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15"
      },
      {
        id: 2,
        title: "吉利汽车云端数字座舱平台",
        client: "吉利汽车",
        category: "云服务",
        description: "构建基于云端的数字座舱服务平台，支持OTA升级和个性化服务推送。",
        challenge: "需要构建稳定可靠的云端服务架构，支持百万级车辆的并发访问和数据处理。",
        solution: "采用腾讯云基础设施，构建微服务架构，实现弹性扩容和高可用部署。",
        results: [
          "支持100万+车辆并发",
          "服务可用性99.9%",
          "数据处理能力提升300%"
        ],
        technologies: ["腾讯云", "Kubernetes", "微服务", "Redis", "MongoDB"],
        duration: "10个月",
        teamSize: "15人",
        image: "/our customers/吉利.svg",
        achievements: [
          { metric: "并发处理", value: "100万+", description: "车辆同时在线" },
          { metric: "可用性", value: "99.9%", description: "服务稳定性" },
          { metric: "性能提升", value: "300%", description: "数据处理能力" }
        ],
        createdAt: "2024-02-20",
        updatedAt: "2024-02-20"
      },
      {
        id: 3,
        title: "长安汽车AI数字人助手",
        client: "长安汽车",
        category: "人工智能",
        description: "开发智能AI数字人助手，为用户提供24小时在线咨询和车辆控制服务。",
        challenge: "需要实现自然语言理解、语音识别和智能对话，同时集成车辆控制功能。",
        solution: "结合深度学习算法和NLP技术，开发多模态交互的AI数字人系统。",
        results: [
          "语音识别准确率95%+",
          "用户问题解决率90%+",
          "服务响应时间<2秒"
        ],
        technologies: ["深度学习", "NLP", "语音识别", "Unity", "Python"],
        duration: "6个月",
        teamSize: "10人",
        image: "/our customers/长安.svg",
        achievements: [
          { metric: "识别准确率", value: "95%+", description: "语音识别精度" },
          { metric: "问题解决率", value: "90%+", description: "用户满意度" },
          { metric: "响应时间", value: "<2秒", description: "服务响应速度" }
        ],
        createdAt: "2024-03-10",
        updatedAt: "2024-03-10"
      },
      {
        id: 4,
        title: "长城汽车网络安全防护系统",
        client: "长城汽车",
        category: "网络安全",
        description: "构建车联网安全防护体系，保护车辆数据和用户隐私安全。",
        challenge: "车联网环境复杂，需要防范多种网络攻击，确保车辆和用户数据安全。",
        solution: "部署多层次安全防护体系，包括入侵检测、数据加密和安全审计。",
        results: [
          "安全事件检测率99%+",
          "数据泄露风险降低95%",
          "合规性评估100%通过"
        ],
        technologies: ["网络安全", "数据加密", "入侵检测", "安全审计", "区块链"],
        duration: "12个月",
        teamSize: "8人",
        image: "/our customers/长城.svg",
        achievements: [
          { metric: "检测率", value: "99%+", description: "安全事件识别" },
          { metric: "风险降低", value: "95%", description: "数据泄露防护" },
          { metric: "合规通过", value: "100%", description: "安全标准认证" }
        ],
        createdAt: "2024-04-05",
        updatedAt: "2024-04-05"
      },
      {
        id: 5,
        title: "一汽奔腾智能制造数字化平台",
        client: "一汽奔腾",
        category: "数字化转型",
        description: "打造智能制造数字化平台，实现生产流程的数字化管理和优化。",
        challenge: "传统制造流程效率低下，需要实现生产数据的实时监控和智能分析。",
        solution: "构建IoT数据采集平台，结合大数据分析实现生产流程的智能化管理。",
        results: [
          "生产效率提升45%",
          "质量缺陷率降低60%",
          "运营成本节省30%"
        ],
        technologies: ["IoT", "大数据", "云计算", "机器学习", "数据可视化"],
        duration: "14个月",
        teamSize: "18人",
        image: "/our customers/一汽奔腾.svg",
        achievements: [
          { metric: "效率提升", value: "45%", description: "生产流程优化" },
          { metric: "质量改善", value: "60%", description: "缺陷率降低" },
          { metric: "成本节省", value: "30%", description: "运营成本优化" }
        ],
        createdAt: "2024-05-12",
        updatedAt: "2024-05-12"
      },
      {
        id: 6,
        title: "东风汽车车联网大数据平台",
        client: "东风汽车",
        category: "大数据",
        description: "构建车联网大数据分析平台，为产品优化和用户服务提供数据支撑。",
        challenge: "海量车辆数据需要实时处理和分析，为业务决策提供准确的数据洞察。",
        solution: "采用分布式大数据架构，实现TB级数据的实时处理和智能分析。",
        results: [
          "数据处理能力10TB/天",
          "分析响应时间<5秒",
          "业务洞察准确率90%+"
        ],
        technologies: ["Hadoop", "Spark", "Kafka", "ElasticSearch", "机器学习"],
        duration: "9个月",
        teamSize: "14人",
        image: "/our customers/东风.svg",
        achievements: [
          { metric: "处理能力", value: "10TB/天", description: "数据处理规模" },
          { metric: "响应时间", value: "<5秒", description: "查询分析速度" },
          { metric: "准确率", value: "90%+", description: "业务洞察精度" }
        ],
        createdAt: "2024-06-18",
        updatedAt: "2024-06-18"
      }
    ]

    // 添加统计信息
    const statistics = {
      totalCases: successCases.length,
      totalClients: new Set(successCases.map(c => c.client)).size,
      averageSatisfaction: "95%+",
      totalExperts: "100+",
      lastUpdated: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      data: {
        cases: successCases,
        statistics
      },
      message: "成功案例数据获取成功"
    })

  } catch (error) {
    console.error('获取成功案例数据失败:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: '获取成功案例数据失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

// 支持POST请求来更新案例数据（管理员功能）
export async function POST(request: Request) {
  try {
    const body = await request.json()
    
    // 这里可以添加身份验证逻辑
    // 验证管理员权限
    
    // 这里可以添加数据验证逻辑
    // 验证案例数据格式
    
    // 这里可以添加数据库操作逻辑
    // 保存新的案例数据
    
    return NextResponse.json({
      success: true,
      message: "案例数据更新成功",
      data: body
    })

  } catch (error) {
    console.error('更新成功案例数据失败:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: '更新成功案例数据失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
