# 🚀 上海荷阁科技 CI/CD 完整解决方案

欢迎使用上海荷阁科技企业官网的完整 CI/CD 解决方案！本解决方案为 Windows 环境下的 GitLab CI/CD 提供了企业级的容器化部署、安全扫描和自动化测试报告功能。

## 📋 解决方案概览

### 🎯 核心特性

- ✅ **完整的 CI/CD 流水线**：从代码提交到生产部署的全自动化流程
- ✅ **容器化部署**：基于 Docker 的现代化部署方案
- ✅ **多层安全扫描**：SAST、依赖扫描、容器安全扫描
- ✅ **全面测试覆盖**：单元测试、集成测试、E2E 测试、性能测试
- ✅ **Windows 环境优化**：专门针对 Windows 环境的配置和脚本
- ✅ **自动化报告**：测试覆盖率、安全扫描、性能分析报告
- ✅ **监控和告警**：Prometheus + Grafana 监控体系
- ✅ **蓝绿部署**：零停机部署策略

### 🏗️ 技术栈

- **前端框架**：Next.js 14 + React 18 + TypeScript
- **样式方案**：Tailwind CSS
- **数据库**：PostgreSQL + Prisma ORM
- **缓存**：Redis
- **容器化**：Docker + Docker Compose
- **CI/CD**：GitLab CI/CD
- **监控**：Prometheus + Grafana
- **测试**：Jest + Playwright + Lighthouse
- **安全扫描**：ESLint Security + Trivy + npm audit

## 📁 目录结构

```
.cicd/
├── README.md                    # 本文件
├── gitlab-ci/                   # GitLab CI/CD 配置
│   └── .gitlab-ci.yml          # 主 CI/CD 配置
├── docker/                     # Docker 容器化配置
│   ├── Dockerfile              # 应用容器镜像
│   ├── Dockerfile.nginx        # Nginx 反向代理镜像
│   ├── docker-compose.yml      # 开发环境
│   ├── docker-compose.prod.yml # 生产环境
│   ├── scripts/                # Docker 脚本
│   └── nginx/                  # Nginx 配置
├── security/                   # 安全扫描配置
│   ├── sast/                   # 静态应用安全测试
│   ├── dependency/             # 依赖漏洞扫描
│   └── container/              # 容器安全扫描
├── testing/                    # 测试配置
│   ├── jest/                   # 单元测试配置
│   ├── e2e/                    # 端到端测试配置
│   └── performance/            # 性能测试配置
├── deploy/                     # 部署配置
│   ├── scripts/                # 部署脚本
│   ├── k8s/                    # Kubernetes 配置
│   └── monitoring/             # 监控配置
├── windows/                    # Windows 环境配置
│   ├── runner/                 # GitLab Runner 配置
│   └── scripts/                # Windows 脚本
└── docs/                       # 文档
    ├── setup-guide.md          # 设置指南
    ├── troubleshooting.md      # 故障排除
    └── best-practices.md       # 最佳实践
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone http://home.spinach.cool:11991/hege-tech/hege-tech-web.git
cd hege-tech-web

# 复制 CI/CD 配置
cp .cicd/gitlab-ci/.gitlab-ci.yml .gitlab-ci.yml

# 启动本地开发环境
docker-compose -f .cicd/docker/docker-compose.yml up -d
```

### 2. GitLab CI/CD 设置

在 GitLab 项目设置中配置以下环境变量：

```bash
# 必需变量
DOCKER_REGISTRY=registry.gitlab.com/hege-tech/hege-tech-web
SSH_PRIVATE_KEY=<your-ssh-private-key>
DATABASE_URL=postgresql://user:password@localhost:5432/hege_tech_db
NEXTAUTH_SECRET=<random-secret-key>

# 部署服务器
STAGING_HOST=staging.hege-tech.cn
PRODUCTION_HOST=hege-tech.cn
```

### 3. Windows 环境设置

```powershell
# 以管理员身份运行 PowerShell
.\.cicd\windows\scripts\setup.ps1 -InstallAll

# 安装 GitLab Runner
.\.cicd\windows\runner\install.ps1 -RegistrationToken "your-token"
```

## 🔄 CI/CD 流水线

### 流水线阶段

```mermaid
graph LR
    A[Prepare] --> B[Test]
    B --> C[Security]
    C --> D[Build]
    D --> E[Deploy]
    E --> F[Report]
```

1. **Prepare**：环境准备和依赖安装
2. **Test**：单元测试、集成测试、代码检查
3. **Security**：安全扫描和漏洞检测
4. **Build**：应用构建和 Docker 镜像构建
5. **Deploy**：部署到测试/生产环境
6. **Report**：生成测试和安全报告

### 主要作业

- `prepare:dependencies` - 安装项目依赖
- `test:unit` - 运行单元测试
- `test:lint` - 代码质量检查
- `security:sast` - 静态应用安全测试
- `security:dependency-scan` - 依赖漏洞扫描
- `build:app` - 构建 Next.js 应用
- `build:docker` - 构建 Docker 镜像
- `deploy:staging` - 部署到测试环境
- `deploy:production` - 部署到生产环境

## 🐳 容器化部署

### 多阶段构建

```dockerfile
# 阶段 1: 依赖安装
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# 阶段 2: 应用构建
FROM node:18-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

# 阶段 3: 运行时镜像
FROM node:18-alpine AS runner
WORKDIR /app
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
USER nextjs
EXPOSE 3000
CMD ["node", "server.js"]
```

### 服务编排

- **应用服务**：Next.js 应用容器
- **数据库服务**：PostgreSQL 数据库
- **缓存服务**：Redis 缓存
- **代理服务**：Nginx 反向代理
- **监控服务**：Prometheus + Grafana

## 🔒 安全扫描

### 多层安全检查

1. **静态代码分析 (SAST)**
   - ESLint 安全规则
   - TypeScript 类型安全
   - 代码质量检查

2. **依赖漏洞扫描**
   - npm audit 漏洞检查
   - 许可证合规性检查
   - 自动安全更新

3. **容器安全扫描**
   - Trivy 镜像扫描
   - Hadolint Dockerfile 检查
   - 基础镜像漏洞检测

### 安全报告

- GitLab 安全仪表板集成
- SARIF 格式报告
- 自动安全告警

## 🧪 测试策略

### 测试金字塔

```
    E2E Tests (10%)
   ┌─────────────────┐
   │  Integration    │ (20%)
   │     Tests       │
   ├─────────────────┤
   │   Unit Tests    │ (70%)
   └─────────────────┘
```

### 测试工具

- **单元测试**：Jest + Testing Library
- **E2E 测试**：Playwright
- **性能测试**：Lighthouse CI
- **覆盖率报告**：Istanbul + Cobertura

### 测试报告

- HTML 测试报告
- JUnit XML 格式
- 覆盖率趋势分析
- 性能指标监控

## 📊 监控和告警

### 监控指标

- **应用指标**：响应时间、错误率、吞吐量
- **系统指标**：CPU、内存、磁盘使用率
- **业务指标**：用户访问量、转化率

### 告警规则

- 高错误率告警
- 响应时间异常告警
- 资源使用率告警
- 服务可用性告警

## 🪟 Windows 环境支持

### 专门优化

- PowerShell 脚本支持
- Windows 路径处理
- GitLab Runner Windows 配置
- Docker Desktop 集成

### 自动化脚本

- 开发环境一键设置
- GitLab Runner 自动安装
- 构建和部署脚本

## 📈 性能优化

### 构建优化

- 多阶段 Docker 构建
- 依赖缓存策略
- 并行作业执行
- 增量构建支持

### 运行时优化

- Next.js 性能优化
- 静态资源 CDN
- 数据库查询优化
- Redis 缓存策略

## 🔧 使用指南

### 开发流程

1. **功能开发**：在 feature 分支开发新功能
2. **代码提交**：遵循 Conventional Commits 规范
3. **合并请求**：创建 MR 触发 CI/CD 流水线
4. **自动测试**：运行完整的测试套件
5. **安全扫描**：执行多层安全检查
6. **自动部署**：部署到测试环境
7. **生产发布**：手动触发生产部署

### 常用命令

```bash
# 本地开发
npm run dev                    # 启动开发服务器
npm run build                  # 构建生产版本
npm run test                   # 运行测试
npm run lint                   # 代码检查

# Docker 操作
docker-compose up -d           # 启动服务
docker-compose logs -f app     # 查看日志
docker-compose down            # 停止服务

# 部署操作
./.cicd/deploy/scripts/deploy-staging.sh    # 部署测试环境
./.cicd/deploy/scripts/deploy-production.sh # 部署生产环境
```

## 📚 文档资源

- [设置指南](setup-guide.md) - 详细的设置步骤
- [故障排除](troubleshooting.md) - 常见问题解决方案
- [最佳实践](best-practices.md) - CI/CD 最佳实践

## 🆘 技术支持

如果您在使用过程中遇到问题，请联系：

- **邮箱**：<EMAIL>
- **电话**：18221165813
- **项目地址**：http://home.spinach.cool:11991/hege-tech/hege-tech-web

## 📄 许可证

© 2024 上海荷阁科技有限公司 - 保留所有权利

---

**感谢您选择上海荷阁科技的 CI/CD 解决方案！** 🎉

这个完整的 CI/CD 解决方案将帮助您实现：
- 🚀 快速、可靠的软件交付
- 🔒 企业级安全保障
- 📊 全面的质量监控
- 🔄 自动化的运维流程

让我们一起构建更好的软件！
