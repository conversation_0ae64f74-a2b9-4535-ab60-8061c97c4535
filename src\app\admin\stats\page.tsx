"use client";

import React, { useState, useEffect } from "react";
import {
  Users,
  Eye,
  MessageCircle,
  TrendingUp,
  Clock,
  Globe,
  Smartphone,
  RefreshCw,
} from "lucide-react";

interface VisitorStats {
  totalVisits: number;
  uniqueVisitors: number;
  pageViews: { [key: string]: number };
  referrers: { [key: string]: number };
  userAgents: { [key: string]: number };
  recentVisits: string[];
}

interface TopEntry {
  name: string;
  count: number;
}

const AdminStatsPage = () => {
  const [stats, setStats] = useState<VisitorStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/visitor-stats?type=summary");
      const data = await response.json();
      setStats(data);
      setLastUpdate(new Date());
    } catch (error) {
      console.error("获取统计数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    // 每30秒自动刷新数据
    const interval = setInterval(fetchStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString("zh-CN");
  };

  if (loading && !stats) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-gray-600">加载统计数据中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 头部 */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">访客统计面板</h1>
              <p className="text-gray-600 mt-2">荷阁科技网站访问数据分析</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-500">
                最后更新: {lastUpdate.toLocaleTimeString("zh-CN")}
              </div>
              <button
                onClick={fetchStats}
                disabled={loading}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 disabled:opacity-50"
              >
                <RefreshCw
                  className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
                />
                <span>刷新</span>
              </button>
            </div>
          </div>
        </div>

        {stats && (
          <>
            {/* 核心指标卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      总访问量
                    </p>
                    <p className="text-3xl font-bold text-gray-900">
                      {stats.totalVisits}
                    </p>
                  </div>
                  <div className="bg-blue-100 p-3 rounded-full">
                    <Eye className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      独立访客
                    </p>
                    <p className="text-3xl font-bold text-gray-900">
                      {stats.uniqueVisitors}
                    </p>
                  </div>
                  <div className="bg-green-100 p-3 rounded-full">
                    <Users className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      页面浏览量
                    </p>
                    <p className="text-3xl font-bold text-gray-900">
                      {Object.values(stats.pageViews).reduce(
                        (a, b) => a + b,
                        0,
                      )}
                    </p>
                  </div>
                  <div className="bg-purple-100 p-3 rounded-full">
                    <Globe className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      平均访问深度
                    </p>
                    <p className="text-3xl font-bold text-gray-900">
                      {stats.uniqueVisitors > 0
                        ? (
                            Object.values(stats.pageViews).reduce(
                              (a, b) => a + b,
                              0,
                            ) / stats.uniqueVisitors
                          ).toFixed(1)
                        : "0"}
                    </p>
                  </div>
                  <div className="bg-orange-100 p-3 rounded-full">
                    <TrendingUp className="h-6 w-6 text-orange-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* 详细统计 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* 热门页面 */}
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  热门页面
                </h3>
                <div className="space-y-3">
                  {Object.entries(stats.pageViews)
                    .sort(([, a], [, b]) => b - a)
                    .slice(0, 5)
                    .map(([page, count]) => (
                      <div
                        key={page}
                        className="flex items-center justify-between"
                      >
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">
                            {page || "首页"}
                          </p>
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                            <div
                              className="bg-blue-500 h-2 rounded-full"
                              style={{
                                width: `${(count / Math.max(...Object.values(stats.pageViews))) * 100}%`,
                              }}
                            ></div>
                          </div>
                        </div>
                        <span className="ml-4 text-sm font-semibold text-gray-600">
                          {count}
                        </span>
                      </div>
                    ))}
                </div>
              </div>

              {/* 浏览器统计 */}
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  浏览器分布
                </h3>
                <div className="space-y-3">
                  {Object.entries(stats.userAgents)
                    .sort(([, a], [, b]) => b - a)
                    .slice(0, 5)
                    .map(([browser, count]) => (
                      <div
                        key={browser}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center space-x-3">
                          <Smartphone className="h-4 w-4 text-gray-400" />
                          <span className="text-sm font-medium text-gray-900">
                            {browser}
                          </span>
                        </div>
                        <span className="text-sm font-semibold text-gray-600">
                          {count}
                        </span>
                      </div>
                    ))}
                </div>
              </div>
            </div>

            {/* 最近访问记录 */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                最近访问记录
              </h3>
              <div className="space-y-2">
                {stats.recentVisits.slice(0, 10).map((visit, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-3 py-2 border-b border-gray-100 last:border-b-0"
                  >
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      {formatTime(visit)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AdminStatsPage;
