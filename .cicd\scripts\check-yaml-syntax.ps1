# Shanghai Hege Technology - YAML Syntax Checker
# Detailed validation of .gitlab-ci.yml syntax

$ErrorActionPreference = "Stop"

Write-Host "Checking YAML syntax for GitLab CI/CD..." -ForegroundColor Green

try {
    $ciFile = ".gitlab-ci.yml"
    if (-not (Test-Path $ciFile)) {
        Write-Host "Error: .gitlab-ci.yml file not found" -ForegroundColor Red
        exit 1
    }

    $content = Get-Content $ciFile -Raw
    $lines = Get-Content $ciFile

    Write-Host "Checking for problematic patterns..." -ForegroundColor Yellow

    # Check for backtick usage in YAML strings
    $backtickMatches = $content | Select-String -Pattern '`' -AllMatches
    if ($backtickMatches) {
        Write-Host "Warning: Found backtick characters that may cause YAML parsing issues:" -ForegroundColor Red
        foreach ($match in $backtickMatches) {
            $lineNum = ($content.Substring(0, $match.Matches[0].Index) -split "`n").Count
            $lineContent = $lines[$lineNum-1].Trim()
            Write-Host "  Line $lineNum`: $lineContent" -ForegroundColor Yellow
        }
    }

    # Check for proper PowerShell variable syntax in scripts
    $scriptBlocks = $content -split "script:" | Select-Object -Skip 1
    $blockIndex = 0
    foreach ($block in $scriptBlocks) {
        $blockIndex++
        $blockLines = $block -split "`n" | Where-Object { $_.Trim().StartsWith("-") }
        
        foreach ($line in $blockLines) {
            # Check for problematic variable assignments
            if ($line -match '\$\w+\s*=\s*\$env:') {
                Write-Host "Error: Found PowerShell variable assignment in script block $blockIndex" -ForegroundColor Red
                Write-Host "  Line: $($line.Trim())" -ForegroundColor Yellow
                Write-Host "  Use environment variables directly instead" -ForegroundColor Yellow
            }
            
            # Check for problematic string interpolation
            if ($line -match '\$env:\w+`') {
                Write-Host "Warning: Found backtick in environment variable usage in script block $blockIndex" -ForegroundColor Yellow
                Write-Host "  Line: $($line.Trim())" -ForegroundColor Yellow
                Write-Host "  Consider using `$(`$env:VAR) syntax instead" -ForegroundColor Yellow
            }
        }
    }

    # Check for proper YAML structure
    Write-Host "Checking YAML structure..." -ForegroundColor Yellow
    
    # Check for proper job definitions
    $jobPattern = '^[a-zA-Z0-9_:-]+:\s*$'
    $currentJob = ""
    $lineNum = 0
    
    foreach ($line in $lines) {
        $lineNum++
        
        if ($line -match $jobPattern -and -not $line.Trim().StartsWith("#")) {
            $currentJob = $line.Split(":")[0].Trim()
            Write-Host "Found job: $currentJob" -ForegroundColor Cyan
        }
        
        # Check for script sections
        if ($line.Trim() -eq "script:" -and $currentJob) {
            Write-Host "  Found script section in job: $currentJob" -ForegroundColor White
        }
    }

    # Test PowerShell string interpolation syntax
    Write-Host "`nTesting PowerShell string interpolation..." -ForegroundColor Yellow
    
    $env:TEST_VAR1 = "test1"
    $env:TEST_VAR2 = "test2"
    
    try {
        $testString1 = "$($env:TEST_VAR1):$($env:TEST_VAR2)"
        Write-Host "✅ PowerShell subexpression syntax works: $testString1" -ForegroundColor Green
    } catch {
        Write-Host "❌ PowerShell subexpression syntax failed: $($_.Exception.Message)" -ForegroundColor Red
    }

    Write-Host "`nYAML syntax check completed" -ForegroundColor Green
    Write-Host "If no errors were reported above, the YAML should be valid" -ForegroundColor Green
}
catch {
    Write-Host "YAML syntax check failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
