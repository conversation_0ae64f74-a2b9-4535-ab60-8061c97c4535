// 用户相关类型
export interface User {
  id: string
  email: string
  name: string
  role: 'ADMIN' | 'EDITOR' | 'USER'
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

// 项目相关类型
export interface Project {
  id: string
  title: string
  description: string
  content: string
  images: string[]
  technologies: string[]
  category: ProjectCategory
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  featured: boolean
  clientName?: string
  projectUrl?: string
  duration?: string
  teamSize?: number
  createdAt: Date
  updatedAt: Date
}

export interface ProjectCategory {
  id: string
  name: string
  slug: string
  description?: string
  projects?: Project[]
}

// 文章相关类型
export interface Article {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  coverImage?: string
  category: ArticleCategory
  tags: string[]
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  featured: boolean
  author: User
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface ArticleCategory {
  id: string
  name: string
  slug: string
  description?: string
  articles?: Article[]
}

// 招聘相关类型
export interface JobPosition {
  id: string
  title: string
  department: string
  location: string
  type: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERNSHIP'
  level: 'JUNIOR' | 'MIDDLE' | 'SENIOR' | 'LEAD'
  description: string
  requirements: string[]
  benefits: string[]
  salaryRange?: string
  status: 'OPEN' | 'CLOSED' | 'DRAFT'
  featured: boolean
  createdAt: Date
  updatedAt: Date
}

// 媒体文件类型
export interface MediaFile {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  alt?: string
  caption?: string
  createdAt: Date
}

// 网站设置类型
export interface SiteSetting {
  id: string
  key: string
  value: string
  type: 'TEXT' | 'TEXTAREA' | 'JSON' | 'BOOLEAN' | 'NUMBER'
  description?: string
  updatedAt: Date
}

// 联系表单类型
export interface ContactForm {
  id: string
  name: string
  email: string
  phone?: string
  company?: string
  subject: string
  message: string
  status: 'NEW' | 'READ' | 'REPLIED' | 'ARCHIVED'
  createdAt: Date
}

// 统计数据类型
export interface Analytics {
  id: string
  page: string
  visitors: number
  pageViews: number
  date: Date
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 分页类型
export interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: Pagination
}

// 表单验证类型
export interface FormErrors {
  [key: string]: string | undefined
}

// 导航菜单类型
export interface NavItem {
  title: string
  href: string
  description?: string
  children?: NavItem[]
}

// 公司信息类型
export interface CompanyInfo {
  name: string
  logo: string
  description: string
  address: string
  phone: string
  email: string
  website: string
  socialMedia: {
    wechat?: string
    weibo?: string
    linkedin?: string
    github?: string
  }
  foundedYear: number
  employeeCount: string
  businessScope: string[]
}
