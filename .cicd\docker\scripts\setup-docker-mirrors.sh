#!/bin/bash
# 🚀 上海荷阁科技 - Docker 镜像源配置脚本
# 自动配置 Docker 镜像源以解决网络连接问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        echo "windows"
    else
        echo "unknown"
    fi
}

# 获取 Docker 配置目录
get_docker_config_dir() {
    local os=$(detect_os)
    
    case $os in
        "linux")
            echo "/etc/docker"
            ;;
        "macos")
            echo "$HOME/.docker"
            ;;
        "windows")
            echo "/c/ProgramData/Docker/config"
            ;;
        *)
            log_error "不支持的操作系统: $OSTYPE"
            exit 1
            ;;
    esac
}

# 备份现有配置
backup_config() {
    local config_dir=$1
    local daemon_json="$config_dir/daemon.json"
    
    if [[ -f "$daemon_json" ]]; then
        local backup_file="$daemon_json.backup.$(date +%Y%m%d_%H%M%S)"
        cp "$daemon_json" "$backup_file"
        log_success "✅ 已备份现有配置: $backup_file"
    fi
}

# 应用 Docker 配置
apply_docker_config() {
    local config_dir=$(get_docker_config_dir)
    local daemon_json="$config_dir/daemon.json"
    local source_config=".cicd/docker/daemon.json"
    
    log_info "Docker 配置目录: $config_dir"
    
    # 创建配置目录
    if [[ ! -d "$config_dir" ]]; then
        log_info "创建 Docker 配置目录: $config_dir"
        sudo mkdir -p "$config_dir"
    fi
    
    # 备份现有配置
    backup_config "$config_dir"
    
    # 复制新配置
    if [[ -f "$source_config" ]]; then
        log_info "应用 Docker 镜像源配置..."
        sudo cp "$source_config" "$daemon_json"
        log_success "✅ Docker 配置已更新"
    else
        log_error "❌ 源配置文件不存在: $source_config"
        exit 1
    fi
}

# 重启 Docker 服务
restart_docker() {
    local os=$(detect_os)
    
    log_info "重启 Docker 服务..."
    
    case $os in
        "linux")
            if command -v systemctl >/dev/null 2>&1; then
                sudo systemctl restart docker
            elif command -v service >/dev/null 2>&1; then
                sudo service docker restart
            else
                log_error "❌ 无法重启 Docker 服务，请手动重启"
                return 1
            fi
            ;;
        "macos")
            log_warning "⚠️  macOS 用户请手动重启 Docker Desktop"
            log_info "或者运行: osascript -e 'quit app \"Docker\"' && open -a Docker"
            return 0
            ;;
        "windows")
            log_warning "⚠️  Windows 用户请手动重启 Docker Desktop"
            log_info "或者在 PowerShell 中运行: Restart-Service docker"
            return 0
            ;;
    esac
    
    # 等待 Docker 服务启动
    log_info "等待 Docker 服务启动..."
    local max_wait=30
    local wait_count=0
    
    while [ $wait_count -lt $max_wait ]; do
        if docker info >/dev/null 2>&1; then
            log_success "✅ Docker 服务已启动"
            return 0
        fi
        sleep 2
        wait_count=$((wait_count + 1))
    done
    
    log_error "❌ Docker 服务启动超时"
    return 1
}

# 验证配置
verify_config() {
    log_info "验证 Docker 配置..."
    
    if docker info >/dev/null 2>&1; then
        log_success "✅ Docker 服务正常运行"
        
        # 显示镜像源配置
        log_info "当前镜像源配置:"
        docker info | grep -A 10 "Registry Mirrors:" || log_warning "⚠️  未找到镜像源配置信息"
        
        return 0
    else
        log_error "❌ Docker 服务异常"
        return 1
    fi
}

# 测试镜像拉取
test_pull() {
    log_info "测试镜像拉取..."
    
    local test_image="registry.cn-hangzhou.aliyuncs.com/library/hello-world:latest"
    
    if docker pull "$test_image" >/dev/null 2>&1; then
        log_success "✅ 镜像拉取测试成功"
        docker rmi "$test_image" >/dev/null 2>&1
        return 0
    else
        log_warning "⚠️  镜像拉取测试失败，但配置已应用"
        return 1
    fi
}

# 主函数
main() {
    log_info "🚀 开始配置 Docker 镜像源"
    
    # 检查 Docker 是否安装
    if ! command -v docker >/dev/null 2>&1; then
        log_error "❌ Docker 未安装"
        exit 1
    fi
    
    # 应用配置
    apply_docker_config
    
    # 重启 Docker 服务
    if restart_docker; then
        # 验证配置
        verify_config
        
        # 测试拉取
        test_pull
    fi
    
    log_success "🎉 Docker 镜像源配置完成！"
    log_info "现在可以运行镜像预拉取脚本: ./.cicd/docker/scripts/pull-images.sh"
}

# 帮助信息
show_help() {
    cat << EOF
🚀 上海荷阁科技 Docker 镜像源配置脚本

用法: $0 [选项]

选项:
    -h, --help     显示此帮助信息
    -t, --test     仅测试当前配置
    -v, --verify   仅验证 Docker 状态

示例:
    $0              # 应用镜像源配置
    $0 -t           # 测试镜像拉取
    $0 -v           # 验证 Docker 状态

注意:
    - Linux 系统需要 sudo 权限
    - macOS 和 Windows 用户可能需要手动重启 Docker Desktop

EOF
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -t|--test)
        test_pull
        exit $?
        ;;
    -v|--verify)
        verify_config
        exit $?
        ;;
    "")
        main
        ;;
    *)
        log_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
