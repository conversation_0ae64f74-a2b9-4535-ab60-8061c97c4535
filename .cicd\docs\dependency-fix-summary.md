# 🔧 依赖安装问题修复总结

## 问题描述

GitLab CI/CD 流水线在执行 `npm ci` 时遇到以下错误：

```
npm ERR! ERESOLVE unable to resolve dependency tree
npm ERR! Found: react-dom@undefined
npm ERR! Could not resolve dependency:
npm ERR! peer react-dom@"^18.0.0" from @testing-library/react@14.3.1
```

## 根本原因

1. **网络连接问题**: 使用国外 npm 镜像源导致下载缓慢或失败
2. **缓存损坏**: npm 缓存中的数据损坏导致依赖解析失败
3. **Peer Dependencies 冲突**: 依赖版本不兼容

## 解决方案实施

### 1. 配置国内镜像源

**文件**: `.npmrc`
- 配置淘宝镜像源作为主要 registry
- 为特定包配置专用镜像源
- 优化性能和安全设置

### 2. 更新 GitLab CI 配置

**文件**: `.gitlab-ci.yml`

#### 全局配置
- 在 `default.before_script` 中配置镜像源
- 确保所有作业都使用国内镜像

#### 依赖安装作业 (`prepare:dependencies`)
- 配置多重安装策略
- 清理缓存后重新安装
- 使用 fallback 机制: `npm ci` → `--legacy-peer-deps` → `--force`

#### Windows 环境优化
- 创建专用 PowerShell 脚本
- 适配 Windows 环境的特殊需求

### 3. 自动化脚本

#### Linux/macOS 脚本
**文件**: `.cicd/scripts/install-dependencies.sh`
- 多策略依赖安装
- 自动故障恢复
- 详细日志输出

#### Windows 脚本
**文件**: `.cicd/scripts/install-dependencies.ps1`
- PowerShell 原生支持
- 错误处理和重试机制
- 彩色输出提升可读性

#### 验证脚本
**文件**: `.cicd/scripts/verify-dependencies.sh`
- 环境检查
- 依赖验证
- 命令可用性测试

### 4. 文档和故障排除

**文件**: `.cicd/docs/troubleshooting-dependencies.md`
- 详细的故障排除指南
- 常见问题和解决方案
- 预防措施建议

## 技术细节

### 镜像源配置
```bash
registry=https://registry.npmmirror.com/
@types:registry=https://registry.npmmirror.com/
@testing-library:registry=https://registry.npmmirror.com/
```

### 安装策略
1. 标准安装: `npm ci --no-audit --no-fund`
2. Legacy 模式: `npm ci --legacy-peer-deps`
3. 强制安装: `npm ci --force`
4. 重新生成: 删除 `node_modules` 和 `package-lock.json`

### 性能优化
- 使用缓存减少重复下载
- 禁用 audit 和 fund 检查提升速度
- 配置 prefer-offline 优先使用本地缓存

## 预期效果

1. **提升安装成功率**: 多重策略确保依赖安装成功
2. **加快安装速度**: 国内镜像源显著提升下载速度
3. **增强稳定性**: 自动重试和故障恢复机制
4. **改善维护性**: 详细文档和自动化脚本

## 监控和维护

- 定期检查镜像源可用性
- 监控 CI/CD 流水线成功率
- 更新依赖版本时验证兼容性
- 保持故障排除文档更新

## 后续优化建议

1. 考虑使用 yarn 或 pnpm 作为替代包管理器
2. 实施依赖版本锁定策略
3. 配置私有 npm 仓库
4. 建立依赖安全扫描流程
