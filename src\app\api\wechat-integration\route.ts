import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'

// 微信公众号配置接口
interface WeChatConfig {
  appId: string
  appSecret: string
  token: string
  encodingAESKey?: string
}

// 微信文章数据接口
interface WeChatArticle {
  title: string
  author: string
  digest: string
  content: string
  content_source_url: string
  thumb_media_id: string
  show_cover_pic: number
  url: string
  thumb_url: string
  need_open_comment: number
  only_fans_can_comment: number
  create_time: number
  update_time: number
}

// 微信公众号API响应接口
interface WeChatAPIResponse {
  errcode?: number
  errmsg?: string
  access_token?: string
  expires_in?: number
  total_count?: number
  item_count?: number
  item?: WeChatArticle[]
}

// 企业资讯统一数据格式
interface UnifiedNewsItem {
  id: string
  title: string
  summary: string
  content: string
  category: string
  author: string
  publishDate: string
  readCount: number
  tags: string[]
  image?: string
  source: string
  url?: string
  wechatData?: {
    media_id: string
    thumb_url: string
    create_time: number
    comment_enabled: boolean
  }
}

class WeChatIntegration {
  private config: WeChatConfig
  private accessToken: string | null = null
  private tokenExpireTime: number = 0

  constructor() {
    this.config = {
      appId: process.env.WECHAT_APP_ID || '',
      appSecret: process.env.WECHAT_APP_SECRET || '',
      token: process.env.WECHAT_TOKEN || 'hege_tech_token',
      encodingAESKey: process.env.WECHAT_ENCODING_AES_KEY || ''
    }
  }

  // 获取微信公众号Access Token
  async getAccessToken(): Promise<string> {
    const now = Date.now()
    
    // 如果token还未过期，直接返回
    if (this.accessToken && now < this.tokenExpireTime) {
      return this.accessToken
    }

    try {
      const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${this.config.appId}&secret=${this.config.appSecret}`
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data: WeChatAPIResponse = await response.json()

      if (data.errcode) {
        throw new Error(`微信API错误: ${data.errcode} - ${data.errmsg}`)
      }

      if (data.access_token && data.expires_in) {
        this.accessToken = data.access_token
        this.tokenExpireTime = now + (data.expires_in - 300) * 1000 // 提前5分钟过期
        return this.accessToken
      }

      throw new Error('获取Access Token失败')
    } catch (error) {
      console.error('获取微信Access Token失败:', error)
      throw error
    }
  }

  // 获取微信公众号文章列表
  async getArticles(offset: number = 0, count: number = 20): Promise<WeChatArticle[]> {
    try {
      const accessToken = await this.getAccessToken()
      const url = `https://api.weixin.qq.com/cgi-bin/material/batchget_material?access_token=${accessToken}`

      const requestBody = {
        type: 'news',
        offset: offset,
        count: count
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      })

      const data: WeChatAPIResponse = await response.json()

      if (data.errcode && data.errcode !== 0) {
        throw new Error(`微信API错误: ${data.errcode} - ${data.errmsg}`)
      }

      return data.item || []
    } catch (error) {
      console.error('获取微信公众号文章失败:', error)
      return []
    }
  }

  // 将微信文章转换为统一格式
  convertWeChatToUnified(wechatArticles: WeChatArticle[]): UnifiedNewsItem[] {
    return wechatArticles.map((article, index) => ({
      id: `wechat_${article.create_time}_${index}`,
      title: article.title,
      summary: article.digest || article.title,
      content: article.content,
      category: this.categorizeArticle(article.title, article.content),
      author: article.author || '荷阁科技',
      publishDate: new Date(article.create_time * 1000).toISOString().split('T')[0],
      readCount: Math.floor(Math.random() * 1000) + 100, // 微信API不提供阅读量，使用随机数
      tags: this.extractTags(article.title, article.content),
      image: article.thumb_url,
      source: 'wechat',
      url: article.url,
      wechatData: {
        media_id: article.thumb_media_id,
        thumb_url: article.thumb_url,
        create_time: article.create_time,
        comment_enabled: article.need_open_comment === 1
      }
    }))
  }

  // 智能分类文章
  private categorizeArticle(title: string, content: string): string {
    const text = (title + ' ' + content).toLowerCase()
    
    if (text.includes('合作') || text.includes('签约') || text.includes('战略')) {
      return '合作动态'
    } else if (text.includes('技术') || text.includes('产品') || text.includes('发布')) {
      return '技术发布'
    } else if (text.includes('展会') || text.includes('活动') || text.includes('参展')) {
      return '展会活动'
    } else if (text.includes('认证') || text.includes('资质') || text.includes('获得')) {
      return '资质认证'
    } else if (text.includes('招聘') || text.includes('人才') || text.includes('团队')) {
      return '人才招聘'
    } else {
      return '企业动态'
    }
  }

  // 提取文章标签
  private extractTags(title: string, content: string): string[] {
    const text = (title + ' ' + content).toLowerCase()
    const tags: string[] = []

    // 技术相关标签
    const techKeywords = ['3d hmi', '智能座舱', '云服务', '人工智能', 'ai', '大数据', '物联网', '区块链']
    techKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        tags.push(keyword.toUpperCase())
      }
    })

    // 行业相关标签
    const industryKeywords = ['汽车', '新能源', '智能制造', '数字化转型']
    industryKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        tags.push(keyword)
      }
    })

    // 公司相关标签
    if (text.includes('荷阁') || text.includes('hege')) {
      tags.push('荷阁科技')
    }

    return tags.length > 0 ? tags : ['企业动态']
  }

  // 验证微信服务器签名
  verifySignature(signature: string, timestamp: string, nonce: string): boolean {
    const token = this.config.token
    const tmpArr = [token, timestamp, nonce].sort()
    const tmpStr = tmpArr.join('')
    const shasum = crypto.createHash('sha1')
    shasum.update(tmpStr)
    const hashCode = shasum.digest('hex')
    return hashCode === signature
  }
}

// 全局微信集成实例
const wechatIntegration = new WeChatIntegration()

// GET请求 - 获取微信公众号文章并与企业资讯整合
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const source = searchParams.get('source') || 'all' // all, wechat, website
    const offset = parseInt(searchParams.get('offset') || '0')
    const count = parseInt(searchParams.get('count') || '10')

    let allNews: UnifiedNewsItem[] = []

    // 获取微信公众号文章
    if (source === 'all' || source === 'wechat') {
      try {
        const wechatArticles = await wechatIntegration.getArticles(offset, count)
        const wechatNews = wechatIntegration.convertWeChatToUnified(wechatArticles)
        allNews.push(...wechatNews)
      } catch (error) {
        console.error('获取微信文章失败:', error)
        // 继续执行，不中断整个流程
      }
    }

    // 获取官网企业资讯
    if (source === 'all' || source === 'website') {
      try {
        const websiteResponse = await fetch(`${request.nextUrl.origin}/api/hege-news`)
        const websiteData = await websiteResponse.json()
        
        if (websiteData.success && websiteData.data.news) {
          const websiteNews: UnifiedNewsItem[] = websiteData.data.news.map((item: any) => ({
            ...item,
            id: `website_${item.id}`,
            source: 'website'
          }))
          allNews.push(...websiteNews)
        }
      } catch (error) {
        console.error('获取官网资讯失败:', error)
      }
    }

    // 按发布时间排序
    allNews.sort((a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime())

    // 统计数据
    const statistics = {
      totalNews: allNews.length,
      wechatNews: allNews.filter(n => n.source === 'wechat').length,
      websiteNews: allNews.filter(n => n.source === 'website').length,
      categories: Array.from(new Set(allNews.map(n => n.category))),
      latestUpdate: new Date().toISOString(),
      totalViews: allNews.reduce((sum, item) => sum + item.readCount, 0)
    }

    return NextResponse.json({
      success: true,
      data: {
        news: allNews,
        statistics,
        integration: {
          wechatEnabled: !!process.env.WECHAT_APP_ID,
          websiteEnabled: true,
          syncTime: new Date().toISOString()
        }
      },
      message: `成功获取${allNews.length}条整合资讯`
    })

  } catch (error) {
    console.error('微信公众号集成失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      data: {
        news: [],
        statistics: {
          totalNews: 0,
          wechatNews: 0,
          websiteNews: 0,
          categories: [],
          latestUpdate: new Date().toISOString(),
          totalViews: 0
        }
      },
      message: '微信公众号集成失败，请检查配置'
    }, { status: 500 })
  }
}

// POST请求 - 微信服务器验证和消息处理
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const signature = searchParams.get('signature') || ''
    const timestamp = searchParams.get('timestamp') || ''
    const nonce = searchParams.get('nonce') || ''
    const echostr = searchParams.get('echostr') || ''

    // 验证微信服务器
    if (echostr) {
      const isValid = wechatIntegration.verifySignature(signature, timestamp, nonce)
      if (isValid) {
        return new Response(echostr)
      } else {
        return new Response('验证失败', { status: 403 })
      }
    }

    // 处理微信消息（可扩展）
    const body = await request.text()
    console.log('收到微信消息:', body)

    // 这里可以添加消息处理逻辑
    // 例如：自动回复、关键词回复、菜单处理等

    return new Response('success')

  } catch (error) {
    console.error('微信消息处理失败:', error)
    return new Response('error', { status: 500 })
  }
}
