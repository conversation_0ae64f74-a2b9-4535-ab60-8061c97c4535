#!/bin/bash

# 🚀 上海荷阁科技 - 依赖安装脚本
# 解决 npm 依赖解析问题的多重策略

set -e

echo "📦 开始安装项目依赖..."

# 策略1: 使用国内镜像源
echo "🔧 配置 npm 镜像源..."
npm config set registry https://registry.npmmirror.com/
npm config set @types:registry https://registry.npmmirror.com/
npm config set @testing-library:registry https://registry.npmmirror.com/

# 策略2: 清理缓存
echo "🧹 清理 npm 缓存..."
npm cache clean --force

# 策略3: 尝试标准安装
echo "📦 尝试标准依赖安装..."
if npm ci --no-audit --no-fund; then
    echo "✅ 标准安装成功"
    exit 0
fi

# 策略4: 使用 legacy peer deps
echo "⚠️ 标准安装失败，尝试使用 legacy peer deps..."
if npm ci --legacy-peer-deps --no-audit --no-fund; then
    echo "✅ Legacy peer deps 安装成功"
    exit 0
fi

# 策略5: 强制安装
echo "⚠️ Legacy peer deps 安装失败，尝试强制安装..."
if npm ci --force --no-audit --no-fund; then
    echo "✅ 强制安装成功"
    exit 0
fi

# 策略6: 删除 node_modules 重新安装
echo "⚠️ 强制安装失败，删除 node_modules 重新安装..."
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps --no-audit --no-fund

echo "✅ 依赖安装完成"
