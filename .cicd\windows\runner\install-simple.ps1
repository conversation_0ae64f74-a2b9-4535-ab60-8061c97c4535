# GitLab Runner Windows Installation Script
# Simple and reliable PowerShell script for installing GitLab Runner

param(
    [string]$GitLabUrl = "http://home.spinach.cool:11991/",
    [string]$RegistrationToken = "",
    [string]$RunnerName = "HEGE-Tech-Windows",
    [string]$InstallPath = "C:\GitLab-Runner",
    [switch]$Help
)

# Show help information
if ($Help) {
    Write-Host "GitLab Runner Windows Installation Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\install-simple.ps1 [parameters]"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "    -GitLabUrl          GitLab server URL (default: $GitLabUrl)"
    Write-Host "    -RegistrationToken  Registration token (required)"
    Write-Host "    -RunnerName         Runner name (default: $RunnerName)"
    Write-Host "    -InstallPath        Installation path (default: $InstallPath)"
    Write-Host "    -Help               Show this help information"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "    .\install-simple.ps1 -RegistrationToken 'your-token-here'"
    Write-Host ""
    Write-Host "Note: Administrator privileges required" -ForegroundColor Yellow
    exit 0
}

# Check administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script requires administrator privileges" -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator and retry" -ForegroundColor Yellow
    exit 1
}

# Check required parameters
if ([string]::IsNullOrEmpty($RegistrationToken)) {
    Write-Host "ERROR: Missing registration token parameter" -ForegroundColor Red
    Write-Host "Please use -RegistrationToken parameter" -ForegroundColor Yellow
    Write-Host "Use -Help to see help information" -ForegroundColor Yellow
    exit 1
}

# Start installation
Write-Host "Starting GitLab Runner installation..." -ForegroundColor Green
Write-Host "GitLab URL: $GitLabUrl" -ForegroundColor Cyan
Write-Host "Runner Name: $RunnerName" -ForegroundColor Cyan
Write-Host "Installation Path: $InstallPath" -ForegroundColor Cyan

# Check PowerShell version
Write-Host "Checking PowerShell version..." -ForegroundColor Blue
$psVersion = $PSVersionTable.PSVersion
if ($psVersion.Major -lt 5) {
    Write-Host "ERROR: PowerShell version too low, requires 5.0 or higher" -ForegroundColor Red
    exit 1
}
Write-Host "SUCCESS: PowerShell version $($psVersion.ToString())" -ForegroundColor Green

# Create installation directory
Write-Host "Creating installation directory..." -ForegroundColor Blue
if (-not (Test-Path $InstallPath)) {
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    Write-Host "SUCCESS: Created directory $InstallPath" -ForegroundColor Green
} else {
    Write-Host "INFO: Directory $InstallPath already exists" -ForegroundColor Yellow
}

# Download GitLab Runner
Write-Host "Downloading GitLab Runner..." -ForegroundColor Blue
$runnerUrl = "https://gitlab-runner-downloads.s3.amazonaws.com/latest/binaries/gitlab-runner-windows-amd64.exe"
$runnerPath = Join-Path $InstallPath "gitlab-runner.exe"

try {
    # Enable TLS 1.2
    [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
    
    Invoke-WebRequest -Uri $runnerUrl -OutFile $runnerPath -UseBasicParsing
    Write-Host "SUCCESS: GitLab Runner downloaded" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to download GitLab Runner: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Verify downloaded file
if (-not (Test-Path $runnerPath)) {
    Write-Host "ERROR: GitLab Runner executable not found" -ForegroundColor Red
    exit 1
}

$fileSize = (Get-Item $runnerPath).Length
if ($fileSize -lt 1MB) {
    Write-Host "ERROR: Downloaded file size is abnormal" -ForegroundColor Red
    exit 1
}
Write-Host "SUCCESS: File verification passed, size: $([math]::Round($fileSize/1MB, 2)) MB" -ForegroundColor Green

# Install service
Write-Host "Installing GitLab Runner service..." -ForegroundColor Blue
try {
    Set-Location $InstallPath
    & .\gitlab-runner.exe install --user "NT AUTHORITY\SYSTEM" --password ""
    Write-Host "SUCCESS: GitLab Runner service installed" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to install service: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Register runner
Write-Host "Registering GitLab Runner..." -ForegroundColor Blue
try {
    $registerArgs = @(
        "register",
        "--non-interactive",
        "--url", $GitLabUrl,
        "--registration-token", $RegistrationToken,
        "--name", $RunnerName,
        "--executor", "shell",
        "--shell", "powershell"
    )
    
    & .\gitlab-runner.exe @registerArgs
    Write-Host "SUCCESS: GitLab Runner registered" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to register runner: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "You can register manually later" -ForegroundColor Yellow
}

# Start service
Write-Host "Starting GitLab Runner service..." -ForegroundColor Blue
try {
    Start-Service -Name "gitlab-runner"
    Write-Host "SUCCESS: GitLab Runner service started" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to start service: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Verify installation
Write-Host "Verifying installation..." -ForegroundColor Blue
Start-Sleep -Seconds 3

$service = Get-Service -Name "gitlab-runner" -ErrorAction SilentlyContinue
if ($service -and $service.Status -eq "Running") {
    Write-Host "SUCCESS: GitLab Runner service is running normally" -ForegroundColor Green
} else {
    Write-Host "ERROR: GitLab Runner service is not running properly" -ForegroundColor Red
}

# Show version information
try {
    $version = & .\gitlab-runner.exe --version
    Write-Host "SUCCESS: Installed version: $version" -ForegroundColor Green
} catch {
    Write-Host "WARNING: Cannot get version information" -ForegroundColor Yellow
}

# Installation complete
Write-Host ""
Write-Host "GitLab Runner installation completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Installation Information:" -ForegroundColor Cyan
Write-Host "  Installation Path: $InstallPath"
Write-Host "  Runner Name: $RunnerName"
Write-Host "  GitLab URL: $GitLabUrl"
Write-Host ""
Write-Host "Management Commands:" -ForegroundColor Cyan
Write-Host "  Check status: Get-Service gitlab-runner"
Write-Host "  Start service: Start-Service gitlab-runner"
Write-Host "  Stop service: Stop-Service gitlab-runner"
Write-Host "  Restart service: Restart-Service gitlab-runner"
Write-Host ""
Write-Host "Runner Management:" -ForegroundColor Cyan
Write-Host "  Verify config: .\gitlab-runner.exe verify"
Write-Host "  View logs: .\gitlab-runner.exe --debug run"
Write-Host "  List runners: .\gitlab-runner.exe list"
Write-Host ""
Write-Host "Please ensure the corresponding Runner is enabled in GitLab project settings" -ForegroundColor Yellow
