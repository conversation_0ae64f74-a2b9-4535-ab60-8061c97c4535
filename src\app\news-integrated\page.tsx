'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import Navigation from '@/components/Navigation'
import { 
  Calendar, 
  Eye, 
  User, 
  Tag, 
  Search, 
  Filter, 
  ChevronDown,
  RefreshCw,
  ArrowRight,
  Clock,
  TrendingUp,
  BookOpen,
  ExternalLink,
  MessageCircle,
  Smartphone,
  Globe,
  Zap,
  BarChart3
} from 'lucide-react'

// 统一的企业资讯数据接口
interface UnifiedNewsItem {
  id: string
  title: string
  summary: string
  content: string
  category: string
  author: string
  publishDate: string
  readCount: number
  tags: string[]
  image?: string
  source: string // 'wechat' | 'website'
  url?: string
  wechatData?: {
    media_id: string
    thumb_url: string
    create_time: number
    comment_enabled: boolean
  }
}

interface IntegratedNewsData {
  news: UnifiedNewsItem[]
  statistics: {
    totalNews: number
    wechatNews: number
    websiteNews: number
    categories: string[]
    latestUpdate: string
    totalViews: number
  }
  integration: {
    wechatEnabled: boolean
    websiteEnabled: boolean
    syncTime: string
  }
}

export default function IntegratedNewsPage() {
  const [newsData, setNewsData] = useState<IntegratedNewsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('全部')
  const [selectedSource, setSelectedSource] = useState('all') // 'all' | 'wechat' | 'website'
  const [showFilters, setShowFilters] = useState(false)
  const [expandedNews, setExpandedNews] = useState<string | null>(null)

  // 获取整合的企业资讯数据
  const fetchIntegratedNews = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch(`/api/wechat-integration?source=${selectedSource}&count=20`, {
        cache: 'no-store'
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        setNewsData(result.data)
      } else {
        throw new Error(result.message || '获取资讯失败')
      }
    } catch (err) {
      console.error('获取整合资讯失败:', err)
      setError(err instanceof Error ? err.message : '获取资讯失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchIntegratedNews()
  }, [selectedSource])

  // 筛选新闻
  const filteredNews = newsData?.news.filter(item => {
    const matchesSearch = searchTerm === '' || 
      item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = selectedCategory === '全部' || item.category === selectedCategory
    
    return matchesSearch && matchesCategory
  }) || []

  // 获取分类颜色
  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      '合作动态': 'bg-blue-100 text-blue-800',
      '技术发布': 'bg-green-100 text-green-800',
      '展会活动': 'bg-purple-100 text-purple-800',
      '资质认证': 'bg-orange-100 text-orange-800',
      '人才招聘': 'bg-pink-100 text-pink-800',
      '企业动态': 'bg-gray-100 text-gray-800'
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  // 获取数据源图标和颜色
  const getSourceInfo = (source: string) => {
    if (source === 'wechat') {
      return {
        icon: <MessageCircle className="h-4 w-4" />,
        label: '微信公众号',
        color: 'text-green-600 bg-green-50'
      }
    } else {
      return {
        icon: <Globe className="h-4 w-4" />,
        label: '官方网站',
        color: 'text-blue-600 bg-blue-50'
      }
    }
  }

  // 计算阅读时间
  const calculateReadTime = (content: string) => {
    const wordsPerMinute = 200
    const wordCount = content.length
    const readTime = Math.ceil(wordCount / wordsPerMinute)
    return readTime
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center space-x-2">
              <RefreshCw className="h-6 w-6 animate-spin text-blue-600" />
              <span className="text-lg text-gray-600">正在加载整合资讯...</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      {/* 页面头部 */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">企业资讯中心</h1>
            <p className="text-xl opacity-90 mb-6">微信公众号 + 官网资讯整合展示</p>
            
            {/* 数据源状态 */}
            <div className="flex items-center justify-center space-x-6 mb-8">
              <div className="flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2">
                <MessageCircle className="h-5 w-5" />
                <span>微信公众号</span>
                <div className={`w-2 h-2 rounded-full ${newsData?.integration.wechatEnabled ? 'bg-green-400' : 'bg-red-400'}`}></div>
              </div>
              <div className="flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2">
                <Globe className="h-5 w-5" />
                <span>官方网站</span>
                <div className={`w-2 h-2 rounded-full ${newsData?.integration.websiteEnabled ? 'bg-green-400' : 'bg-red-400'}`}></div>
              </div>
            </div>

            {/* 统计数据 */}
            {newsData && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
                <div className="bg-white/20 rounded-lg p-4">
                  <div className="text-2xl font-bold">{newsData.statistics.totalNews}</div>
                  <div className="text-sm opacity-80">总资讯数</div>
                </div>
                <div className="bg-white/20 rounded-lg p-4">
                  <div className="text-2xl font-bold">{newsData.statistics.wechatNews}</div>
                  <div className="text-sm opacity-80">微信资讯</div>
                </div>
                <div className="bg-white/20 rounded-lg p-4">
                  <div className="text-2xl font-bold">{newsData.statistics.websiteNews}</div>
                  <div className="text-sm opacity-80">官网资讯</div>
                </div>
                <div className="bg-white/20 rounded-lg p-4">
                  <div className="text-2xl font-bold">{newsData.statistics.categories.length}</div>
                  <div className="text-sm opacity-80">资讯分类</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 py-8">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <div className="text-red-600 mr-3">⚠️</div>
              <div>
                <h3 className="text-red-800 font-medium">加载失败</h3>
                <p className="text-red-600 text-sm mt-1">{error}</p>
              </div>
              <button
                onClick={fetchIntegratedNews}
                className="ml-auto bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                重试
              </button>
            </div>
          </div>
        )}

        {/* 搜索和筛选 */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            {/* 搜索框 */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="搜索资讯标题、内容或标签..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* 数据源筛选 */}
            <div className="flex space-x-2">
              <button
                onClick={() => setSelectedSource('all')}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  selectedSource === 'all' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                全部来源
              </button>
              <button
                onClick={() => setSelectedSource('wechat')}
                className={`px-4 py-2 rounded-lg transition-colors flex items-center space-x-2 ${
                  selectedSource === 'wechat' 
                    ? 'bg-green-600 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <MessageCircle className="h-4 w-4" />
                <span>微信</span>
              </button>
              <button
                onClick={() => setSelectedSource('website')}
                className={`px-4 py-2 rounded-lg transition-colors flex items-center space-x-2 ${
                  selectedSource === 'website' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Globe className="h-4 w-4" />
                <span>官网</span>
              </button>
            </div>

            {/* 筛选按钮 */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              <Filter className="h-4 w-4" />
              <span>筛选</span>
              <ChevronDown className={`h-4 w-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
            </button>

            {/* 刷新按钮 */}
            <button
              onClick={fetchIntegratedNews}
              disabled={loading}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              <span>刷新</span>
            </button>
          </div>

          {/* 分类筛选 */}
          {showFilters && newsData && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => setSelectedCategory('全部')}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    selectedCategory === '全部'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  全部分类
                </button>
                {newsData.statistics.categories.map(category => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      selectedCategory === category
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 资讯列表 */}
        <div className="grid gap-6">
          {filteredNews.length === 0 ? (
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-500 mb-2">暂无资讯</h3>
              <p className="text-gray-400">请尝试调整搜索条件或筛选选项</p>
            </div>
          ) : (
            filteredNews.map((item) => {
              const sourceInfo = getSourceInfo(item.source)
              const readTime = calculateReadTime(item.content)
              
              return (
                <div key={item.id} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
                  <div className="p-6">
                    {/* 资讯头部信息 */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(item.category)}`}>
                          {item.category}
                        </span>
                        <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${sourceInfo.color}`}>
                          {sourceInfo.icon}
                          <span>{sourceInfo.label}</span>
                        </span>
                      </div>
                      <div className="text-sm text-gray-500 flex items-center space-x-4">
                        <span className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {item.publishDate}
                        </span>
                        <span className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {readTime}分钟阅读
                        </span>
                      </div>
                    </div>

                    {/* 资讯标题 */}
                    <h2 className="text-xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors cursor-pointer">
                      {item.title}
                    </h2>

                    {/* 资讯摘要 */}
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {item.summary}
                    </p>

                    {/* 资讯底部信息 */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span className="flex items-center">
                          <User className="h-4 w-4 mr-1" />
                          {item.author}
                        </span>
                        <span className="flex items-center">
                          <Eye className="h-4 w-4 mr-1" />
                          {item.readCount}
                        </span>
                      </div>

                      {/* 标签和操作按钮 */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {item.tags.slice(0, 3).map((tag, index) => (
                            <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-600">
                              <Tag className="h-3 w-3 mr-1" />
                              {tag}
                            </span>
                          ))}
                        </div>
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={() => setExpandedNews(expandedNews === item.id ? null : item.id)}
                            className="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors group"
                          >
                            {expandedNews === item.id ? '收起' : '阅读全文'}
                            <ArrowRight className={`h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform ${expandedNews === item.id ? 'rotate-90' : ''}`} />
                          </button>
                          {item.url && (
                            <Link
                              href={item.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center text-gray-600 hover:text-gray-700 text-sm font-medium"
                            >
                              原文链接
                              <ExternalLink className="h-4 w-4 ml-1" />
                            </Link>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* 展开的完整内容 */}
                    {expandedNews === item.id && (
                      <div className="border-t border-gray-200 bg-gray-50 p-6 mt-4">
                        <div className="max-w-4xl mx-auto">
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">完整内容</h3>
                          <div
                            className="prose prose-lg max-w-none text-gray-700 leading-relaxed"
                            dangerouslySetInnerHTML={{ __html: item.content }}
                          />

                          {/* 文章底部信息 */}
                          <div className="mt-6 pt-4 border-t border-gray-300">
                            <div className="flex items-center justify-between text-sm text-gray-500">
                              <div className="flex items-center space-x-4">
                                <span>作者：{item.author}</span>
                                <span>发布时间：{item.publishDate}</span>
                                <span>阅读量：{item.readCount}</span>
                                <span>来源：{item.source === 'wechat' ? '微信公众号' : '官方网站'}</span>
                              </div>
                              <button
                                onClick={() => setExpandedNews(null)}
                                className="text-blue-600 hover:text-blue-700 font-medium"
                              >
                                收起内容
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )
            })
          )}
        </div>

        {/* 数据源信息 */}
        {newsData && (
          <div className="mt-8 bg-gray-50 rounded-xl p-6">
            <div className="text-center text-sm text-gray-600">
              <p className="mb-2">
                数据来源：微信公众号 + 官方网站 | 
                最后同步时间：{new Date(newsData.integration.syncTime).toLocaleString('zh-CN')}
              </p>
              <p>
                总计 {newsData.statistics.totalNews} 条资讯，
                其中微信公众号 {newsData.statistics.wechatNews} 条，
                官方网站 {newsData.statistics.websiteNews} 条
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
