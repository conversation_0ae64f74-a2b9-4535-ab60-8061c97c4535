/**
 * 网络安全工具函数
 * 用于输入验证、XSS防护、SQL注入防护等
 */

import crypto from 'crypto'
import { NextRequest } from 'next/server'

// ===========================================
// 输入验证和清理
// ===========================================

/**
 * HTML实体编码 - 防止XSS攻击
 */
export function escapeHtml(unsafe: string): string {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;")
    .replace(/\//g, "&#x2F;")
}

/**
 * 清理用户输入 - 移除潜在危险字符
 */
export function sanitizeInput(input: string): string {
  if (!input || typeof input !== 'string') return ''
  
  return input
    .trim()
    .replace(/[<>]/g, '') // 移除HTML标签
    .replace(/javascript:/gi, '') // 移除JavaScript协议
    .replace(/on\w+=/gi, '') // 移除事件处理器
    .replace(/script/gi, '') // 移除script标签
    .replace(/eval\(/gi, '') // 移除eval函数
    .slice(0, 1000) // 限制长度
}

/**
 * 验证邮箱格式
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email) && email.length <= 254
}

/**
 * 验证电话号码格式
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone.replace(/\D/g, ''))
}

/**
 * 验证URL格式
 */
export function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url)
    return ['http:', 'https:'].includes(urlObj.protocol)
  } catch {
    return false
  }
}

// ===========================================
// CSRF防护
// ===========================================

/**
 * 生成CSRF令牌
 */
export function generateCSRFToken(): string {
  return crypto.randomBytes(32).toString('hex')
}

/**
 * 验证CSRF令牌
 */
export function verifyCSRFToken(token: string, sessionToken: string): boolean {
  if (!token || !sessionToken) return false
  return crypto.timingSafeEqual(
    Buffer.from(token, 'hex'),
    Buffer.from(sessionToken, 'hex')
  )
}

// ===========================================
// 速率限制
// ===========================================

interface RateLimitEntry {
  count: number
  resetTime: number
}

const rateLimitMap = new Map<string, RateLimitEntry>()

/**
 * 检查速率限制
 */
export function checkRateLimit(
  identifier: string,
  limit: number = 100,
  windowMs: number = 60000
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now()
  const entry = rateLimitMap.get(identifier)
  
  if (!entry || now > entry.resetTime) {
    // 创建新的限制条目
    const newEntry: RateLimitEntry = {
      count: 1,
      resetTime: now + windowMs
    }
    rateLimitMap.set(identifier, newEntry)
    
    return {
      allowed: true,
      remaining: limit - 1,
      resetTime: newEntry.resetTime
    }
  }
  
  if (entry.count >= limit) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: entry.resetTime
    }
  }
  
  entry.count++
  rateLimitMap.set(identifier, entry)
  
  return {
    allowed: true,
    remaining: limit - entry.count,
    resetTime: entry.resetTime
  }
}

// ===========================================
// IP地址处理
// ===========================================

/**
 * 获取客户端真实IP地址
 */
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  return realIP || remoteAddr || 'unknown'
}

/**
 * 检查IP是否在黑名单中
 */
const ipBlacklist = new Set<string>([
  // 添加需要屏蔽的IP地址
])

export function isIPBlacklisted(ip: string): boolean {
  return ipBlacklist.has(ip)
}

// ===========================================
// 文件上传安全
// ===========================================

/**
 * 验证文件类型
 */
export function isAllowedFileType(filename: string, allowedTypes: string[]): boolean {
  const ext = filename.toLowerCase().split('.').pop()
  if (!ext) return false
  
  const allowedExts = allowedTypes.map(type => {
    switch (type) {
      case 'image/jpeg': return 'jpg'
      case 'image/png': return 'png'
      case 'image/webp': return 'webp'
      case 'application/pdf': return 'pdf'
      default: return type.split('/')[1]
    }
  })
  
  return allowedExts.includes(ext)
}

/**
 * 生成安全的文件名
 */
export function generateSafeFilename(originalName: string): string {
  const ext = originalName.split('.').pop()
  const timestamp = Date.now()
  const random = crypto.randomBytes(8).toString('hex')
  
  return `${timestamp}_${random}.${ext}`
}

// ===========================================
// 密码安全
// ===========================================

/**
 * 验证密码强度
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  if (password.length < 8) {
    errors.push('密码长度至少8位')
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('密码必须包含大写字母')
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('密码必须包含小写字母')
  }
  
  if (!/\d/.test(password)) {
    errors.push('密码必须包含数字')
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('密码必须包含特殊字符')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// ===========================================
// 安全日志
// ===========================================

export interface SecurityEvent {
  type: 'login_attempt' | 'failed_login' | 'suspicious_activity' | 'rate_limit_exceeded' | 'file_upload' | 'xss_attempt'
  ip: string
  userAgent: string
  timestamp: Date
  details: Record<string, any>
}

const securityLogs: SecurityEvent[] = []

/**
 * 记录安全事件
 */
export function logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
  const logEntry: SecurityEvent = {
    ...event,
    timestamp: new Date()
  }
  
  securityLogs.push(logEntry)
  
  // 保持最近1000条记录
  if (securityLogs.length > 1000) {
    securityLogs.splice(0, securityLogs.length - 1000)
  }
  
  // 在生产环境中，这里应该写入到持久化存储
  if (process.env.ENABLE_SECURITY_LOGS === 'true') {
    console.log('[SECURITY]', JSON.stringify(logEntry))
  }
}

/**
 * 获取安全日志
 */
export function getSecurityLogs(limit: number = 100): SecurityEvent[] {
  return securityLogs.slice(-limit)
}
