# Shanghai Hege Technology - Windows Environment Dependency Installation Script
# Multiple strategies to resolve npm dependency issues

$ErrorActionPreference = "Stop"

Write-Host "Starting project dependencies installation..." -ForegroundColor Green

try {
    # Strategy 1: Use domestic mirror sources
    Write-Host "Configuring npm registry..." -ForegroundColor Yellow
    npm config set registry https://registry.npmmirror.com/
    npm config set "@types:registry" https://registry.npmmirror.com/
    npm config set "@testing-library:registry" https://registry.npmmirror.com/

    # Strategy 2: Clean cache
    Write-Host "Cleaning npm cache..." -ForegroundColor Yellow
    npm cache clean --force

    # Strategy 3: Try standard installation
    Write-Host "Attempting standard dependency installation..." -ForegroundColor Yellow
    $result = npm ci --no-audit --no-fund 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Standard installation successful" -ForegroundColor Green
        exit 0
    }

    # Strategy 4: Use legacy peer deps
    Write-Host "Standard installation failed, trying legacy peer deps..." -ForegroundColor Yellow
    $result = npm ci --legacy-peer-deps --no-audit --no-fund 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Legacy peer deps installation successful" -ForegroundColor Green
        exit 0
    }

    # Strategy 5: Force installation
    Write-Host "Legacy peer deps installation failed, trying force installation..." -ForegroundColor Yellow
    $result = npm ci --force --no-audit --no-fund 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Force installation successful" -ForegroundColor Green
        exit 0
    }

    # Strategy 6: Remove node_modules and reinstall
    Write-Host "Force installation failed, removing node_modules and reinstalling..." -ForegroundColor Yellow
    if (Test-Path "node_modules") {
        Remove-Item -Recurse -Force "node_modules"
    }
    if (Test-Path "package-lock.json") {
        Remove-Item -Force "package-lock.json"
    }
    npm install --legacy-peer-deps --no-audit --no-fund

    Write-Host "Dependencies installation completed" -ForegroundColor Green
}
catch {
    Write-Host "Dependencies installation failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
