# Shanghai Hege Technology - Windows Environment Dependency Installation Script
# Multiple strategies to resolve npm dependency issues

# Set error handling to continue on command failures but stop on script errors
$ErrorActionPreference = "Continue"

Write-Host "Starting project dependencies installation..." -ForegroundColor Green

# Function to run npm command and capture both output and exit code
function Invoke-NpmCommand {
    param(
        [string]$Command,
        [string]$Description
    )

    Write-Host "Attempting: $Description" -ForegroundColor Yellow
    Write-Host "Running: $Command" -ForegroundColor Cyan

    # Execute the command and capture output
    $output = Invoke-Expression $Command 2>&1
    $exitCode = $LASTEXITCODE

    # Display output
    if ($output) {
        Write-Host "Output:" -ForegroundColor Gray
        $output | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
    }

    if ($exitCode -eq 0) {
        Write-Host "$Description - SUCCESS" -ForegroundColor Green
        return $true
    } else {
        Write-Host "$Description - FAILED (Exit Code: $exitCode)" -ForegroundColor Red
        return $false
    }
}

try {
    # Strategy 1: Use domestic mirror sources
    Write-Host "Configuring npm registry..." -ForegroundColor Yellow
    npm config set registry https://registry.npmmirror.com/
    npm config set "@types:registry" https://registry.npmmirror.com/
    npm config set "@testing-library:registry" https://registry.npmmirror.com/

    # Strategy 2: Clean cache
    Write-Host "Cleaning npm cache..." -ForegroundColor Yellow
    npm cache clean --force

    # Strategy 3: Try standard installation
    if (Invoke-NpmCommand "npm ci --no-audit --no-fund" "Standard npm ci installation") {
        Write-Host "Dependencies installation completed successfully" -ForegroundColor Green
        exit 0
    }

    # Strategy 4: Use legacy peer deps
    if (Invoke-NpmCommand "npm ci --legacy-peer-deps --no-audit --no-fund" "npm ci with legacy peer deps") {
        Write-Host "Dependencies installation completed successfully" -ForegroundColor Green
        exit 0
    }

    # Strategy 5: Force installation
    if (Invoke-NpmCommand "npm ci --force --no-audit --no-fund" "npm ci with force flag") {
        Write-Host "Dependencies installation completed successfully" -ForegroundColor Green
        exit 0
    }

    # Strategy 6: Remove node_modules and reinstall
    Write-Host "All npm ci strategies failed, removing node_modules and trying npm install..." -ForegroundColor Yellow
    if (Test-Path "node_modules") {
        Write-Host "Removing existing node_modules directory..." -ForegroundColor Yellow
        Remove-Item -Recurse -Force "node_modules"
    }

    if (Invoke-NpmCommand "npm install --legacy-peer-deps --no-audit --no-fund" "npm install with legacy peer deps") {
        Write-Host "Dependencies installation completed successfully" -ForegroundColor Green
        exit 0
    }

    # Strategy 7: Last resort - force install
    if (Invoke-NpmCommand "npm install --force --no-audit --no-fund" "npm install with force flag") {
        Write-Host "Dependencies installation completed successfully" -ForegroundColor Green
        exit 0
    }

    # If all strategies fail
    Write-Host "All installation strategies failed!" -ForegroundColor Red
    Write-Host "Please check the npm configuration and network connectivity." -ForegroundColor Red
    exit 1

}
catch {
    Write-Host "Dependencies installation failed with exception: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
    exit 1
}
