# Git 仓库设置完成

## ✅ 配置状态

### 🔧 Git 配置

- **仓库地址**: http://home.spinach.cool:11991/Web/hege-tech-web.git
- **用户名**: Hege Tech
- **邮箱**: <EMAIL>
- **默认分支**: main

### 📁 文件管理

- **忽略文件**: `.gitignore` 已配置
- **排除内容**:
  - ❌ node_modules/ (依赖包)
  - ❌ .next/, out/ (构建文件)
  - ❌ _.zip, _.tar.gz (压缩包)
  - ❌ \*.log (日志文件)
  - ❌ .DS_Store (系统文件)
  - ❌ 缓存文件

### 📦 已提交内容

- ✅ 源代码 (src/)
- ✅ 配置文件 (package.json, next.config.js等)
- ✅ 静态资源 (public/)
- ✅ 文档文件 (README.md)
- ✅ 自动提交脚本 (daily-commit.sh)

## 🚀 每日提交使用方法

### 方法一：手动执行脚本

```bash
./daily-commit.sh
```

### 方法二：设置定时任务 (推荐)

```bash
# 编辑crontab
crontab -e

# 添加以下行 (每天晚上11点自动提交)
0 23 * * * cd /path/to/HomePage && ./daily-commit.sh >> commit.log 2>&1
```

### 方法三：手动Git操作

```bash
# 添加文件
git add .

# 提交变更
git commit -m "📅 Daily update $(date '+%Y-%m-%d')"

# 推送到远程
git push origin main
```

## 📋 脚本功能特性

### 🧹 自动清理

- 删除构建缓存 (.next/, out/)
- 清理日志文件 (\*.log)
- 移除压缩包 (_.zip, _.tar.gz)
- 清理临时文件

### 📊 智能检测

- 检测文件变更
- 跳过空提交
- 生成统计信息
- 错误处理

### 🏷️ 版本管理 (内部项目规范)

- 版本格式: V<主版本>.<次版本>.<修订号>[-阶段标识.迭代次数]
- 自动递增修订号 (V1.0.0 → V1.0.1 → V1.0.2)
- 创建Git标签 (以大写V开头)
- 推送标签到远程
- 版本历史记录和演进追踪

### 📝 提交信息

- 标准化提交格式
- 包含日期和时间
- 显示文件统计
- Emoji 图标美化

## 🔍 故障排除

### 常见问题

1. **推送失败**

   ```bash
   # 检查远程仓库连接
   git remote -v

   # 测试连接
   git ls-remote origin
   ```

2. **权限问题**

   ```bash
   # 给脚本执行权限
   chmod +x daily-commit.sh
   ```

3. **合并冲突**

   ```bash
   # 拉取最新代码
   git pull origin main

   # 解决冲突后重新提交
   git add .
   git commit -m "解决合并冲突"
   git push origin main
   ```

## 📞 技术支持

如遇问题请联系：

- 电话：18221165813
- 邮箱：<EMAIL>

---

**Git 仓库已配置完成，可以开始每日自动提交！** 🎉
