# Shanghai Hege Technology - GitLab Runner Tags Validation Script
# Validate that all jobs have proper runner tags configured

param(
    [string]$GitLabCIFile = ".gitlab-ci.yml"
)

$ErrorActionPreference = "Continue"

Write-Host "=== GitLab Runner Tags Validation ===" -ForegroundColor Green
Write-Host "Validating runner tags in $GitLabCIFile..." -ForegroundColor Yellow

if (-not (Test-Path $GitLabCIFile)) {
    Write-Host "ERROR: GitLab CI file not found: $GitLabCIFile" -ForegroundColor Red
    exit 1
}

# Read the GitLab CI file
$content = Get-Content $GitLabCIFile -Raw

# Extract job names (lines that end with : and are not indented)
$jobPattern = '^([a-zA-Z0-9_:-]+):\s*$'
$jobs = @()

$lines = Get-Content $GitLabCIFile
for ($i = 0; $i -lt $lines.Count; $i++) {
    $line = $lines[$i]
    if ($line -match $jobPattern -and -not $line.StartsWith(' ') -and -not $line.StartsWith('#')) {
        $jobName = $matches[1]
        # Skip global configuration sections
        if ($jobName -notin @('stages', 'variables', 'cache', 'default', 'include')) {
            $jobs += @{
                Name = $jobName
                LineNumber = $i + 1
                HasTags = $false
                Tags = @()
            }
        }
    }
}

Write-Host "`nFound $($jobs.Count) jobs to validate:" -ForegroundColor Cyan
foreach ($job in $jobs) {
    Write-Host "  - $($job.Name) (line $($job.LineNumber))" -ForegroundColor White
}

# Check each job for tags
Write-Host "`n=== Checking Runner Tags ===" -ForegroundColor Cyan

$jobsWithoutTags = @()
$jobsWithTags = @()

foreach ($job in $jobs) {
    $jobName = $job.Name
    $startLine = $job.LineNumber
    
    # Look for tags in the next 20 lines after job definition
    $foundTags = $false
    $tags = @()
    
    for ($i = $startLine; $i -lt [Math]::Min($startLine + 20, $lines.Count); $i++) {
        $line = $lines[$i]
        
        # Stop if we hit another job definition
        if ($line -match '^[a-zA-Z0-9_:-]+:\s*$' -and -not $line.StartsWith(' ') -and $i -ne ($startLine - 1)) {
            break
        }
        
        # Check for tags section
        if ($line -match '^\s+tags:\s*$') {
            $foundTags = $true
            # Get the tags from following lines
            for ($j = $i + 1; $j -lt [Math]::Min($i + 10, $lines.Count); $j++) {
                $tagLine = $lines[$j]
                if ($tagLine -match '^\s+- (.+)$') {
                    $tags += $matches[1].Trim()
                } elseif ($tagLine -match '^\s+[a-zA-Z]' -and -not $tagLine.StartsWith('    -')) {
                    # Hit another configuration section
                    break
                }
            }
            break
        }
    }
    
    $job.HasTags = $foundTags
    $job.Tags = $tags
    
    if ($foundTags) {
        Write-Host "✓ $jobName has tags: $($tags -join ', ')" -ForegroundColor Green
        $jobsWithTags += $job
    } else {
        Write-Host "✗ $jobName missing tags" -ForegroundColor Red
        $jobsWithoutTags += $job
    }
}

# Check for default tags
Write-Host "`n=== Checking Default Configuration ===" -ForegroundColor Cyan
$hasDefaultTags = $content -match 'default:\s*\n.*tags:'
if ($hasDefaultTags) {
    Write-Host "✓ Default tags configuration found" -ForegroundColor Green
    # Extract default tags
    if ($content -match 'default:.*?tags:(.*?)(?=\n\S|\n\n|\Z)') {
        $defaultTagsSection = $matches[1]
        $defaultTags = @()
        $defaultTagsSection -split '\n' | ForEach-Object {
            if ($_ -match '^\s*- (.+)$') {
                $defaultTags += $matches[1].Trim()
            }
        }
        Write-Host "  Default tags: $($defaultTags -join ', ')" -ForegroundColor White
    }
} else {
    Write-Host "⚠ No default tags configuration found" -ForegroundColor Yellow
}

# Summary
Write-Host "`n=== Validation Summary ===" -ForegroundColor Green
Write-Host "Total jobs: $($jobs.Count)" -ForegroundColor White
Write-Host "Jobs with tags: $($jobsWithTags.Count)" -ForegroundColor Green
Write-Host "Jobs without tags: $($jobsWithoutTags.Count)" -ForegroundColor Red

if ($jobsWithoutTags.Count -gt 0) {
    Write-Host "`nJobs missing tags:" -ForegroundColor Red
    foreach ($job in $jobsWithoutTags) {
        Write-Host "  - $($job.Name) (line $($job.LineNumber))" -ForegroundColor Red
    }
    
    Write-Host "`nRecommendation: Add the following tags to each job:" -ForegroundColor Yellow
    Write-Host "  tags:" -ForegroundColor White
    Write-Host "    - windows" -ForegroundColor White
    Write-Host "    - shell" -ForegroundColor White
}

# Check for common Windows runner tags
Write-Host "`n=== Windows Runner Tags Check ===" -ForegroundColor Cyan
$windowsJobs = $jobsWithTags | Where-Object { $_.Tags -contains 'windows' }
$shellJobs = $jobsWithTags | Where-Object { $_.Tags -contains 'shell' }

Write-Host "Jobs with 'windows' tag: $($windowsJobs.Count)" -ForegroundColor White
Write-Host "Jobs with 'shell' tag: $($shellJobs.Count)" -ForegroundColor White

if ($jobsWithoutTags.Count -eq 0) {
    Write-Host "`n🎉 All jobs have runner tags configured!" -ForegroundColor Green
    Write-Host "GitLab CI/CD should be able to find available runners." -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n❌ Some jobs are missing runner tags." -ForegroundColor Red
    Write-Host "These jobs will be blocked until tags are added." -ForegroundColor Red
    exit 1
}
