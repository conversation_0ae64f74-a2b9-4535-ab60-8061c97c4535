"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import {
  ExternalLink,
  Calendar,
  Users,
  Award,
  TrendingUp,
  Zap,
  Shield,
  Smartphone,
  RefreshCw,
} from "lucide-react";

interface CaseData {
  id: number;
  title: string;
  client: string;
  category: string;
  description: string;
  challenge: string;
  solution: string;
  results: string[];
  technologies: string[];
  duration: string;
  teamSize: string;
  image: string;
  achievements: {
    metric: string;
    value: string;
    description: string;
  }[];
}

const SuccessCases = () => {
  const [selectedCase, setSelectedCase] = useState<number | null>(null);
  const [casesData, setCasesData] = useState<CaseData[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 从API获取成功案例数据
  const fetchCasesData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch("/api/success-cases");
      const result = await response.json();

      if (result.success) {
        setCasesData(result.data.cases);
        setStatistics(result.data.statistics);
      } else {
        setError(result.message || "获取数据失败");
      }
    } catch (err) {
      setError("网络请求失败，请稍后重试");
      console.error("获取成功案例数据失败:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCasesData();
  }, []);

  // 根据类别映射图标和渐变色
  const getCategoryIcon = (category: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      智能座舱: <Smartphone className="w-8 h-8" />,
      云服务: <TrendingUp className="w-8 h-8" />,
      人工智能: <Zap className="w-8 h-8" />,
      网络安全: <Shield className="w-8 h-8" />,
      数字化转型: <Award className="w-8 h-8" />,
      大数据: <Users className="w-8 h-8" />,
    };
    return iconMap[category] || <Award className="w-8 h-8" />;
  };

  const getCategoryGradient = (category: string) => {
    const gradientMap: { [key: string]: string } = {
      智能座舱: "from-blue-500 to-purple-600",
      云服务: "from-green-500 to-teal-600",
      人工智能: "from-purple-500 to-pink-600",
      网络安全: "from-red-500 to-orange-600",
      数字化转型: "from-indigo-500 to-blue-600",
      大数据: "from-cyan-500 to-blue-600",
    };
    return gradientMap[category] || "from-gray-500 to-gray-600";
  };

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              成功案例
            </h2>
            <button
              onClick={fetchCasesData}
              disabled={loading}
              className="ml-4 p-2 text-gray-500 hover:text-blue-600 transition-colors disabled:opacity-50"
              title="刷新数据"
            >
              <RefreshCw
                className={`w-5 h-5 ${loading ? "animate-spin" : ""}`}
              />
            </button>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            我们为汽车行业领军企业提供专业的技术解决方案，助力客户实现数字化转型和业务创新
          </p>
          {statistics && (
            <p className="text-sm text-gray-500 mt-2">
              最后更新:{" "}
              {new Date(statistics.lastUpdated).toLocaleString("zh-CN")}
            </p>
          )}
        </div>

        {/* 加载状态 */}
        {loading && (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">正在加载成功案例...</span>
          </div>
        )}

        {/* 错误状态 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <div className="flex items-center">
              <div className="text-red-600 mr-3">⚠️</div>
              <div>
                <h3 className="text-red-800 font-medium">数据加载失败</h3>
                <p className="text-red-600 text-sm mt-1">{error}</p>
                <button
                  onClick={fetchCasesData}
                  className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                >
                  点击重试
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 案例网格 */}
        {!loading && !error && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {casesData.map((caseItem, index) => (
              <div
                key={caseItem.id}
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
                onClick={() =>
                  setSelectedCase(
                    selectedCase === caseItem.id ? null : caseItem.id,
                  )
                }
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* 案例头部 */}
                <div
                  className={`h-48 bg-gradient-to-br ${getCategoryGradient(caseItem.category)} flex items-center justify-center relative overflow-hidden`}
                >
                  <div className="absolute inset-0 bg-black/20"></div>
                  <div className="text-white text-center z-10">
                    <div className="mb-4">
                      {getCategoryIcon(caseItem.category)}
                    </div>
                    <h3 className="text-xl font-bold mb-2">{caseItem.title}</h3>
                    <p className="text-sm opacity-90">{caseItem.client}</p>
                  </div>
                  {/* 客户LOGO */}
                  <div className="absolute top-4 right-4 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                    <Image
                      src={caseItem.image}
                      alt={caseItem.client}
                      width={32}
                      height={32}
                      className="object-contain filter brightness-0 invert"
                    />
                  </div>
                </div>

                {/* 案例内容 */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium">
                      {caseItem.category}
                    </span>
                    <div className="flex items-center text-gray-500 text-sm">
                      <Calendar className="w-4 h-4 mr-1" />
                      {caseItem.duration}
                    </div>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {caseItem.description}
                  </p>

                  {/* 关键指标 */}
                  <div className="grid grid-cols-3 gap-2 mb-4">
                    {caseItem.achievements
                      .slice(0, 3)
                      .map((achievement, idx) => (
                        <div key={idx} className="text-center">
                          <div className="text-lg font-bold text-blue-600">
                            {achievement.value}
                          </div>
                          <div className="text-xs text-gray-500">
                            {achievement.metric}
                          </div>
                        </div>
                      ))}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-gray-500 text-sm">
                      <Users className="w-4 h-4 mr-1" />
                      团队 {caseItem.teamSize}
                    </div>
                    <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                      查看详情
                      <ExternalLink className="w-4 h-4 ml-1" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* 详细信息弹窗 */}
        {selectedCase && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              {(() => {
                const caseData = casesData.find((c) => c.id === selectedCase)!;
                return (
                  <div>
                    {/* 弹窗头部 */}
                    <div
                      className={`bg-gradient-to-br ${getCategoryGradient(caseData.category)} text-white p-8 relative`}
                    >
                      <button
                        onClick={() => setSelectedCase(null)}
                        className="absolute top-4 right-4 text-white/80 hover:text-white text-2xl"
                      >
                        ×
                      </button>
                      <div className="flex items-center mb-4">
                        <div className="mr-4">
                          {getCategoryIcon(caseData.category)}
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold mb-2">
                            {caseData.title}
                          </h3>
                          <p className="text-lg opacity-90">
                            {caseData.client}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* 弹窗内容 */}
                    <div className="p-8">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* 左侧内容 */}
                        <div>
                          <h4 className="text-lg font-bold text-gray-900 mb-3">
                            项目挑战
                          </h4>
                          <p className="text-gray-600 mb-6">
                            {caseData.challenge}
                          </p>

                          <h4 className="text-lg font-bold text-gray-900 mb-3">
                            解决方案
                          </h4>
                          <p className="text-gray-600 mb-6">
                            {caseData.solution}
                          </p>

                          <h4 className="text-lg font-bold text-gray-900 mb-3">
                            技术栈
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {caseData.technologies.map((tech, idx) => (
                              <span
                                key={idx}
                                className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                              >
                                {tech}
                              </span>
                            ))}
                          </div>
                        </div>

                        {/* 右侧内容 */}
                        <div>
                          <h4 className="text-lg font-bold text-gray-900 mb-3">
                            项目成果
                          </h4>
                          <ul className="space-y-2 mb-6">
                            {caseData.results.map((result, idx) => (
                              <li
                                key={idx}
                                className="flex items-center text-gray-600"
                              >
                                <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                {result}
                              </li>
                            ))}
                          </ul>

                          <h4 className="text-lg font-bold text-gray-900 mb-3">
                            关键指标
                          </h4>
                          <div className="grid grid-cols-1 gap-4">
                            {caseData.achievements.map((achievement, idx) => (
                              <div
                                key={idx}
                                className="bg-gray-50 p-4 rounded-lg"
                              >
                                <div className="text-2xl font-bold text-blue-600 mb-1">
                                  {achievement.value}
                                </div>
                                <div className="text-sm font-medium text-gray-900 mb-1">
                                  {achievement.metric}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {achievement.description}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        )}

        {/* 统计数据 */}
        {!loading && !error && statistics && (
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {statistics.totalCases}+
                </div>
                <div className="text-gray-600">成功案例</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {statistics.totalClients}+
                </div>
                <div className="text-gray-600">汽车品牌</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {statistics.averageSatisfaction}
                </div>
                <div className="text-gray-600">客户满意度</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-orange-600 mb-2">
                  {statistics.totalExperts}
                </div>
                <div className="text-gray-600">技术专家</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default SuccessCases;
