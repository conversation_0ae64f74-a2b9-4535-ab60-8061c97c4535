// 🚀 上海荷阁科技 - 简化的 Jest 测试配置
// 用于单元测试，避免复杂的配置问题

const path = require("path");

// 获取项目根目录
const rootDir = path.resolve(__dirname, "../../../");

module.exports = {
  // 基础配置
  displayName: "上海荷阁科技 - 单元测试",
  rootDir: rootDir,
  testEnvironment: "jsdom",

  // 测试文件匹配
  testMatch: [
    "<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}",
    "<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}",
  ],

  // 忽略的测试文件
  testPathIgnorePatterns: [
    "<rootDir>/.next/",
    "<rootDir>/node_modules/",
    "<rootDir>/out/",
    "<rootDir>/build/",
    "<rootDir>/dist/",
  ],

  // 模块名称映射
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/src/$1",
    "\\.(css|less|scss|sass)$": "identity-obj-proxy",
    "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$":
      "<rootDir>/.cicd/testing/jest/__mocks__/fileMock.js",
  },

  // 设置文件
  setupFilesAfterEnv: ["<rootDir>/.cicd/testing/jest/jest.setup.js"],

  // 覆盖率配置
  collectCoverage: true,
  collectCoverageFrom: [
    "src/**/*.{js,jsx,ts,tsx}",
    "!src/**/*.d.ts",
    "!src/**/__tests__/**",
    "!src/**/*.{test,spec}.{js,jsx,ts,tsx}",
  ],

  coverageDirectory: "<rootDir>/coverage",
  coverageReporters: ["text", "lcov", "cobertura", "json-summary"],

  // 转换配置
  transform: {
    "^.+\\.(js|jsx|ts|tsx)$": ["babel-jest", { presets: ["next/babel"] }],
  },

  // 转换忽略模式
  transformIgnorePatterns: [
    "/node_modules/",
    "^.+\\.module\\.(css|sass|scss)$",
  ],

  // 测试环境配置
  testEnvironmentOptions: {
    url: "http://localhost:3000",
  },

  // 报告配置
  reporters: [
    "default",
    [
      "jest-junit",
      {
        outputDirectory: "<rootDir>/coverage",
        outputName: "junit.xml",
        classNameTemplate: "{classname}",
        titleTemplate: "{title}",
      },
    ],
  ],

  // 性能配置
  maxWorkers: "50%",
  testTimeout: 10000,

  // 清除模拟
  clearMocks: true,
  restoreMocks: true,

  // 详细输出
  verbose: true,
};
