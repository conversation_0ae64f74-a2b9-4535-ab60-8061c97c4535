# 🚀 上海荷阁科技 - Windows 开发环境设置脚本
# 自动安装和配置 Windows 开发环境所需的工具

param(
    [switch]$InstallNode,
    [switch]$InstallDocker,
    [switch]$InstallGit,
    [switch]$InstallVSCode,
    [switch]$InstallAll,
    [switch]$Help
)

# 显示帮助信息
if ($Help) {
    Write-Host @"
🚀 上海荷阁科技 Windows 开发环境设置脚本

用法: .\setup.ps1 [参数]

参数:
    -InstallNode        安装 Node.js 和 npm
    -InstallDocker      安装 Docker Desktop
    -InstallGit         安装 Git
    -InstallVSCode      安装 Visual Studio Code
    -InstallAll         安装所有工具
    -Help               显示此帮助信息

示例:
    .\setup.ps1 -InstallAll                    # 安装所有工具
    .\setup.ps1 -InstallNode -InstallDocker    # 只安装 Node.js 和 Docker

注意: 需要管理员权限运行此脚本
"@
    exit 0
}

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "❌ 此脚本需要管理员权限运行"
    Write-Host "请以管理员身份运行 PowerShell 并重新执行此脚本"
    exit 1
}

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colors = @{
        "Red" = [ConsoleColor]::Red
        "Green" = [ConsoleColor]::Green
        "Yellow" = [ConsoleColor]::Yellow
        "Blue" = [ConsoleColor]::Blue
        "White" = [ConsoleColor]::White
    }
    
    Write-Host $Message -ForegroundColor $colors[$Color]
}

function Write-Info { param([string]$Message) Write-ColorOutput "ℹ️ $Message" "Blue" }
function Write-Success { param([string]$Message) Write-ColorOutput "✅ $Message" "Green" }
function Write-Warning { param([string]$Message) Write-ColorOutput "⚠️ $Message" "Yellow" }
function Write-Error { param([string]$Message) Write-ColorOutput "❌ $Message" "Red" }

# 检查是否安装了 Chocolatey
function Test-Chocolatey {
    try {
        choco --version | Out-Null
        return $true
    } catch {
        return $false
    }
}

# 安装 Chocolatey
function Install-Chocolatey {
    Write-Info "安装 Chocolatey 包管理器..."
    
    try {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        
        # 刷新环境变量
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        Write-Success "Chocolatey 安装完成"
        return $true
    } catch {
        Write-Error "Chocolatey 安装失败: $($_.Exception.Message)"
        return $false
    }
}

# 检查软件是否已安装
function Test-Software {
    param([string]$Name, [string]$Command)
    
    try {
        Invoke-Expression "$Command --version" | Out-Null
        Write-Success "$Name 已安装"
        return $true
    } catch {
        Write-Info "$Name 未安装"
        return $false
    }
}

# 使用 Chocolatey 安装软件
function Install-WithChocolatey {
    param([string]$PackageName, [string]$DisplayName)
    
    Write-Info "安装 $DisplayName..."
    
    try {
        choco install $PackageName -y
        Write-Success "$DisplayName 安装完成"
        return $true
    } catch {
        Write-Error "$DisplayName 安装失败: $($_.Exception.Message)"
        return $false
    }
}

# 安装 Node.js
function Install-NodeJS {
    Write-Info "检查 Node.js..."
    
    if (Test-Software "Node.js" "node") {
        $nodeVersion = node --version
        Write-Info "当前 Node.js 版本: $nodeVersion"
        
        $continue = Read-Host "是否重新安装 Node.js? (y/N)"
        if ($continue -ne "y" -and $continue -ne "Y") {
            return $true
        }
    }
    
    $success = Install-WithChocolatey "nodejs" "Node.js"
    
    if ($success) {
        # 刷新环境变量
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        # 验证安装
        try {
            $nodeVersion = node --version
            $npmVersion = npm --version
            Write-Success "Node.js 版本: $nodeVersion"
            Write-Success "npm 版本: $npmVersion"
            
            # 配置 npm
            Write-Info "配置 npm..."
            npm config set registry https://registry.npmmirror.com
            npm config set cache "C:\npm-cache"
            npm config set prefix "C:\npm-global"
            
            Write-Success "npm 配置完成"
        } catch {
            Write-Warning "Node.js 安装可能不完整"
        }
    }
    
    return $success
}

# 安装 Docker Desktop
function Install-Docker {
    Write-Info "检查 Docker..."
    
    if (Test-Software "Docker" "docker") {
        $dockerVersion = docker --version
        Write-Info "当前 Docker 版本: $dockerVersion"
        
        $continue = Read-Host "是否重新安装 Docker Desktop? (y/N)"
        if ($continue -ne "y" -and $continue -ne "Y") {
            return $true
        }
    }
    
    Write-Info "安装 Docker Desktop..."
    Write-Warning "Docker Desktop 需要 Windows 10/11 专业版或企业版"
    Write-Warning "安装完成后需要重启计算机"
    
    $success = Install-WithChocolatey "docker-desktop" "Docker Desktop"
    
    if ($success) {
        Write-Info "启用 Windows 功能..."
        
        # 启用 Hyper-V
        try {
            Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All -NoRestart
            Write-Success "Hyper-V 功能已启用"
        } catch {
            Write-Warning "启用 Hyper-V 失败，可能需要手动启用"
        }
        
        # 启用容器功能
        try {
            Enable-WindowsOptionalFeature -Online -FeatureName Containers -All -NoRestart
            Write-Success "容器功能已启用"
        } catch {
            Write-Warning "启用容器功能失败，可能需要手动启用"
        }
        
        Write-Warning "请重启计算机以完成 Docker 安装"
    }
    
    return $success
}

# 安装 Git
function Install-Git {
    Write-Info "检查 Git..."
    
    if (Test-Software "Git" "git") {
        $gitVersion = git --version
        Write-Info "当前 Git 版本: $gitVersion"
        
        $continue = Read-Host "是否重新安装 Git? (y/N)"
        if ($continue -ne "y" -and $continue -ne "Y") {
            return $true
        }
    }
    
    $success = Install-WithChocolatey "git" "Git"
    
    if ($success) {
        # 刷新环境变量
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        # 基本配置
        Write-Info "配置 Git..."
        try {
            git config --global init.defaultBranch main
            git config --global core.autocrlf true
            git config --global core.safecrlf false
            git config --global pull.rebase false
            
            Write-Success "Git 基本配置完成"
            Write-Info "请手动设置用户信息:"
            Write-Info "  git config --global user.name `"您的姓名`""
            Write-Info "  git config --global user.email `"您的邮箱`""
        } catch {
            Write-Warning "Git 配置失败"
        }
    }
    
    return $success
}

# 安装 Visual Studio Code
function Install-VSCode {
    Write-Info "检查 Visual Studio Code..."
    
    if (Test-Path "${env:ProgramFiles}\Microsoft VS Code\Code.exe") {
        Write-Success "Visual Studio Code 已安装"
        
        $continue = Read-Host "是否重新安装 Visual Studio Code? (y/N)"
        if ($continue -ne "y" -and $continue -ne "Y") {
            return $true
        }
    }
    
    $success = Install-WithChocolatey "vscode" "Visual Studio Code"
    
    if ($success) {
        Write-Info "安装推荐的 VS Code 扩展..."
        
        $extensions = @(
            "ms-vscode.vscode-typescript-next",
            "bradlc.vscode-tailwindcss",
            "esbenp.prettier-vscode",
            "ms-vscode.vscode-eslint",
            "ms-vscode.vscode-json",
            "ms-playwright.playwright",
            "ms-vscode.vscode-docker"
        )
        
        foreach ($extension in $extensions) {
            try {
                code --install-extension $extension --force
                Write-Success "已安装扩展: $extension"
            } catch {
                Write-Warning "安装扩展失败: $extension"
            }
        }
    }
    
    return $success
}

# 配置开发环境
function Configure-Environment {
    Write-Info "配置开发环境..."
    
    # 创建开发目录
    $devDirs = @(
        "C:\Dev",
        "C:\Dev\Projects",
        "C:\Dev\Tools",
        "C:\npm-cache",
        "C:\npm-global"
    )
    
    foreach ($dir in $devDirs) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Success "创建目录: $dir"
        }
    }
    
    # 设置环境变量
    try {
        [Environment]::SetEnvironmentVariable("DEV_HOME", "C:\Dev", "Machine")
        [Environment]::SetEnvironmentVariable("NPM_CONFIG_CACHE", "C:\npm-cache", "Machine")
        [Environment]::SetEnvironmentVariable("NPM_CONFIG_PREFIX", "C:\npm-global", "Machine")
        
        # 添加到 PATH
        $currentPath = [Environment]::GetEnvironmentVariable("Path", "Machine")
        if ($currentPath -notlike "*C:\npm-global*") {
            [Environment]::SetEnvironmentVariable("Path", "$currentPath;C:\npm-global", "Machine")
        }
        
        Write-Success "环境变量配置完成"
    } catch {
        Write-Warning "环境变量配置失败"
    }
    
    # 配置 PowerShell 执行策略
    try {
        Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope LocalMachine -Force
        Write-Success "PowerShell 执行策略已配置"
    } catch {
        Write-Warning "PowerShell 执行策略配置失败"
    }
}

# 主函数
function Main {
    Write-Info "🚀 开始设置 Windows 开发环境..."
    Write-Info "项目: 上海荷阁科技企业官网"
    Write-Info ""
    
    # 检查并安装 Chocolatey
    if (-not (Test-Chocolatey)) {
        if (-not (Install-Chocolatey)) {
            Write-Error "Chocolatey 安装失败，无法继续"
            exit 1
        }
    } else {
        Write-Success "Chocolatey 已安装"
    }
    
    # 根据参数安装软件
    $installAll = $InstallAll
    $anyInstall = $InstallNode -or $InstallDocker -or $InstallGit -or $InstallVSCode -or $installAll
    
    if (-not $anyInstall) {
        Write-Info "未指定安装选项，将安装所有工具"
        $installAll = $true
    }
    
    $results = @{}
    
    if ($InstallNode -or $installAll) {
        $results["Node.js"] = Install-NodeJS
    }
    
    if ($InstallGit -or $installAll) {
        $results["Git"] = Install-Git
    }
    
    if ($InstallVSCode -or $installAll) {
        $results["VS Code"] = Install-VSCode
    }
    
    if ($InstallDocker -or $installAll) {
        $results["Docker"] = Install-Docker
    }
    
    # 配置环境
    Configure-Environment
    
    # 显示安装结果
    Write-Info ""
    Write-Info "📋 安装结果:"
    foreach ($software in $results.Keys) {
        if ($results[$software]) {
            Write-Success "$software - 安装成功"
        } else {
            Write-Error "$software - 安装失败"
        }
    }
    
    Write-Info ""
    Write-Success "🎉 Windows 开发环境设置完成！"
    Write-Info ""
    Write-Info "下一步操作:"
    Write-Info "1. 重启计算机（如果安装了 Docker）"
    Write-Info "2. 配置 Git 用户信息"
    Write-Info "3. 克隆项目代码"
    Write-Info "4. 运行 npm install 安装项目依赖"
    Write-Info ""
    Write-Info "项目相关命令:"
    Write-Info "  npm run dev          # 启动开发服务器"
    Write-Info "  npm run build        # 构建生产版本"
    Write-Info "  npm run test         # 运行测试"
    Write-Info "  npm run lint         # 代码检查"
}

# 执行主函数
Main
