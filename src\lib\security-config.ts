/**
 * 网络安全配置文件
 * 集中管理所有安全相关的配置
 */

// ===========================================
// 基础安全配置
// ===========================================

export const SECURITY_CONFIG = {
  // 速率限制配置
  RATE_LIMITS: {
    API_DEFAULT: { requests: 100, window: 60000 }, // 每分钟100次
    CONTACT_FORM: { requests: 5, window: 3600000 }, // 每小时5次
    FILE_UPLOAD: { requests: 10, window: 3600000 }, // 每小时10次
    VISITOR_STATS: { requests: 60, window: 60000 }, // 每分钟60次
    SECURITY_LOGS: { requests: 10, window: 60000 }, // 每分钟10次
  },
  
  // 文件上传配置
  FILE_UPLOAD: {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: [
      'image/jpeg',
      'image/png', 
      'image/webp',
      'application/pdf'
    ],
    ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.webp', '.pdf'],
    UPLOAD_DIR: './public/uploads'
  },
  
  // 输入验证配置
  INPUT_VALIDATION: {
    MAX_STRING_LENGTH: 1000,
    MAX_NAME_LENGTH: 50,
    MAX_EMAIL_LENGTH: 254,
    MAX_PHONE_LENGTH: 20,
    MAX_URL_LENGTH: 2048
  },
  
  // 会话配置
  SESSION: {
    MAX_AGE: 24 * 60 * 60 * 1000, // 24小时
    SECURE_COOKIES: process.env.NODE_ENV === 'production',
    SAME_SITE: 'strict' as const,
    HTTP_ONLY: true
  },
  
  // 密码策略
  PASSWORD_POLICY: {
    MIN_LENGTH: 8,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SPECIAL_CHARS: true,
    BCRYPT_ROUNDS: 12
  }
}

// ===========================================
// HTTP安全头部配置
// ===========================================

export const SECURITY_HEADERS = {
  // 内容安全策略
  CONTENT_SECURITY_POLICY: [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: http:",
    "connect-src 'self' https://www.google-analytics.com https://cloud.tencent.com",
    "frame-src 'none'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; '),
  
  // 其他安全头部
  X_FRAME_OPTIONS: 'DENY',
  X_CONTENT_TYPE_OPTIONS: 'nosniff',
  X_XSS_PROTECTION: '1; mode=block',
  REFERRER_POLICY: 'strict-origin-when-cross-origin',
  STRICT_TRANSPORT_SECURITY: 'max-age=31536000; includeSubDomains; preload',
  X_DNS_PREFETCH_CONTROL: 'off',
  X_DOWNLOAD_OPTIONS: 'noopen',
  X_PERMITTED_CROSS_DOMAIN_POLICIES: 'none',
  
  // 权限策略
  PERMISSIONS_POLICY: [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'gyroscope=()',
    'accelerometer=()'
  ].join(', ')
}

// ===========================================
// IP白名单配置
// ===========================================

export const IP_WHITELIST = {
  ADMIN_IPS: [
    '127.0.0.1',
    '::1',
    'localhost'
  ],
  
  LOCAL_NETWORK_PATTERNS: [
    /^192\.168\./,
    /^10\./,
    /^172\.(1[6-9]|2[0-9]|3[0-1])\./
  ]
}

// ===========================================
// 可疑活动检测配置
// ===========================================

export const THREAT_DETECTION = {
  // 可疑User-Agent模式
  SUSPICIOUS_USER_AGENTS: [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /java/i,
    /go-http/i,
    /scanner/i,
    /exploit/i
  ],
  
  // 允许的搜索引擎爬虫
  ALLOWED_BOTS: [
    /googlebot/i,
    /bingbot/i,
    /baiduspider/i,
    /yandexbot/i,
    /slurp/i, // Yahoo
    /duckduckbot/i
  ],
  
  // 恶意路径模式
  MALICIOUS_PATHS: [
    /\/\.env/,
    /\/\.git/,
    /\/admin/,
    /\/wp-admin/,
    /\/wp-login/,
    /\/phpmyadmin/,
    /\/config/,
    /\/backup/,
    /\/database/,
    /\/sql/,
    /\/shell/,
    /\/cmd/,
    /\/eval/,
    /\/exec/,
    // SQL注入尝试
    /union.*select/i,
    /select.*from/i,
    /insert.*into/i,
    /delete.*from/i,
    /drop.*table/i,
    // XSS尝试
    /<script/i,
    /javascript:/i,
    /on\w+=/i,
    // 路径遍历
    /\.\.\//,
    /\.\.%2f/i,
    /\.\.%5c/i
  ],
  
  // 垃圾信息模式
  SPAM_PATTERNS: [
    /viagra/i,
    /casino/i,
    /lottery/i,
    /winner/i,
    /congratulations/i,
    /click here/i,
    /free money/i,
    /make money/i,
    /work from home/i,
    /http[s]?:\/\//i, // 包含链接
    /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/ // 信用卡号码模式
  ]
}

// ===========================================
// 文件安全配置
// ===========================================

export const FILE_SECURITY = {
  // 文件魔数（用于验证文件类型）
  MAGIC_NUMBERS: {
    'image/jpeg': [0xFF, 0xD8, 0xFF],
    'image/png': [0x89, 0x50, 0x4E, 0x47],
    'image/webp': [0x52, 0x49, 0x46, 0x46], // RIFF
    'application/pdf': [0x25, 0x50, 0x44, 0x46] // %PDF
  },
  
  // 恶意文件内容模式
  MALICIOUS_CONTENT_PATTERNS: [
    /<script/i,
    /javascript:/i,
    /on\w+=/i,
    /eval\(/i,
    /exec\(/i,
    /system\(/i,
    /shell_exec/i,
    /<?php/i,
    /<%/i
  ]
}

// ===========================================
// 环境配置
// ===========================================

export const ENVIRONMENT_CONFIG = {
  IS_PRODUCTION: process.env.NODE_ENV === 'production',
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  
  // 安全功能开关
  ENABLE_SECURITY_LOGS: process.env.ENABLE_SECURITY_LOGS === 'true',
  ENABLE_RATE_LIMITING: process.env.ENABLE_RATE_LIMITING !== 'false',
  ENABLE_CSRF_PROTECTION: process.env.ENABLE_CSRF_PROTECTION !== 'false',
  ENABLE_XSS_PROTECTION: process.env.ENABLE_XSS_PROTECTION !== 'false',
  
  // 调试配置
  DEBUG_MODE: process.env.DEBUG === 'true',
  SHOW_DETAILED_ERRORS: process.env.SHOW_DETAILED_ERRORS === 'true' && process.env.NODE_ENV === 'development'
}

// ===========================================
// 导出默认配置
// ===========================================

export default {
  SECURITY_CONFIG,
  SECURITY_HEADERS,
  IP_WHITELIST,
  THREAT_DETECTION,
  FILE_SECURITY,
  ENVIRONMENT_CONFIG
}
