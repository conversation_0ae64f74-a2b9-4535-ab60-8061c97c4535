# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js build output
.next/
out/

# Production build
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cache files
.cache/
*.tsbuildinfo
.eslintcache

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Temporary folders
tmp/
temp/

# Compressed files - 不提交压缩包
*.zip
*.tar.gz
*.rar
*.7z

# Backup files
*.bak
*.backup

# Local development files
.vercel
.turbo

# Testing
coverage/
.nyc_output

# Lint and format check outputs
lint-output.txt
lint-results.json
lint-results.xml
lint-summary.md
format-check-output.txt

# Storybook build outputs
.out
.storybook-out

# Temporary files
*.tmp
*.temp

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Build artifacts
*.tgz
*.tar

# Local configuration
.env.*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# MacOS
.AppleDouble
.LSOverride

# Windows
desktop.ini

# Linux
*~

# Project specific ignores
CLEANUP_SUMMARY.md
网站使用说明.md
DEPLOYMENT_GUIDE.md
