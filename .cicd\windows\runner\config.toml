# 🚀 上海荷阁科技 - GitLab Runner Windows 配置
# 针对 Windows 环境优化的 GitLab Runner 配置

concurrent = 2
check_interval = 0
log_level = "info"
log_format = "runner"

[session_server]
  session_timeout = 1800

# ==========================================
# Windows Shell Runner 配置
# ==========================================
[[runners]]
  name = "HEGE-Tech-Windows-Shell"
  url = "http://home.spinach.cool:11991/"
  token = "your-registration-token-here"
  executor = "shell"
  
  # Shell 配置
  shell = "powershell"
  # 备选配置：使用 PowerShell Core
  # shell = "pwsh"
  # 或使用完整路径
  # shell = "C:\\Program Files\\PowerShell\\7\\pwsh.exe"
  
  # 工作目录配置
  builds_dir = "D:\\GitLab-Runner\\builds"
  cache_dir = "D:\\GitLab-Runner\\cache"
  
  # 环境变量
  environment = [
    "GIT_SSL_NO_VERIFY=true",
    "NODE_ENV=test",
    "CI=true",
    "FORCE_COLOR=1",
    "NPM_CONFIG_PROGRESS=false",
    "NPM_CONFIG_LOGLEVEL=warn"
  ]
  
  # 克隆配置
  clone_url = "http://home.spinach.cool:11991/"
  
  # 标签
  tag_list = ["windows", "shell", "nodejs", "docker"]
  
  # 运行未标记的作业
  run_untagged = false
  
  # 锁定到项目
  locked = false
  
  # 访问级别
  access_level = "not_protected"
  
  # 最大作业超时
  maximum_timeout = 3600
  
  # Shell 执行器特定配置
  [runners.custom_build_dir]
    enabled = true
  
  [runners.cache]
    Type = "local"
    Path = "D:\\GitLab-Runner\\cache"
    Shared = false
    
    [runners.cache.local]
      MaxUploadedArchiveSize = 0

# ==========================================
# Windows Docker Runner 配置
# ==========================================
[[runners]]
  name = "HEGE-Tech-Windows-Docker"
  url = "http://home.spinach.cool:11991/"
  token = "your-registration-token-here"
  executor = "docker-windows"
  
  # 工作目录配置
  builds_dir = "D:\\GitLab-Runner\\builds"
  cache_dir = "D:\\GitLab-Runner\\cache"
  
  # 环境变量
  environment = [
    "GIT_SSL_NO_VERIFY=true",
    "DOCKER_DRIVER=overlay2",
    "DOCKER_TLS_CERTDIR=/certs"
  ]
  
  # 标签
  tag_list = ["windows", "docker", "containers"]
  
  # 运行未标记的作业
  run_untagged = false
  
  # Docker 执行器配置
  [runners.docker]
    tls_verify = false
    image = "mcr.microsoft.com/windows/servercore:ltsc2019"
    privileged = false
    disable_entrypoint_overwrite = false
    oom_kill_disable = false
    disable_cache = false
    volumes = [
      "D:\\GitLab-Runner\\cache:C:\\cache:rw"
    ]
    shm_size = 0
    network_mode = "default"
    
    # Windows 特定配置
    isolation = "process"
    
    # 资源限制
    memory = "4g"
    cpus = "2"
    
    # 拉取策略
    pull_policy = ["if-not-present"]
    
    # 等待服务超时
    wait_for_services_timeout = 300
    
    # 允许的镜像
    allowed_images = [
      "mcr.microsoft.com/windows/*",
      "mcr.microsoft.com/dotnet/*",
      "node:*-windowsservercore*"
    ]
    
    # 允许的服务
    allowed_services = [
      "mcr.microsoft.com/mssql/server:*",
      "redis:*-windowsservercore*"
    ]

# ==========================================
# 备用配置 - CMD Shell
# ==========================================
[[runners]]
  name = "HEGE-Tech-Windows-CMD"
  url = "http://home.spinach.cool:11991/"
  token = "your-registration-token-here"
  executor = "shell"
  
  # 使用 CMD
  shell = "cmd"
  
  # 工作目录配置
  builds_dir = "D:\\GitLab-Runner\\builds"
  cache_dir = "D:\\GitLab-Runner\\cache"
  
  # 环境变量
  environment = [
    "GIT_SSL_NO_VERIFY=true",
    "NODE_ENV=test"
  ]
  
  # 标签
  tag_list = ["windows", "cmd", "legacy"]
  
  # 仅在需要时使用
  run_untagged = false
  
  [runners.custom_build_dir]
    enabled = true

# ==========================================
# 全局配置说明
# ==========================================

# 配置文件位置：
# Windows: C:\GitLab-Runner\config.toml
# 或者自定义位置

# 注册命令示例：
# gitlab-runner register --url http://home.spinach.cool:11991/ --registration-token YOUR_TOKEN

# 服务管理命令：
# gitlab-runner install --user "NT AUTHORITY\SYSTEM" --password ""
# gitlab-runner start
# gitlab-runner stop
# gitlab-runner restart

# 日志查看：
# gitlab-runner --debug run

# 配置验证：
# gitlab-runner verify

# ==========================================
# 性能优化建议
# ==========================================

# 1. 使用 SSD 存储构建目录
# 2. 增加内存分配
# 3. 使用本地缓存
# 4. 限制并发作业数量
# 5. 定期清理缓存目录
# 6. 使用 PowerShell Core 提升性能
# 7. 配置防病毒软件排除规则

# ==========================================
# 安全配置建议
# ==========================================

# 1. 使用专用用户账户运行 Runner
# 2. 限制文件系统访问权限
# 3. 配置网络防火墙规则
# 4. 定期更新 Runner 版本
# 5. 监控 Runner 活动日志
# 6. 使用 HTTPS 连接 GitLab
# 7. 定期轮换注册令牌
