# 🚀 上海荷阁科技 - Trivy 容器安全扫描配置
# 用于 Docker 镜像安全漏洞扫描

# ==========================================
# 基础配置
# ==========================================
format: json
output: trivy-report.json
exit-code: 1
severity: HIGH,CRITICAL

# ==========================================
# 扫描配置
# ==========================================
scan:
  # 扫描类型
  types:
    - vuln      # 漏洞扫描
    - config    # 配置扫描
    - secret    # 密钥扫描
    - license   # 许可证扫描
  
  # 扫描目标
  targets:
    - image     # 容器镜像
    - fs        # 文件系统
    - repo      # 代码仓库
  
  # 超时设置 (分钟)
  timeout: 30m
  
  # 并发扫描数
  parallel: 4

# ==========================================
# 漏洞扫描配置
# ==========================================
vulnerability:
  # 漏洞数据库
  db:
    # 自动更新
    auto_update: true
    
    # 更新间隔
    update_interval: 24h
    
    # 数据库路径
    cache_dir: .trivy-cache
  
  # 严重性过滤
  severity_filter:
    - CRITICAL
    - HIGH
    - MEDIUM
  
  # 忽略未修复的漏洞
  ignore_unfixed: false
  
  # 忽略策略文件
  ignorefile: .cicd/security/container/.trivyignore

# ==========================================
# 配置扫描
# ==========================================
config:
  # 配置检查类型
  checks:
    - dockerfile
    - kubernetes
    - terraform
  
  # 自定义策略
  policy_paths:
    - .cicd/security/container/policies/
  
  # 严重性阈值
  severity_threshold: MEDIUM

# ==========================================
# 密钥扫描配置
# ==========================================
secret:
  # 启用密钥扫描
  enabled: true
  
  # 自定义规则
  custom_rules:
    - .cicd/security/container/secret-rules.yaml
  
  # 忽略文件
  ignore_patterns:
    - "*.test.js"
    - "*.spec.ts"
    - "test/**"
    - "tests/**"
    - "node_modules/**"

# ==========================================
# 许可证扫描配置
# ==========================================
license:
  # 启用许可证扫描
  enabled: true
  
  # 许可证分类
  categories:
    forbidden:
      - GPL-3.0
      - AGPL-3.0
      - LGPL-3.0
    
    restricted:
      - GPL-2.0
      - LGPL-2.1
    
    allowed:
      - MIT
      - Apache-2.0
      - BSD-3-Clause
      - ISC
      - BSD-2-Clause

# ==========================================
# 报告配置
# ==========================================
reporting:
  # 报告格式
  formats:
    - json
    - table
    - sarif
  
  # 输出文件
  outputs:
    json: trivy-report.json
    table: trivy-report.txt
    sarif: trivy-report.sarif
  
  # 包含详细信息
  include_details: true
  
  # 模板文件
  template: "@contrib/html.tpl"

# ==========================================
# 缓存配置
# ==========================================
cache:
  # 缓存目录
  dir: .trivy-cache
  
  # 清理缓存
  clear: false
  
  # 缓存过期时间
  ttl: 168h  # 7 days

# ==========================================
# 网络配置
# ==========================================
network:
  # 离线模式
  offline: false
  
  # 跳过数据库更新
  skip_db_update: false
  
  # 代理设置
  proxy:
    http: "${HTTP_PROXY}"
    https: "${HTTPS_PROXY}"
    no_proxy: "localhost,127.0.0.1"

# ==========================================
# 过滤配置
# ==========================================
filters:
  # 按包名过滤
  packages:
    exclude:
      - "test-*"
      - "*-test"
      - "mock-*"
  
  # 按文件路径过滤
  paths:
    exclude:
      - "test/**"
      - "tests/**"
      - "**/*.test.js"
      - "**/*.spec.ts"
      - "node_modules/**/test/**"
  
  # 按 CVE ID 过滤
  cves:
    exclude:
      # 示例: 临时忽略特定 CVE
      # - "CVE-2021-44228"

# ==========================================
# 集成配置
# ==========================================
integrations:
  # GitLab CI/CD
  gitlab:
    # 生成 GitLab 安全报告
    security_report: true
    
    # 报告文件路径
    report_path: trivy-gitlab-report.json
  
  # GitHub Actions
  github:
    # 生成 SARIF 报告
    sarif_report: true
    
    # 上传到 GitHub Security
    upload_sarif: false

# ==========================================
# 自定义配置
# ==========================================
custom:
  # 自定义检查器
  checkers:
    - name: "hege-custom-check"
      path: ".cicd/security/container/custom-checks/"
  
  # 环境特定配置
  environments:
    development:
      severity: MEDIUM,HIGH,CRITICAL
      ignore_unfixed: true
    
    staging:
      severity: HIGH,CRITICAL
      ignore_unfixed: false
    
    production:
      severity: CRITICAL
      ignore_unfixed: false
      exit_code: 1

# ==========================================
# 通知配置
# ==========================================
notifications:
  # 启用通知
  enabled: true
  
  # 通知条件
  conditions:
    - severity: CRITICAL
      count: "> 0"
    - severity: HIGH
      count: "> 5"
  
  # 通知渠道
  channels:
    email:
      recipients:
        - "<EMAIL>"
        - "<EMAIL>"
    
    slack:
      webhook: "${SLACK_SECURITY_WEBHOOK}"
      channel: "#security-alerts"
