"use client";

import React from "react";
import { QrCode, Phone, Mail, Clock, User } from "lucide-react";

interface WeChatQRProps {
  size?: "small" | "medium" | "large";
  showContactInfo?: boolean;
  title?: string;
}

const WeChatQR: React.FC<WeChatQRProps> = ({
  size = "medium",
  showContactInfo = true,
  title = "添加专业顾问微信",
}) => {
  const sizeClasses = {
    small: "w-24 h-24",
    medium: "w-32 h-32",
    large: "w-40 h-40",
  };

  const containerClasses = {
    small: "p-3",
    medium: "p-4",
    large: "p-6",
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border text-center ${containerClasses[size]}`}
    >
      <h3 className="text-sm font-semibold text-gray-900 mb-3">{title}</h3>

      {/* 二维码区域 */}
      <div
        className={`${sizeClasses[size]} bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex flex-col items-center justify-center mx-auto mb-3 relative overflow-hidden`}
      >
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>

        {/* 二维码内容 */}
        <div className="relative z-10 text-white text-center">
          <QrCode className="h-8 w-8 mx-auto mb-1" />
          <div className="text-xs font-bold">
            <div>荷阁科技</div>
            <div>企业微信</div>
          </div>
          <div className="text-xs mt-1 opacity-90">扫码添加</div>
        </div>

        {/* 装饰性网格 */}
        <div className="absolute inset-2 border border-white/20 rounded grid grid-cols-3 gap-px">
          {Array.from({ length: 9 }).map((_, i) => (
            <div key={i} className="bg-white/10 rounded-sm"></div>
          ))}
        </div>
      </div>

      <p className="text-xs text-gray-600 mb-3">
        使用微信扫描上方二维码
        <br />
        添加专业顾问获得一对一服务
      </p>

      {showContactInfo && (
        <div className="space-y-2 text-xs text-gray-500 border-t pt-3">
          <div className="flex items-center justify-center gap-1">
            <User className="h-3 w-3" />
            <span>荷阁科技-李工</span>
          </div>
          <div className="flex items-center justify-center gap-1">
            <Phone className="h-3 w-3" />
            <span>18221165813</span>
          </div>
          <div className="flex items-center justify-center gap-1">
            <Mail className="h-3 w-3" />
            <span><EMAIL></span>
          </div>
          <div className="flex items-center justify-center gap-1">
            <Clock className="h-3 w-3" />
            <span>7×24小时在线服务</span>
          </div>
        </div>
      )}

      {/* 添加提示 */}
      <div className="mt-3 p-2 bg-blue-50 rounded text-xs text-blue-700">
        💡 添加时请备注"官网咨询"，享受优先服务
      </div>
    </div>
  );
};

export default WeChatQR;
