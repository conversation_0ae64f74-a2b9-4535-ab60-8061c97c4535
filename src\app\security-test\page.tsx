'use client'

import { useState, useEffect } from 'react'

interface SecurityLog {
  type: string
  ip: string
  userAgent: string
  timestamp: string
  details: any
}

interface SecurityStats {
  total: number
  byType: Record<string, number>
  recentActivity: SecurityLog[]
}

export default function SecurityTestPage() {
  const [logs, setLogs] = useState<SecurityLog[]>([])
  const [stats, setStats] = useState<SecurityStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [testResults, setTestResults] = useState<Record<string, string>>({})

  // 获取安全日志
  const fetchSecurityLogs = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/security/logs', {
        headers: {
          'X-API-Key': 'default-secret-key' // 测试用密钥
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setLogs(data.logs || [])
        setStats(data.stats || null)
      } else {
        console.error('获取安全日志失败:', response.statusText)
      }
    } catch (error) {
      console.error('获取安全日志错误:', error)
    } finally {
      setLoading(false)
    }
  }

  // 测试联系表单安全
  const testContactFormSecurity = async () => {
    const testCases = [
      {
        name: '正常提交',
        data: {
          name: '张三',
          email: '<EMAIL>',
          phone: '13800138000',
          company: '测试公司',
          message: '这是一条正常的测试消息'
        }
      },
      {
        name: 'XSS攻击测试',
        data: {
          name: '<script>alert("XSS")</script>',
          email: '<EMAIL>',
          phone: '13800138000',
          company: '测试公司',
          message: '<img src="x" onerror="alert(\'XSS\')">'
        }
      },
      {
        name: '垃圾信息测试',
        data: {
          name: '张三',
          email: '<EMAIL>',
          phone: '13800138000',
          company: '测试公司',
          message: 'Click here to win free money! Visit http://malicious-site.com'
        }
      },
      {
        name: '超长内容测试',
        data: {
          name: 'A'.repeat(100),
          email: '<EMAIL>',
          phone: '13800138000',
          company: '测试公司',
          message: 'B'.repeat(2000)
        }
      }
    ]

    const results: Record<string, string> = {}

    for (const testCase of testCases) {
      try {
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(testCase.data)
        })

        const result = await response.json()
        results[testCase.name] = response.ok ? '✅ 通过' : `❌ 失败: ${result.message}`
      } catch (error) {
        results[testCase.name] = `❌ 错误: ${error}`
      }
    }

    setTestResults(results)
  }

  // 测试速率限制
  const testRateLimit = async () => {
    const results: Record<string, string> = {}
    
    try {
      // 快速发送多个请求测试速率限制
      const promises = Array.from({ length: 10 }, (_, i) => 
        fetch('/api/visitor-stats', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            currentPage: '/security-test',
            referrer: '',
            userAgent: navigator.userAgent,
            visitCount: i + 1,
            timeOnPage: 1000
          })
        })
      )

      const responses = await Promise.all(promises)
      const successCount = responses.filter(r => r.ok).length
      const rateLimitCount = responses.filter(r => r.status === 429).length

      results['速率限制测试'] = `✅ 成功请求: ${successCount}, 被限制: ${rateLimitCount}`
    } catch (error) {
      results['速率限制测试'] = `❌ 错误: ${error}`
    }

    setTestResults(prev => ({ ...prev, ...results }))
  }

  useEffect(() => {
    fetchSecurityLogs()
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            🛡️ 网络安全测试面板
          </h1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h2 className="text-xl font-semibold text-blue-900 mb-4">安全功能测试</h2>
              <div className="space-y-3">
                <button
                  onClick={testContactFormSecurity}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
                >
                  测试联系表单安全
                </button>
                <button
                  onClick={testRateLimit}
                  className="w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
                >
                  测试速率限制
                </button>
                <button
                  onClick={fetchSecurityLogs}
                  className="w-full bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
                  disabled={loading}
                >
                  {loading ? '加载中...' : '刷新安全日志'}
                </button>
              </div>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <h2 className="text-xl font-semibold text-green-900 mb-4">安全统计</h2>
              {stats ? (
                <div className="space-y-2 text-sm">
                  <div>总事件数: <span className="font-semibold">{stats.total}</span></div>
                  <div>事件类型分布:</div>
                  <ul className="ml-4 space-y-1">
                    {Object.entries(stats.byType).map(([type, count]) => (
                      <li key={type} className="text-gray-700">
                        {type}: {count}
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <div className="text-gray-500">暂无统计数据</div>
              )}
            </div>
          </div>

          {/* 测试结果 */}
          {Object.keys(testResults).length > 0 && (
            <div className="bg-yellow-50 p-4 rounded-lg mb-8">
              <h2 className="text-xl font-semibold text-yellow-900 mb-4">测试结果</h2>
              <div className="space-y-2">
                {Object.entries(testResults).map(([test, result]) => (
                  <div key={test} className="flex justify-between items-center">
                    <span className="font-medium">{test}:</span>
                    <span className="text-sm">{result}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 安全日志 */}
          <div className="bg-red-50 p-4 rounded-lg">
            <h2 className="text-xl font-semibold text-red-900 mb-4">
              安全日志 ({logs.length} 条记录)
            </h2>
            <div className="max-h-96 overflow-y-auto">
              {logs.length > 0 ? (
                <div className="space-y-2">
                  {logs.slice(-20).reverse().map((log, index) => (
                    <div key={index} className="bg-white p-3 rounded border text-sm">
                      <div className="flex justify-between items-start mb-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          log.type === 'rate_limit_exceeded' ? 'bg-orange-100 text-orange-800' :
                          log.type === 'suspicious_activity' ? 'bg-red-100 text-red-800' :
                          log.type === 'file_upload' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {log.type}
                        </span>
                        <span className="text-gray-500">
                          {new Date(log.timestamp).toLocaleString()}
                        </span>
                      </div>
                      <div className="text-gray-700">
                        <div>IP: {log.ip}</div>
                        <div>详情: {JSON.stringify(log.details, null, 2)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-gray-500 text-center py-8">
                  暂无安全日志记录
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
