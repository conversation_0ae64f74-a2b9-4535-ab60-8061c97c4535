// 🚀 上海荷阁科技 - Playwright E2E 测试配置
// 用于端到端测试

import { defineConfig, devices } from '@playwright/test'

/**
 * 从环境变量读取配置
 */
const baseURL = process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000'
const CI = !!process.env.CI

export default defineConfig({
  // ==========================================
  // 基础配置
  // ==========================================
  testDir: './tests',
  
  // 测试文件匹配模式
  testMatch: /.*\.e2e\.ts/,
  
  // 并行运行测试
  fullyParallel: true,
  
  // CI 环境中禁止重试
  forbidOnly: CI,
  
  // 失败时重试次数
  retries: CI ? 2 : 0,
  
  // 并发工作进程数
  workers: CI ? 1 : undefined,
  
  // ==========================================
  // 报告配置
  // ==========================================
  reporter: [
    ['html', { 
      outputFolder: '../../../coverage/playwright-report',
      open: 'never'
    }],
    ['json', { 
      outputFile: '../../../coverage/playwright-results.json' 
    }],
    ['junit', { 
      outputFile: '../../../coverage/playwright-junit.xml' 
    }],
    ['line'],
    ...(CI ? [['github']] : [])
  ],
  
  // ==========================================
  // 全局配置
  // ==========================================
  use: {
    // 基础 URL
    baseURL,
    
    // 浏览器上下文配置
    locale: 'zh-CN',
    timezoneId: 'Asia/Shanghai',
    
    // 截图配置
    screenshot: 'only-on-failure',
    
    // 视频录制
    video: 'retain-on-failure',
    
    // 追踪配置
    trace: 'retain-on-failure',
    
    // 操作超时
    actionTimeout: 30000,
    
    // 导航超时
    navigationTimeout: 30000,
    
    // 等待超时
    expect: {
      timeout: 10000,
    },
  },
  
  // ==========================================
  // 项目配置 - 多浏览器测试
  // ==========================================
  projects: [
    // 桌面浏览器
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 }
      },
    },
    {
      name: 'firefox',
      use: { 
        ...devices['Desktop Firefox'],
        viewport: { width: 1920, height: 1080 }
      },
    },
    {
      name: 'webkit',
      use: { 
        ...devices['Desktop Safari'],
        viewport: { width: 1920, height: 1080 }
      },
    },
    
    // 移动设备
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
    
    // 平板设备
    {
      name: 'Tablet',
      use: { 
        ...devices['iPad Pro'],
        viewport: { width: 1024, height: 768 }
      },
    },
    
    // Microsoft Edge
    {
      name: 'Microsoft Edge',
      use: { 
        ...devices['Desktop Edge'], 
        channel: 'msedge' 
      },
    },
    
    // Google Chrome
    {
      name: 'Google Chrome',
      use: { 
        ...devices['Desktop Chrome'], 
        channel: 'chrome' 
      },
    },
  ],
  
  // ==========================================
  // Web 服务器配置
  // ==========================================
  webServer: {
    command: 'npm run dev',
    url: baseURL,
    reuseExistingServer: !CI,
    timeout: 120000,
    env: {
      NODE_ENV: 'test',
    },
  },
  
  // ==========================================
  // 输出目录配置
  // ==========================================
  outputDir: '../../../coverage/playwright-test-results',
  
  // ==========================================
  // 全局设置和清理
  // ==========================================
  globalSetup: require.resolve('./global-setup'),
  globalTeardown: require.resolve('./global-teardown'),
  
  // ==========================================
  // 测试超时配置
  // ==========================================
  timeout: 60000,
  
  // ==========================================
  // 元数据
  // ==========================================
  metadata: {
    project: '上海荷阁科技企业官网',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'test',
    baseURL,
    timestamp: new Date().toISOString(),
  },
})
