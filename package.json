{"name": "enterprise-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "jest --config .cicd/testing/jest/jest.config.simple.js", "test:ci": "jest --config .cicd/testing/jest/jest.config.simple.js --ci --coverage --watchAll=false", "test:coverage": "jest --config .cicd/testing/jest/jest.config.simple.js --coverage", "test:watch": "jest --config .cicd/testing/jest/jest.config.simple.js --watch", "test:e2e": "playwright test --config .cicd/testing/e2e/playwright.config.ts", "test:e2e:ui": "playwright test --config .cicd/testing/e2e/playwright.config.ts --ui", "test:e2e:headed": "playwright test --config .cicd/testing/e2e/playwright.config.ts --headed", "test:performance": "lhci autorun --config .cicd/testing/performance/lighthouse.config.js", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@prisma/client": "^5.7.1", "@tailwindcss/typography": "^0.5.10", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "lucide-react": "^0.303.0", "next": "14.0.4", "next-auth": "^4.24.5", "postcss": "^8", "prisma": "^5.7.1", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.3.0", "typescript": "^5", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@playwright/test": "^1.40.1", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "eslint": "^8", "eslint-config-next": "14.0.4", "eslint-plugin-security": "^1.7.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-html-reporters": "^3.1.5", "jest-junit": "^16.0.0", "lighthouse": "^11.4.0", "@lhci/cli": "^0.12.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9"}}