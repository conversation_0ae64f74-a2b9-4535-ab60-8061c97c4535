# 🚀 Docker 网络连接问题解决方案

本文档提供了解决 Docker Hub 连接超时问题的完整解决方案，特别针对中国大陆网络环境优化。

## 🔍 问题描述

当运行 `docker-compose -f .cicd/docker/docker-compose.yml up -d` 时出现以下错误：

```
Error response from daemon: Get "https://registry-1.docker.io/v2/": context deadline exceeded
```

这是典型的 Docker Hub 连接超时问题，在中国大陆环境下很常见。

## 🎯 解决方案概览

我们提供了多层次的解决方案：

1. **镜像源切换** - 将所有镜像切换为国内镜像源
2. **Docker 配置优化** - 配置 Docker 守护进程使用国内镜像源
3. **自动化脚本** - 提供一键解决方案

## 📋 已修改的文件

### 1. Docker Compose 配置
- ✅ `.cicd/docker/docker-compose.yml` - 开发环境配置
- ✅ `.cicd/docker/docker-compose.prod.yml` - 生产环境配置

### 2. Dockerfile 配置
- ✅ `.cicd/docker/Dockerfile` - 主应用镜像
- ✅ `.cicd/docker/Dockerfile.nginx` - Nginx 镜像

### 3. Docker 守护进程配置
- ✅ `.cicd/docker/daemon.json` - 镜像源配置

### 4. 自动化脚本
- ✅ `.cicd/docker/scripts/pull-images.sh` - 镜像预拉取脚本
- ✅ `.cicd/docker/scripts/setup-docker-mirrors.sh` - 镜像源配置脚本
- ✅ `.cicd/docker/scripts/quick-start.sh` - 一键启动脚本

## 🚀 使用方法

### 方法一：一键启动（推荐）

```bash
# 一键解决网络问题并启动环境
./.cicd/docker/scripts/quick-start.sh
```

### 方法二：分步执行

```bash
# 1. 配置 Docker 镜像源
./.cicd/docker/scripts/setup-docker-mirrors.sh

# 2. 预拉取镜像
./.cicd/docker/scripts/pull-images.sh

# 3. 启动服务
docker-compose -f .cicd/docker/docker-compose.yml up -d
```

### 方法三：快速启动（跳过配置）

```bash
# 如果已经配置过镜像源，可以快速启动
./.cicd/docker/scripts/quick-start.sh -q
```

## 🔧 镜像源配置详情

### 使用的国内镜像源

1. **阿里云镜像源**（主要）
   - `registry.cn-hangzhou.aliyuncs.com/library/`

2. **备用镜像源**
   - 中科大镜像源：`https://docker.mirrors.ustc.edu.cn`
   - 网易镜像源：`https://hub-mirror.c.163.com`
   - 百度镜像源：`https://mirror.baidubce.com`
   - 腾讯云镜像源：`https://ccr.ccs.tencentyun.com`

### 镜像映射表

| 原始镜像 | 国内镜像源 |
|---------|-----------|
| `postgres:15-alpine` | `registry.cn-hangzhou.aliyuncs.com/library/postgres:15-alpine` |
| `redis:7-alpine` | `registry.cn-hangzhou.aliyuncs.com/library/redis:7-alpine` |
| `nginx:1.25-alpine` | `registry.cn-hangzhou.aliyuncs.com/library/nginx:1.25-alpine` |
| `node:18-alpine` | `registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine` |
| `prom/prometheus:latest` | `registry.cn-hangzhou.aliyuncs.com/library/prometheus:latest` |
| `grafana/grafana:latest` | `registry.cn-hangzhou.aliyuncs.com/library/grafana:latest` |

## 🛠️ 手动配置步骤

如果自动化脚本无法使用，可以手动执行以下步骤：

### 1. 应用 Docker 配置

```bash
# Linux
sudo cp .cicd/docker/daemon.json /etc/docker/daemon.json
sudo systemctl restart docker

# macOS
cp .cicd/docker/daemon.json ~/.docker/daemon.json
# 然后重启 Docker Desktop

# Windows
# 复制 daemon.json 到 C:\ProgramData\Docker\config\daemon.json
# 然后重启 Docker Desktop
```

### 2. 验证配置

```bash
# 检查 Docker 信息
docker info

# 测试镜像拉取
docker pull registry.cn-hangzhou.aliyuncs.com/library/hello-world:latest
```

## 🔍 故障排除

### 问题 1: 脚本权限不足

```bash
# 解决方案：添加执行权限
chmod +x .cicd/docker/scripts/*.sh
```

### 问题 2: Docker 服务未启动

```bash
# Linux
sudo systemctl start docker

# macOS/Windows
# 启动 Docker Desktop 应用
```

### 问题 3: 镜像拉取仍然失败

```bash
# 尝试手动拉取单个镜像
docker pull registry.cn-hangzhou.aliyuncs.com/library/postgres:15-alpine

# 如果失败，尝试其他镜像源
docker pull postgres:15-alpine
```

### 问题 4: 服务启动失败

```bash
# 查看详细日志
docker-compose -f .cicd/docker/docker-compose.yml logs

# 检查端口占用
netstat -tulpn | grep :3000
```

## 📊 服务访问信息

启动成功后，可以通过以下地址访问服务：

- **应用首页**: http://localhost:3000
- **健康检查**: http://localhost:3000/api/health
- **数据库**: localhost:5432 (用户: hege_user, 密码: hege_password)
- **Redis**: localhost:6379
- **Nginx**: http://localhost:80

## 🎯 常用命令

```bash
# 查看服务状态
docker-compose -f .cicd/docker/docker-compose.yml ps

# 查看服务日志
docker-compose -f .cicd/docker/docker-compose.yml logs -f

# 重启服务
docker-compose -f .cicd/docker/docker-compose.yml restart

# 停止服务
docker-compose -f .cicd/docker/docker-compose.yml down

# 完全清理（包括数据卷）
docker-compose -f .cicd/docker/docker-compose.yml down -v
```

## 📝 注意事项

1. **首次运行**：建议使用一键启动脚本，它会自动处理所有配置
2. **网络环境**：如果网络环境良好，可以跳过镜像源配置
3. **权限问题**：Linux 用户可能需要 sudo 权限来修改 Docker 配置
4. **端口冲突**：确保 3000、5432、6379、80 端口未被占用

## 🔗 相关文档

- [Docker 官方文档](https://docs.docker.com/)
- [Docker Compose 文档](https://docs.docker.com/compose/)
- [阿里云镜像加速器](https://help.aliyun.com/document_detail/60750.html)

---

**维护者**: 上海荷阁科技有限公司  
**更新时间**: 2025-07-07  
**版本**: v1.0.0
