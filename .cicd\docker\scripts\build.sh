#!/bin/bash
# 🚀 上海荷阁科技 - Docker 镜像构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
DOCKER_DIR="$PROJECT_ROOT/.cicd/docker"

# 默认值
REGISTRY="${DOCKER_REGISTRY:-registry.gitlab.com/hege-tech/hege-tech-web}"
IMAGE_NAME="${DOCKER_IMAGE:-hege-tech-web}"
TAG="${IMAGE_TAG:-latest}"
BUILD_ARGS=""
PUSH_IMAGE=false
NO_CACHE=false

# 帮助信息
show_help() {
    cat << EOF
🚀 上海荷阁科技 Docker 镜像构建脚本

用法: $0 [选项]

选项:
    -r, --registry REGISTRY    Docker 镜像仓库地址 (默认: $REGISTRY)
    -i, --image IMAGE          镜像名称 (默认: $IMAGE_NAME)
    -t, --tag TAG              镜像标签 (默认: $TAG)
    -p, --push                 构建后推送镜像到仓库
    -n, --no-cache             不使用缓存构建
    -a, --build-arg ARG        传递构建参数 (可多次使用)
    -h, --help                 显示此帮助信息

示例:
    $0 -t v1.0.0 -p                           # 构建并推送 v1.0.0 标签
    $0 -r my-registry.com -i my-app -t latest # 使用自定义仓库和镜像名
    $0 -n -a NODE_ENV=production              # 无缓存构建并传递构建参数

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -i|--image)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -p|--push)
            PUSH_IMAGE=true
            shift
            ;;
        -n|--no-cache)
            NO_CACHE=true
            shift
            ;;
        -a|--build-arg)
            BUILD_ARGS="$BUILD_ARGS --build-arg $2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 构建完整镜像名
FULL_IMAGE_NAME="$REGISTRY/$IMAGE_NAME:$TAG"

log_info "开始构建 Docker 镜像..."
log_info "项目根目录: $PROJECT_ROOT"
log_info "Docker 目录: $DOCKER_DIR"
log_info "镜像名称: $FULL_IMAGE_NAME"

# 检查必要文件
if [[ ! -f "$DOCKER_DIR/Dockerfile" ]]; then
    log_error "Dockerfile 不存在: $DOCKER_DIR/Dockerfile"
    exit 1
fi

if [[ ! -f "$PROJECT_ROOT/package.json" ]]; then
    log_error "package.json 不存在: $PROJECT_ROOT/package.json"
    exit 1
fi

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 构建 Docker 镜像
log_info "构建镜像: $FULL_IMAGE_NAME"

DOCKER_BUILD_CMD="docker build"

if [[ "$NO_CACHE" == "true" ]]; then
    DOCKER_BUILD_CMD="$DOCKER_BUILD_CMD --no-cache"
fi

DOCKER_BUILD_CMD="$DOCKER_BUILD_CMD $BUILD_ARGS -f $DOCKER_DIR/Dockerfile -t $FULL_IMAGE_NAME ."

log_info "执行命令: $DOCKER_BUILD_CMD"

if eval "$DOCKER_BUILD_CMD"; then
    log_success "镜像构建成功: $FULL_IMAGE_NAME"
else
    log_error "镜像构建失败"
    exit 1
fi

# 显示镜像信息
log_info "镜像信息:"
docker images "$REGISTRY/$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# 推送镜像
if [[ "$PUSH_IMAGE" == "true" ]]; then
    log_info "推送镜像到仓库: $FULL_IMAGE_NAME"
    
    if docker push "$FULL_IMAGE_NAME"; then
        log_success "镜像推送成功: $FULL_IMAGE_NAME"
    else
        log_error "镜像推送失败"
        exit 1
    fi
fi

# 清理悬空镜像
log_info "清理悬空镜像..."
docker image prune -f

log_success "🎉 Docker 镜像构建完成！"
log_info "镜像名称: $FULL_IMAGE_NAME"
log_info "构建时间: $(date)"

# 显示下一步操作建议
echo ""
log_info "下一步操作建议:"
echo "  1. 测试镜像: docker run --rm -p 3000:3000 $FULL_IMAGE_NAME"
echo "  2. 推送镜像: docker push $FULL_IMAGE_NAME"
echo "  3. 部署应用: docker-compose -f .cicd/docker/docker-compose.prod.yml up -d"
