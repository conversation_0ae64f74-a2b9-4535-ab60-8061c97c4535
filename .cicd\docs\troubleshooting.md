# 🔧 故障排除指南

本指南帮助您解决 CI/CD 流水线中可能遇到的常见问题。

## 📋 目录

- [GitLab CI/CD 问题](#gitlab-cicd-问题)
- [Docker 相关问题](#docker-相关问题)
- [构建失败问题](#构建失败问题)
- [测试失败问题](#测试失败问题)
- [部署失败问题](#部署失败问题)
- [Windows 环境问题](#windows-环境问题)
- [性能问题](#性能问题)
- [安全扫描问题](#安全扫描问题)

## 🔄 GitLab CI/CD 问题

### 问题：Runner 无法连接到 GitLab

**症状**：

- Runner 状态显示离线
- 作业一直处于 pending 状态

**解决方案**：

```bash
# 1. 检查 Runner 状态
gitlab-runner verify

# 2. 检查网络连接
curl -v http://home.spinach.cool:11991/

# 3. 重新注册 Runner
gitlab-runner unregister --name "runner-name"
gitlab-runner register --url "http://home.spinach.cool:11991/" --registration-token "new-token"

# 4. 重启 Runner 服务
gitlab-runner restart
```

### 问题：环境变量未正确设置

**症状**：

- 构建过程中提示缺少环境变量
- 应用配置错误

**解决方案**：

1. 检查 GitLab 项目设置中的 CI/CD 变量
2. 确保变量名称正确且未被屏蔽
3. 检查变量的作用域（protected/unprotected）

```yaml
# 在 .gitlab-ci.yml 中调试环境变量
debug:variables:
  stage: test
  script:
    - echo "NODE_ENV=$NODE_ENV"
    - echo "DOCKER_REGISTRY=$DOCKER_REGISTRY"
    - env | grep -E "(NODE_|DOCKER_|DATABASE_)"
```

### 问题：作业超时

**症状**：

- 作业运行时间过长被终止
- 超时错误信息

**解决方案**：

```yaml
# 增加作业超时时间
job_name:
  timeout: 2h # 设置为 2 小时
  script:
    - your-long-running-command
```

## 🐳 Docker 相关问题

### 问题：Docker 镜像构建失败

**症状**：

- Docker build 命令失败
- 镜像层构建错误

**解决方案**：

```bash
# 1. 清理 Docker 缓存
docker system prune -a

# 2. 检查 Dockerfile 语法
docker build --no-cache -f .cicd/docker/Dockerfile .

# 3. 逐层构建调试
docker build --target deps -f .cicd/docker/Dockerfile .
docker build --target builder -f .cicd/docker/Dockerfile .

# 4. 检查基础镜像
docker pull node:18-alpine
```

### 问题：容器启动失败

**症状**：

- 容器立即退出
- 健康检查失败

**解决方案**：

```bash
# 1. 查看容器日志
docker logs <container-id>

# 2. 进入容器调试
docker run -it --entrypoint /bin/sh hege-tech-web:latest

# 3. 检查端口映射
docker ps -a
netstat -tulpn | grep 3000

# 4. 检查资源限制
docker stats
```

### 问题：Docker Compose 服务依赖问题

**症状**：

- 服务启动顺序错误
- 数据库连接失败

**解决方案**：

```yaml
# 在 docker-compose.yml 中添加健康检查和依赖
services:
  app:
    depends_on:
      database:
        condition: service_healthy

  database:
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5
```

## 🏗️ 构建失败问题

### 问题：Node.js 版本不兼容

**症状**：

- npm install 失败
- 语法错误

**解决方案**：

```bash
# 1. 检查 Node.js 版本
node --version
npm --version

# 2. 使用正确的 Node.js 版本
nvm use 18
# 或在 Docker 中
FROM node:18-alpine

# 3. 清理 node_modules
rm -rf node_modules package-lock.json
npm install
```

### 问题：依赖安装失败

**症状**：

- npm install 报错
- 包版本冲突

**解决方案**：

```bash
# 1. 清理 npm 缓存
npm cache clean --force

# 2. 删除 lock 文件重新安装
rm package-lock.json
npm install

# 3. 使用 npm ci 进行干净安装
npm ci

# 4. 检查网络连接和镜像源
npm config get registry
npm config set registry https://registry.npmmirror.com
```

### 问题：TypeScript 编译错误

**症状**：

- 类型检查失败
- 编译错误

**解决方案**：

```bash
# 1. 检查 TypeScript 配置
npx tsc --noEmit

# 2. 更新类型定义
npm update @types/node @types/react

# 3. 检查 tsconfig.json 配置
cat tsconfig.json

# 4. 逐步修复类型错误
npx tsc --noEmit --incremental
```

## 🧪 测试失败问题

### 问题：Jest 测试失败

**症状**：

- 单元测试不通过
- 测试环境配置错误

**解决方案**：

```bash
# 1. 运行单个测试文件
npm test -- --testPathPattern=specific-test.test.ts

# 2. 清理测试缓存
npm test -- --clearCache

# 3. 调试模式运行
npm test -- --verbose --no-cache

# 4. 检查测试配置
cat .cicd/testing/jest/jest.config.js
```

### 问题：Playwright E2E 测试失败

**症状**：

- 浏览器启动失败
- 页面加载超时

**解决方案**：

```bash
# 1. 安装浏览器
npx playwright install

# 2. 检查应用是否运行
curl http://localhost:3000

# 3. 运行单个测试
npx playwright test --headed --project=chromium specific-test.e2e.ts

# 4. 查看测试报告
npx playwright show-report
```

### 问题：测试覆盖率不足

**症状**：

- 覆盖率低于阈值
- CI 流水线失败

**解决方案**：

```bash
# 1. 查看详细覆盖率报告
npm run test:coverage
open coverage/lcov-report/index.html

# 2. 调整覆盖率阈值
# 编辑 .cicd/testing/jest/jest.config.js
coverageThreshold: {
  global: {
    branches: 60,  # 降低阈值
    functions: 60,
    lines: 60,
    statements: 60
  }
}

# 3. 添加更多测试
# 为未覆盖的代码添加测试用例
```

## 🚀 部署失败问题

### 问题：SSH 连接失败

**症状**：

- 无法连接到目标服务器
- 权限被拒绝

**解决方案**：

```bash
# 1. 测试 SSH 连接
ssh -i ~/.ssh/id_rsa user@server

# 2. 检查 SSH 密钥格式
cat ~/.ssh/id_rsa | head -1
# 应该是 -----BEGIN OPENSSH PRIVATE KEY-----

# 3. 在 GitLab 中设置 SSH_PRIVATE_KEY 变量
# 确保包含完整的密钥内容，包括头尾

# 4. 检查服务器防火墙
telnet server-ip 22
```

### 问题：Docker 镜像拉取失败

**症状**：

- 无法从 Registry 拉取镜像
- 认证失败

**解决方案**：

```bash
# 1. 检查 Docker Registry 认证
docker login registry.gitlab.com

# 2. 检查镜像是否存在
docker pull registry.gitlab.com/hege-tech/hege-tech-web:latest

# 3. 在 GitLab CI 中使用正确的认证
before_script:
  - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
```

### 问题：服务健康检查失败

**症状**：

- 应用部署后无法访问
- 健康检查端点返回错误

**解决方案**：

```bash
# 1. 检查应用日志
docker logs app-container

# 2. 检查端口绑定
netstat -tulpn | grep 3000

# 3. 手动测试健康检查
curl -v http://localhost:3000/api/health

# 4. 检查环境变量
docker exec app-container env
```

## 🪟 Windows 环境问题

### 问题：PowerShell 执行策略限制

**症状**：

- 脚本无法执行
- 执行策略错误

**解决方案**：

```powershell
# 1. 检查当前执行策略
Get-ExecutionPolicy

# 2. 设置执行策略
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 3. 临时绕过执行策略
powershell -ExecutionPolicy Bypass -File script.ps1
```

### 问题：GitLab Runner Windows 服务问题

**症状**：

- Runner 服务无法启动
- 权限问题

**解决方案**：

```powershell
# 1. 检查服务状态
Get-Service gitlab-runner

# 2. 重新安装服务
gitlab-runner uninstall
gitlab-runner install --user "NT AUTHORITY\SYSTEM"

# 3. 启动服务
Start-Service gitlab-runner

# 4. 检查服务日志
Get-EventLog -LogName Application -Source gitlab-runner
```

### 问题：Docker Desktop Windows 问题

**症状**：

- Docker 命令无法识别
- 容器无法启动

**解决方案**：

```powershell
# 1. 检查 Docker Desktop 状态
docker version

# 2. 重启 Docker Desktop
Restart-Service docker

# 3. 检查 Hyper-V 功能
Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V

# 4. 启用必要功能
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All
```

## ⚡ 性能问题

### 问题：构建时间过长

**症状**：

- CI 流水线运行时间超过预期
- 构建步骤缓慢

**解决方案**：

```yaml
# 1. 启用缓存
cache:
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/
    - .next/cache/

# 2. 并行执行作业
test:unit:
  stage: test
  parallel: 3

# 3. 使用更快的镜像
image: node:18-alpine # 而不是 node:18
```

### 问题：内存不足

**症状**：

- 构建过程中内存溢出
- 容器被 OOM Killer 终止

**解决方案**：

```yaml
# 1. 增加 Node.js 内存限制
script:
  - export NODE_OPTIONS="--max-old-space-size=4096"
  - npm run build

# 2. 在 Docker 中设置内存限制
services:
  app:
    deploy:
      resources:
        limits:
          memory: 2G
```

## 🔒 安全扫描问题

### 问题：安全扫描误报

**症状**：

- 安全工具报告不相关的漏洞
- 阻止正常部署

**解决方案**：

```bash
# 1. 更新忽略文件
echo "CVE-2021-44228" >> .cicd/security/container/.trivyignore

# 2. 配置扫描规则
# 编辑 .cicd/security/sast/.eslintrc.security.js

# 3. 设置允许失败
security:sast:
  allow_failure: true
```

### 问题：依赖漏洞无法修复

**症状**：

- npm audit 报告漏洞
- 无可用的安全更新

**解决方案**：

```bash
# 1. 尝试自动修复
npm audit fix

# 2. 强制修复
npm audit fix --force

# 3. 手动更新依赖
npm update package-name

# 4. 临时忽略
echo "vulnerability-id" >> .cicd/security/dependency/.npmauditrc
```

## 📞 获取帮助

如果以上解决方案无法解决您的问题，请：

1. **查看日志**：收集详细的错误日志
2. **检查文档**：参考 [setup-guide.md](setup-guide.md)
3. **联系支持**：
   - 邮箱：<EMAIL>
   - 电话：18221165813

## 🔍 调试技巧

### 启用详细日志

```bash
# GitLab Runner 调试模式
gitlab-runner --debug run

# Docker 详细输出
docker build --progress=plain .

# npm 详细日志
npm install --loglevel verbose
```

### 常用调试命令

```bash
# 系统信息
uname -a
docker version
node --version
npm --version

# 网络连接
curl -v http://localhost:3000
netstat -tulpn

# 磁盘空间
df -h
du -sh node_modules/

# 进程信息
ps aux | grep node
ps aux | grep docker
```

---

© 2024 上海荷阁科技有限公司 - 故障排除指南
