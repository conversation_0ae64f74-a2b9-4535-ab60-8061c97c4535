"use client";

import { useState, useEffect } from "react";
import {
  ChevronLeft,
  ChevronRight,
  Calendar,
  Award,
  Users,
  Rocket,
} from "lucide-react";

interface TimelineItem {
  year: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  achievements?: string[];
  image?: string;
}

const timelineData: TimelineItem[] = [
  {
    year: "2020",
    title: "荷阁科技成立",
    description:
      "上海荷阁科技有限公司正式成立，由资深技术团队创立，专注于3D引擎技术研发和智能化解决方案",
    icon: <Rocket className="h-6 w-6" />,
    achievements: [
      "公司正式成立于上海青浦区",
      "确立3D引擎技术核心方向",
      "组建10人核心技术团队",
      "完成首轮技术架构设计",
    ],
  },
  {
    year: "2021",
    title: "云服务业务拓展",
    description:
      "成为腾讯云官方代理商，建立云服务业务线，开始为企业客户提供专业的云计算解决方案和技术支持",
    icon: <Award className="h-6 w-6" />,
    achievements: [
      "获得腾讯云官方代理资质",
      "建立专业云服务团队",
      "服务首批20家企业客户",
      "云服务收入占比达30%",
    ],
  },
  {
    year: "2022",
    title: "智能座舱技术突破",
    description:
      "3D HMI工具链技术取得重大突破，成功推出智能座舱解决方案，进军汽车行业，获得多家知名车企认可",
    icon: <Users className="h-6 w-6" />,
    achievements: [
      "发布3D HMI工具链产品",
      "与比亚迪、吉利等车企合作",
      "智能座舱项目成功落地",
      "获得汽车行业技术认证",
    ],
  },
  {
    year: "2023",
    title: "全面技术创新",
    description:
      "MCU AutoSar软件平台正式发布，虚拟数字人技术突破，泊车辅助解决方案成熟，服务客户数量快速增长",
    icon: <Calendar className="h-6 w-6" />,
    achievements: [
      "MCU AutoSar平台发布",
      "虚拟数字人技术商业化",
      "服务客户突破50家",
      "技术专利申请10余项",
    ],
  },
  {
    year: "2024",
    title: "市场成果显著",
    description:
      "助力捷途旅行者、领克900等重点汽车项目成功上市，技术实力获得市场验证，业务规模持续扩大",
    icon: <Rocket className="h-6 w-6" />,
    achievements: [
      "重点汽车项目成功上市",
      "年营收增长超过100%",
      "团队规模扩展至30人",
      "获得行业优秀供应商奖",
    ],
  },
  {
    year: "2025",
    title: "未来展望",
    description:
      "持续深耕技术创新，拓展AI、云计算、智能制造等更多应用场景，致力成为行业领先的技术服务商",
    icon: <Award className="h-6 w-6" />,
    achievements: [
      "规划AI技术深度应用",
      "拓展智能制造领域",
      "目标服务客户100家",
      "建设技术研发中心",
    ],
  },
];

export default function Timeline() {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isAutoPlay, setIsAutoPlay] = useState(true);

  useEffect(() => {
    if (!isAutoPlay) return;

    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev + 1) % timelineData.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoPlay]);

  const handlePrevious = () => {
    setIsAutoPlay(false);
    setActiveIndex(
      (prev) => (prev - 1 + timelineData.length) % timelineData.length,
    );
  };

  const handleNext = () => {
    setIsAutoPlay(false);
    setActiveIndex((prev) => (prev + 1) % timelineData.length);
  };

  const handleYearClick = (index: number) => {
    setIsAutoPlay(false);
    setActiveIndex(index);
  };

  return (
    <div className="relative bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-20 overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-10 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-1/2 w-72 h-72 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-2000"></div>
      </div>

      <div className="container-custom relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-6">
            发展历程
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            从初创到成长，我们始终坚持技术创新和客户服务，每一步都见证着荷阁科技的发展足迹
          </p>
        </div>

        {/* 年份导航 */}
        <div className="flex justify-center mb-16">
          <div className="relative max-w-5xl w-full">
            {/* 时间线背景 */}
            <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 transform -translate-y-1/2"></div>
            <div
              className="absolute top-1/2 left-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 transform -translate-y-1/2 transition-all duration-700 ease-out"
              style={{
                width: `${((activeIndex + 1) / timelineData.length) * 100}%`,
              }}
            ></div>

            {/* 年份节点 */}
            <div className="flex justify-between items-center relative z-10 px-2 md:px-4">
              {timelineData.map((item, index) => (
                <button
                  key={index}
                  onClick={() => handleYearClick(index)}
                  className={`
                    flex flex-col items-center group transition-all duration-500 transform
                    ${index === activeIndex ? "scale-110" : "hover:scale-105"}
                  `}
                >
                  <div
                    className={`
                    w-12 h-12 md:w-20 md:h-20 rounded-full flex items-center justify-center mb-2 md:mb-4 transition-all duration-500 relative
                    ${
                      index === activeIndex
                        ? "bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-2xl shadow-blue-500/40 ring-2 md:ring-4 ring-blue-200"
                        : "bg-white text-gray-600 border-2 border-gray-300 group-hover:border-blue-400 group-hover:shadow-lg"
                    }
                  `}
                  >
                    {index === activeIndex && (
                      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 animate-ping opacity-20"></div>
                    )}
                    <div className="relative z-10 scale-75 md:scale-100">
                      {item.icon}
                    </div>
                  </div>
                  <span
                    className={`
                    text-xs md:text-sm font-bold transition-all duration-300 px-1 md:px-2 py-1 rounded-full
                    ${
                      index === activeIndex
                        ? "text-blue-600 bg-blue-100"
                        : "text-gray-500 group-hover:text-blue-500 group-hover:bg-blue-50"
                    }
                  `}
                  >
                    {item.year}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 内容展示区域 */}
        <div className="relative max-w-6xl mx-auto px-4">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl md:rounded-3xl shadow-2xl border border-white/20 p-6 md:p-12 min-h-[400px] md:min-h-[500px] transition-all duration-700">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-center">
              <div className="space-y-4 md:space-y-6">
                <div className="flex items-center mb-6 md:mb-8">
                  <div className="bg-gradient-to-br from-blue-500 to-purple-600 p-3 md:p-4 rounded-xl md:rounded-2xl mr-4 md:mr-6 shadow-lg">
                    <div className="text-white scale-90 md:scale-100">
                      {timelineData[activeIndex].icon}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-2xl md:text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-800 bg-clip-text text-transparent">
                      {timelineData[activeIndex].year}
                    </h3>
                    <h4 className="text-lg md:text-2xl font-bold text-blue-600 mt-1 md:mt-2">
                      {timelineData[activeIndex].title}
                    </h4>
                  </div>
                </div>

                <p className="text-base md:text-lg text-gray-700 leading-relaxed font-medium">
                  {timelineData[activeIndex].description}
                </p>

                {timelineData[activeIndex].achievements && (
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 md:p-6 rounded-xl md:rounded-2xl border border-blue-100">
                    <h5 className="text-lg md:text-xl font-bold text-gray-900 mb-3 md:mb-4 flex items-center">
                      <Award className="h-4 w-4 md:h-5 md:w-5 text-blue-600 mr-2" />
                      主要成就
                    </h5>
                    <ul className="space-y-2 md:space-y-3">
                      {timelineData[activeIndex].achievements.map(
                        (achievement, idx) => (
                          <li
                            key={idx}
                            className="flex items-start text-sm md:text-base text-gray-700 font-medium"
                          >
                            <div className="w-2.5 h-2.5 md:w-3 md:h-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mr-3 md:mr-4 mt-1.5 flex-shrink-0"></div>
                            <span>{achievement}</span>
                          </li>
                        ),
                      )}
                    </ul>
                  </div>
                )}
              </div>

              <div className="relative">
                <div className="aspect-square bg-gradient-to-br from-blue-100 via-purple-50 to-indigo-100 rounded-3xl flex items-center justify-center relative overflow-hidden shadow-inner">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-purple-400/10"></div>
                  <div className="text-8xl font-black text-blue-600/15 relative z-10">
                    {timelineData[activeIndex].year}
                  </div>
                  <div className="absolute top-4 right-4 w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full opacity-20 animate-pulse"></div>
                  <div className="absolute bottom-4 left-4 w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full opacity-20 animate-pulse delay-1000"></div>
                </div>
              </div>
            </div>
          </div>

          {/* 导航按钮 */}
          <button
            onClick={handlePrevious}
            className="absolute -left-6 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm rounded-full p-4 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-110 border border-gray-200 group"
          >
            <ChevronLeft className="h-6 w-6 text-gray-600 group-hover:text-blue-600 transition-colors duration-300" />
          </button>

          <button
            onClick={handleNext}
            className="absolute -right-6 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm rounded-full p-4 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-110 border border-gray-200 group"
          >
            <ChevronRight className="h-6 w-6 text-gray-600 group-hover:text-blue-600 transition-colors duration-300" />
          </button>
        </div>

        {/* 自动播放控制和进度指示器 */}
        <div className="text-center mt-12 space-y-6">
          <div className="flex justify-center space-x-2">
            {timelineData.map((_, index) => (
              <button
                key={index}
                onClick={() => handleYearClick(index)}
                className={`
                  w-3 h-3 rounded-full transition-all duration-300
                  ${
                    index === activeIndex
                      ? "bg-blue-600 scale-125"
                      : "bg-gray-300 hover:bg-blue-400"
                  }
                `}
              />
            ))}
          </div>

          <button
            onClick={() => setIsAutoPlay(!isAutoPlay)}
            className={`
              px-8 py-3 rounded-full text-sm font-bold transition-all duration-300 transform hover:scale-105 shadow-lg
              ${
                isAutoPlay
                  ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-blue-500/25"
                  : "bg-white text-gray-700 hover:bg-gray-50 border-2 border-gray-300 hover:border-blue-400"
              }
            `}
          >
            {isAutoPlay ? "⏸️ 暂停自动播放" : "▶️ 开始自动播放"}
          </button>
        </div>
      </div>
    </div>
  );
}
