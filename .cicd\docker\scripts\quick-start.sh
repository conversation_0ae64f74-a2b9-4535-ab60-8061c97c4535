#!/bin/bash
# 🚀 上海荷阁科技 - Docker 环境快速启动脚本
# 一键解决网络问题并启动开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# 检查 Docker 状态
check_docker() {
    log_info "检查 Docker 状态..."
    
    if ! command -v docker >/dev/null 2>&1; then
        log_error "❌ Docker 未安装"
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        log_error "❌ Docker 未运行，请启动 Docker 服务"
        exit 1
    fi
    
    log_success "✅ Docker 服务正常运行"
}

# 应用镜像源配置
setup_mirrors() {
    log_info "配置 Docker 镜像源..."
    
    if [[ -f "$SCRIPT_DIR/setup-docker-mirrors.sh" ]]; then
        bash "$SCRIPT_DIR/setup-docker-mirrors.sh"
    else
        log_warning "⚠️  镜像源配置脚本不存在，跳过配置"
    fi
}

# 预拉取镜像
pull_images() {
    log_info "预拉取 Docker 镜像..."
    
    if [[ -f "$SCRIPT_DIR/pull-images.sh" ]]; then
        bash "$SCRIPT_DIR/pull-images.sh"
    else
        log_warning "⚠️  镜像拉取脚本不存在，跳过预拉取"
    fi
}

# 启动服务
start_services() {
    log_info "启动 Docker Compose 服务..."
    
    cd "$PROJECT_ROOT"
    
    local compose_file=".cicd/docker/docker-compose.yml"
    
    if [[ ! -f "$compose_file" ]]; then
        log_error "❌ Docker Compose 文件不存在: $compose_file"
        exit 1
    fi
    
    # 停止现有服务
    log_info "停止现有服务..."
    docker-compose -f "$compose_file" down --remove-orphans || true
    
    # 启动服务
    log_info "启动新服务..."
    if docker-compose -f "$compose_file" up -d; then
        log_success "✅ 服务启动成功"
    else
        log_error "❌ 服务启动失败"
        log_info "查看服务日志:"
        docker-compose -f "$compose_file" logs
        exit 1
    fi
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    cd "$PROJECT_ROOT"
    local compose_file=".cicd/docker/docker-compose.yml"
    
    # 等待服务启动
    sleep 10
    
    # 显示服务状态
    echo ""
    log_info "服务状态:"
    docker-compose -f "$compose_file" ps
    
    # 检查健康状态
    echo ""
    log_info "健康检查:"
    
    # 检查应用健康状态
    if curl -f http://localhost:3000/api/health >/dev/null 2>&1; then
        log_success "✅ Next.js 应用运行正常"
    else
        log_warning "⚠️  Next.js 应用可能还在启动中"
    fi
    
    # 检查数据库连接
    if docker exec hege-tech-db-dev pg_isready -U hege_user >/dev/null 2>&1; then
        log_success "✅ PostgreSQL 数据库运行正常"
    else
        log_warning "⚠️  PostgreSQL 数据库连接异常"
    fi
    
    # 检查 Redis 连接
    if docker exec hege-tech-redis-dev redis-cli ping >/dev/null 2>&1; then
        log_success "✅ Redis 缓存运行正常"
    else
        log_warning "⚠️  Redis 缓存连接异常"
    fi
}

# 显示访问信息
show_access_info() {
    echo ""
    log_success "🎉 开发环境启动完成！"
    echo ""
    log_info "访问地址:"
    echo "  📱 应用首页:    http://localhost:3000"
    echo "  🔍 健康检查:    http://localhost:3000/api/health"
    echo "  🗄️  数据库:      localhost:5432 (用户: hege_user)"
    echo "  🚀 Redis:       localhost:6379"
    echo "  🌐 Nginx:       http://localhost:80"
    echo ""
    log_info "常用命令:"
    echo "  查看日志:       docker-compose -f .cicd/docker/docker-compose.yml logs -f"
    echo "  停止服务:       docker-compose -f .cicd/docker/docker-compose.yml down"
    echo "  重启服务:       docker-compose -f .cicd/docker/docker-compose.yml restart"
    echo ""
}

# 主函数
main() {
    echo ""
    log_info "🚀 上海荷阁科技 - Docker 环境快速启动"
    echo ""
    
    # 检查 Docker
    check_docker
    
    # 配置镜像源（可选）
    if [[ "${SETUP_MIRRORS:-true}" == "true" ]]; then
        setup_mirrors
    fi
    
    # 预拉取镜像（可选）
    if [[ "${PULL_IMAGES:-true}" == "true" ]]; then
        pull_images
    fi
    
    # 启动服务
    start_services
    
    # 检查服务状态
    check_services
    
    # 显示访问信息
    show_access_info
}

# 帮助信息
show_help() {
    cat << EOF
🚀 上海荷阁科技 Docker 环境快速启动脚本

用法: $0 [选项]

选项:
    -h, --help           显示此帮助信息
    -s, --skip-mirrors   跳过镜像源配置
    -p, --skip-pull      跳过镜像预拉取
    -q, --quick          快速模式（跳过镜像源配置和预拉取）

环境变量:
    SETUP_MIRRORS=false  跳过镜像源配置
    PULL_IMAGES=false    跳过镜像预拉取

示例:
    $0                   # 完整启动流程
    $0 -q                # 快速启动（跳过配置）
    $0 -s                # 跳过镜像源配置
    $0 -p                # 跳过镜像预拉取

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -s|--skip-mirrors)
            export SETUP_MIRRORS=false
            shift
            ;;
        -p|--skip-pull)
            export PULL_IMAGES=false
            shift
            ;;
        -q|--quick)
            export SETUP_MIRRORS=false
            export PULL_IMAGES=false
            shift
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 错误处理
trap 'log_error "❌ 启动过程中发生错误"; exit 1' ERR

# 执行主流程
main
