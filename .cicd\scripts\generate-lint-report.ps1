# Shanghai Hege Technology - Lint Results Report Generator
# Generates comprehensive lint and format check reports in multiple formats

$ErrorActionPreference = "Continue"

Write-Host "Generating lint and format check reports..." -ForegroundColor Green

try {
    # Read output files
    $lintOutput = ""
    $formatOutput = ""
    
    if (Test-Path "lint-output.txt") {
        $lintOutput = Get-Content -Path "lint-output.txt" -Raw
    }
    
    if (Test-Path "format-check-output.txt") {
        $formatOutput = Get-Content -Path "format-check-output.txt" -Raw
    }
    
    # Analyze lint results
    $lintErrors = 0
    $lintWarnings = 0
    $lintPassed = $true
    
    if ($lintOutput) {
        $lintErrors = ([regex]::Matches($lintOutput, "error", [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)).Count
        $lintWarnings = ([regex]::Matches($lintOutput, "warning", [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)).Count
        $lintPassed = $lintErrors -eq 0
    }
    
    # Analyze format results
    $formatIssues = 0
    $formatPassed = $true

    if ($formatOutput) {
        # Check for format warnings or errors
        $warnMatches = ([regex]::Matches($formatOutput, "\[warn\]", [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)).Count
        $errorMatches = ([regex]::Matches($formatOutput, "\[error\]", [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)).Count

        if ($warnMatches -gt 0 -or $errorMatches -gt 0) {
            $formatIssues = $warnMatches + $errorMatches
            $formatPassed = $false
        }

        # Also check for the "Code style issues found" message
        if ($formatOutput -match "Code style issues found in (\d+) files") {
            $formatIssues = [int]$matches[1]
            $formatPassed = $false
        }

        # Check for "Error occurred when checking code style"
        if ($formatOutput -match "Error occurred when checking code style") {
            $formatPassed = $false
            if ($formatIssues -eq 0) {
                $formatIssues = 1  # At least one error
            }
        }
    }
    
    # Generate JSON report
    $jsonReport = @{
        timestamp = Get-Date -Format 'yyyy-MM-ddTHH:mm:ss'
        summary = @{
            lint = @{
                status = if ($lintPassed) { "passed" } else { "failed" }
                errors = $lintErrors
                warnings = $lintWarnings
            }
            format = @{
                status = if ($formatPassed) { "passed" } else { "failed" }
                issues = $formatIssues
            }
            overall = @{
                status = if ($lintPassed -and $formatPassed) { "passed" } else { "failed" }
            }
        }
        details = @{
            lint_output = $lintOutput
            format_output = $formatOutput
        }
    }
    
    $jsonReport | ConvertTo-Json -Depth 4 | Out-File -FilePath "lint-results.json" -Encoding UTF8
    Write-Host "JSON report generated: lint-results.json" -ForegroundColor Green
    
    # Generate JUnit XML report
    $timestamp = Get-Date -Format 'yyyy-MM-ddTHH:mm:ss'
    $testSuites = 2
    $totalTests = 2
    $failures = 0
    $errors = 0
    
    if (-not $lintPassed) { $failures++ }
    if (-not $formatPassed) { $failures++ }
    
    $junitXml = @"
<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Code Quality Checks" tests="$totalTests" failures="$failures" errors="$errors" time="0" timestamp="$timestamp">
  <testsuite name="ESLint" tests="1" failures="$(if ($lintPassed) { 0 } else { 1 })" errors="0" time="0">
    <testcase name="ESLint Code Analysis" classname="lint">
$(if (-not $lintPassed) {
"      <failure message=""ESLint found $lintErrors errors and $lintWarnings warnings"" type=""LintError"">
        <![CDATA[$($lintOutput -replace '&', '&amp;' -replace '<', '&lt;' -replace '>', '&gt;')]]>
      </failure>"
} else {
"      <!-- ESLint passed with $lintWarnings warnings -->"
})
    </testcase>
  </testsuite>
  <testsuite name="Prettier" tests="1" failures="$(if ($formatPassed) { 0 } else { 1 })" errors="0" time="0">
    <testcase name="Code Format Check" classname="format">
$(if (-not $formatPassed) {
"      <failure message=""Prettier found formatting issues in $formatIssues files"" type=""FormatError"">
        <![CDATA[$($formatOutput -replace '&', '&amp;' -replace '<', '&lt;' -replace '>', '&gt;')]]>
      </failure>"
} else {
"      <!-- Code formatting is correct -->"
})
    </testcase>
  </testsuite>
</testsuites>
"@
    
    $junitXml | Out-File -FilePath "lint-results.xml" -Encoding UTF8
    Write-Host "JUnit XML report generated: lint-results.xml" -ForegroundColor Green
    
    # Generate summary report
    $summaryReport = @"
# Code Quality Check Report

**Generated:** $timestamp

## Summary
- **ESLint Status:** $(if ($lintPassed) { "✅ PASSED" } else { "❌ FAILED" })
- **Format Status:** $(if ($formatPassed) { "✅ PASSED" } else { "❌ FAILED" })
- **Overall Status:** $(if ($lintPassed -and $formatPassed) { "✅ PASSED" } else { "❌ FAILED" })

## Details
### ESLint Results
- Errors: $lintErrors
- Warnings: $lintWarnings

### Format Check Results
- Files with formatting issues: $formatIssues

$(if (-not $formatPassed) {
"### How to Fix Format Issues
Run the following command to automatically fix formatting issues:
``````
npm run format
``````"
})

$(if (-not $lintPassed) {
"### How to Fix Lint Issues
Review the lint output above and fix the reported issues, or run:
``````
npm run lint:fix
``````"
})
"@
    
    $summaryReport | Out-File -FilePath "lint-summary.md" -Encoding UTF8
    Write-Host "Summary report generated: lint-summary.md" -ForegroundColor Green
    
    # Display summary in console
    Write-Host "`n=== CODE QUALITY CHECK SUMMARY ===" -ForegroundColor Cyan
    Write-Host "ESLint: $(if ($lintPassed) { "PASSED" } else { "FAILED" }) ($lintErrors errors, $lintWarnings warnings)" -ForegroundColor $(if ($lintPassed) { "Green" } else { "Red" })
    Write-Host "Format: $(if ($formatPassed) { "PASSED" } else { "FAILED" }) ($formatIssues files need formatting)" -ForegroundColor $(if ($formatPassed) { "Green" } else { "Red" })
    Write-Host "Overall: $(if ($lintPassed -and $formatPassed) { "PASSED" } else { "FAILED" })" -ForegroundColor $(if ($lintPassed -and $formatPassed) { "Green" } else { "Red" })
    Write-Host "=================================" -ForegroundColor Cyan
    
    Write-Host "All reports generated successfully" -ForegroundColor Green
    
    # Exit with appropriate code
    if ($lintPassed -and $formatPassed) {
        exit 0
    } else {
        exit 1
    }
}
catch {
    Write-Host "Failed to generate lint reports: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
