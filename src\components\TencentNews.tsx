'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Calendar, ExternalLink, Tag, RefreshCw } from 'lucide-react';

interface NewsItem {
  title: string;
  url: string;
  date: string;
  summary: string;
  category: string;
}

interface NewsResponse {
  success: boolean;
  data: NewsItem[];
  message: string;
  timestamp: string;
}

export default function TencentNews() {
  const [news, setNews] = useState<NewsItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string>('');

  const fetchNews = async () => {
    try {
      setLoading(true);
      setError(null);

      // 静态导出时使用模拟数据
      if (typeof window !== 'undefined' && window.location.protocol === 'file:') {
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 1000));

        setNews([
          {
            title: '腾讯云发布最新AI解决方案',
            url: 'https://cloud.tencent.com/product/ai',
            date: new Date().toLocaleDateString('zh-CN'),
            summary: '腾讯云推出全新的人工智能解决方案，助力企业数字化转型，提供更智能的云服务体验。',
            category: '人工智能'
          },
          {
            title: '云原生技术助力企业降本增效',
            url: 'https://cloud.tencent.com/product/tke',
            date: new Date().toLocaleDateString('zh-CN'),
            summary: '通过云原生技术，企业可以实现更高效的资源利用和成本控制，提升业务敏捷性。',
            category: '云原生'
          },
          {
            title: '腾讯云数据库服务全面升级',
            url: 'https://cloud.tencent.com/product/cdb',
            date: new Date().toLocaleDateString('zh-CN'),
            summary: '新一代数据库服务提供更高性能、更强安全性，满足企业级应用需求。',
            category: '数据库'
          },
          {
            title: '边缘计算解决方案正式发布',
            url: 'https://cloud.tencent.com/product/ecm',
            date: new Date().toLocaleDateString('zh-CN'),
            summary: '腾讯云边缘计算为物联网、游戏、视频等场景提供低延迟、高可用的服务。',
            category: '边缘计算'
          }
        ]);
        setLastUpdate(new Date().toLocaleString('zh-CN'));
        return;
      }

      const response = await fetch('/api/tencent-news', {
        cache: 'no-store' // 确保获取最新数据
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: NewsResponse = await response.json();

      if (result.success) {
        setNews(result.data);
        setLastUpdate(new Date(result.timestamp).toLocaleString('zh-CN'));
      } else {
        throw new Error(result.message || '获取资讯失败');
      }
    } catch (err) {
      console.error('获取腾讯云资讯失败:', err);
      setError(err instanceof Error ? err.message : '获取资讯失败');

      // 设置默认数据作为fallback
      setNews([
        {
          title: '腾讯云服务持续创新发展',
          url: 'https://cloud.tencent.com/',
          date: new Date().toLocaleDateString('zh-CN'),
          summary: '腾讯云持续推出创新服务，为企业数字化转型提供强力支撑。',
          category: '云计算'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNews();
    
    // 设置定时刷新，每小时更新一次
    const interval = setInterval(fetchNews, 60 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  const handleRefresh = () => {
    fetchNews();
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-gray-900">腾讯云最新资讯</h3>
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5 text-blue-600" />
          </div>
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-2xl font-bold text-gray-900 mb-2">腾讯云最新资讯</h3>
          {lastUpdate && (
            <p className="text-sm text-gray-500 flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              最后更新: {lastUpdate}
            </p>
          )}
        </div>
        <button
          onClick={handleRefresh}
          disabled={loading}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          刷新
        </button>
      </div>

      {error && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <p className="text-yellow-800 text-sm">
            ⚠️ {error} - 显示备用资讯数据
          </p>
        </div>
      )}

      <div className="space-y-6">
        {news.map((item, index) => (
          <div key={index} className="border-b border-gray-200 pb-6 last:border-b-0 last:pb-0">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <Link 
                  href={item.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="group"
                >
                  <h4 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-2 flex items-center">
                    {item.title}
                    <ExternalLink className="h-4 w-4 ml-2 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </h4>
                </Link>
                <p className="text-gray-600 text-sm leading-relaxed mb-3">
                  {item.summary}
                </p>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <span className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    {item.date}
                  </span>
                  <span className="flex items-center">
                    <Tag className="h-4 w-4 mr-1" />
                    {item.category}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 text-center">
        <Link 
          href="https://cloud.tencent.com/developer/news" 
          target="_blank" 
          rel="noopener noreferrer"
          className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
        >
          查看更多腾讯云资讯
          <ExternalLink className="h-4 w-4 ml-2" />
        </Link>
      </div>
    </div>
  );
}
