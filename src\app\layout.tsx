import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "@/styles/globals.css";
import ChatBot from "@/components/ChatBot";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "上海荷阁科技 - 专业3D HMI解决方案提供商",
  description:
    "上海荷阁科技有限公司专注于3D HMI工具链、智能座舱、3D建模、自动泊车和高精地图渲染技术，为汽车行业提供专业技术解决方案。",
  keywords: "3D HMI,智能座舱,3D建模,自动泊车,高精地图渲染,VPA全3D方案,一镜到底",
  authors: [{ name: "上海荷阁科技有限公司" }],
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        {children}
        <ChatBot />
      </body>
    </html>
  );
}
