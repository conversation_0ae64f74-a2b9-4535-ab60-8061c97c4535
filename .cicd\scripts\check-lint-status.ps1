# Shanghai Hege Technology - Lint Status Checker
# Checks the final status of lint and format checks

$ErrorActionPreference = "Continue"

Write-Host "Checking lint and format check final status..." -ForegroundColor Green

try {
    if (Test-Path 'lint-results.json') {
        $report = Get-Content 'lint-results.json' | ConvertFrom-Json
        $overallStatus = $report.summary.overall.status
        
        Write-Host "Overall status: $overallStatus" -ForegroundColor $(if ($overallStatus -eq 'passed') { 'Green' } else { 'Red' })
        
        if ($overallStatus -eq 'failed') {
            Write-Host 'Code quality checks failed. Please review the reports.' -ForegroundColor Red
            Write-Host 'Reports generated:' -ForegroundColor Yellow
            Write-Host '  - lint-results.json (detailed JSON report)' -ForegroundColor Yellow
            Write-Host '  - lint-results.xml (JUnit format for GitLab)' -ForegroundColor Yellow
            Write-Host '  - lint-summary.md (human-readable summary)' -ForegroundColor Yellow
            exit 1
        } else {
            Write-Host 'All code quality checks passed!' -ForegroundColor Green
            exit 0
        }
    } else {
        Write-Host 'lint-results.json not found. Assuming failure.' -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "Failed to check lint status: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
