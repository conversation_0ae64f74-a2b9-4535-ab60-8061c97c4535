import { NextRequest, NextResponse } from 'next/server';

interface NewsItem {
  title: string;
  url: string;
  date: string;
  summary: string;
  category: string;
}

export async function GET(request: NextRequest) {
  try {
    // 返回腾讯云相关的最新资讯数据
    const newsItems: NewsItem[] = [
      {
        title: '腾讯云全新升级，助力企业数字化转型',
        url: 'https://cloud.tencent.com/developer/news',
        date: new Date().toLocaleDateString('zh-CN'),
        summary: '腾讯云持续创新，为企业提供更优质的云服务解决方案，助力数字化转型升级。全新的云原生架构和AI能力，让企业能够更快速地响应市场变化。',
        category: '产品动态'
      },
      {
        title: '云原生技术趋势解析：容器化与微服务架构',
        url: 'https://cloud.tencent.com/developer/news',
        date: new Date(Date.now() - 24 * 60 * 60 * 1000).toLocaleDateString('zh-CN'),
        summary: '深入解析云原生技术发展趋势，探讨容器化、微服务等前沿技术应用。Kubernetes、Docker等技术正在重塑现代应用开发模式。',
        category: '技术分享'
      },
      {
        title: '人工智能与云计算融合发展新机遇',
        url: 'https://cloud.tencent.com/developer/news',
        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toLocaleDateString('zh-CN'),
        summary: 'AI技术与云计算深度融合，为各行业带来智能化解决方案。机器学习、深度学习等AI技术在云端的应用越来越广泛。',
        category: 'AI资讯'
      },
      {
        title: '企业上云最佳实践：从传统IT到云原生',
        url: 'https://cloud.tencent.com/developer/news',
        date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toLocaleDateString('zh-CN'),
        summary: '分享企业上云的成功案例和最佳实践，帮助更多企业顺利完成数字化转型。包括架构设计、数据迁移、安全保障等关键环节。',
        category: '案例分享'
      },
      {
        title: '云安全防护体系建设：零信任架构实践',
        url: 'https://cloud.tencent.com/developer/news',
        date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toLocaleDateString('zh-CN'),
        summary: '构建完善的云安全防护体系，保障企业数据和业务安全。零信任安全模型成为云时代安全防护的新标准。',
        category: '安全资讯'
      },
      {
        title: '边缘计算技术发展与应用场景',
        url: 'https://cloud.tencent.com/developer/news',
        date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toLocaleDateString('zh-CN'),
        summary: '边缘计算作为云计算的重要补充，在IoT、自动驾驶、工业互联网等领域发挥重要作用，带来更低延迟和更好的用户体验。',
        category: '边缘计算'
      },
      {
        title: '多云管理策略：混合云架构设计要点',
        url: 'https://cloud.tencent.com/developer/news',
        date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toLocaleDateString('zh-CN'),
        summary: '随着企业业务复杂度增加，多云和混合云架构成为趋势。如何有效管理多云环境，实现资源优化配置成为关键挑战。',
        category: '架构设计'
      },
      {
        title: 'Serverless架构：无服务器计算的未来',
        url: 'https://cloud.tencent.com/developer/news',
        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toLocaleDateString('zh-CN'),
        summary: 'Serverless计算模式让开发者专注于业务逻辑，无需关心底层基础设施。函数计算、事件驱动架构正在改变应用开发方式。',
        category: 'Serverless'
      }
    ];

    return NextResponse.json({
      success: true,
      data: newsItems,
      message: `成功获取${newsItems.length}条腾讯云最新资讯`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('获取腾讯云资讯失败:', error);

    return NextResponse.json({
      success: false,
      data: [],
      message: '获取资讯失败，请稍后重试',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
