# Shanghai Hege Technology - Local Deployment Verification Script
# Verify if local Docker container deployment is successful

param(
    [string]$ContainerName = "hege-tech-web-local",
    [string]$HostPort = "3000",
    [int]$MaxRetries = 30,
    [int]$RetryInterval = 2
)

$ErrorActionPreference = "Continue"

Write-Host "Starting local deployment verification..." -ForegroundColor Green

# Check if container is running
Write-Host "Checking container status..." -ForegroundColor Yellow
$containerStatus = docker ps --filter "name=$ContainerName" --format "{{.Status}}"

if (-not $containerStatus) {
    Write-Host "Container $ContainerName is not running" -ForegroundColor Red
    exit 1
}

Write-Host "Container status: $containerStatus" -ForegroundColor Green

# Check port mapping
Write-Host "Checking port mapping..." -ForegroundColor Yellow
$portMapping = docker port $ContainerName 2>$null
if ($portMapping) {
    Write-Host "Port mapping: $portMapping" -ForegroundColor Green
} else {
    Write-Host "Unable to get port mapping information" -ForegroundColor Yellow
}

# Health check - try to access application
Write-Host "Performing health check..." -ForegroundColor Yellow
$healthCheckUrl = "http://localhost:$HostPort"

for ($i = 1; $i -le $MaxRetries; $i++) {
    try {
        Write-Host "Attempt $i/$MaxRetries : accessing $healthCheckUrl" -ForegroundColor Cyan

        $response = Invoke-WebRequest -Uri $healthCheckUrl -TimeoutSec 5 -UseBasicParsing

        if ($response.StatusCode -eq 200) {
            Write-Host "Health check successful! HTTP status code: $($response.StatusCode)" -ForegroundColor Green
            Write-Host "Response content length: $($response.Content.Length) characters" -ForegroundColor Green

            # Check if response content contains expected content
            if ($response.Content -match "Next.js|React|Hege") {
                Write-Host "Response content validation successful" -ForegroundColor Green
            } else {
                Write-Host "Response content may be incomplete" -ForegroundColor Yellow
            }

            break
        }
    }
    catch {
        Write-Host "Attempt $i failed: $($_.Exception.Message)" -ForegroundColor Red

        if ($i -eq $MaxRetries) {
            Write-Host "Health check failed, maximum retry attempts reached" -ForegroundColor Red

            # Show container logs for debugging
            Write-Host "Container logs (last 50 lines):" -ForegroundColor Yellow
            docker logs --tail 50 $ContainerName

            exit 1
        }

        Write-Host "Waiting $RetryInterval seconds before retry..." -ForegroundColor Yellow
        Start-Sleep -Seconds $RetryInterval
    }
}

# Display final status
Write-Host "`nDeployment verification completed!" -ForegroundColor Green
Write-Host "Application access URL: $healthCheckUrl" -ForegroundColor Cyan
Write-Host "Container information:" -ForegroundColor Yellow
docker ps --filter "name=$ContainerName" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.Image}}"

# Display resource usage
Write-Host "`nResource usage:" -ForegroundColor Yellow
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" $ContainerName

Write-Host "`nLocal deployment verification successful!" -ForegroundColor Green
