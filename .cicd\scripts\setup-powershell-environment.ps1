# Shanghai Hege Technology - PowerShell Environment Setup Script
# Configure PowerShell environment for GitLab CI/CD to avoid encoding issues

$ErrorActionPreference = "Stop"

Write-Host "Setting up PowerShell environment for GitLab CI/CD..." -ForegroundColor Green

try {
    # Set console output encoding to UTF-8
    Write-Host "Configuring console encoding..." -ForegroundColor Yellow
    [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
    [Console]::InputEncoding = [System.Text.Encoding]::UTF8
    
    # Set PowerShell execution policy
    Write-Host "Setting PowerShell execution policy..." -ForegroundColor Yellow
    Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force
    
    # Configure environment variables for better compatibility
    Write-Host "Setting environment variables..." -ForegroundColor Yellow
    $env:POWERSHELL_TELEMETRY_OPTOUT = "1"
    $env:DOTNET_CLI_TELEMETRY_OPTOUT = "1"
    
    # Display current environment information
    Write-Host "PowerShell environment information:" -ForegroundColor Cyan
    Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor White
    Write-Host "Execution Policy: $(Get-ExecutionPolicy)" -ForegroundColor White
    Write-Host "Console Output Encoding: $([Console]::OutputEncoding.EncodingName)" -ForegroundColor White
    Write-Host "Console Input Encoding: $([Console]::InputEncoding.EncodingName)" -ForegroundColor White
    Write-Host "Current Culture: $((Get-Culture).Name)" -ForegroundColor White
    Write-Host "Current UI Culture: $((Get-UICulture).Name)" -ForegroundColor White
    
    Write-Host "PowerShell environment setup completed successfully" -ForegroundColor Green
}
catch {
    Write-Host "PowerShell environment setup failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
