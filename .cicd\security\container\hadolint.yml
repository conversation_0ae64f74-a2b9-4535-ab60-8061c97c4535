# 🚀 上海荷阁科技 - Hadolint Dockerfile 检查配置
# 用于 Dockerfile 最佳实践和安全检查

# ==========================================
# 基础配置
# ==========================================
format: json
output: hadolint-report.json
no-color: true
no-fail: false

# ==========================================
# 规则配置
# ==========================================
rules:
  # 启用的规则
  enable:
    - DL3000 # 使用绝对 WORKDIR
    - DL3001 # 避免使用 sudo
    - DL3002 # 最后一个 USER 不应该是 root
    - DL3003 # 使用 WORKDIR 切换目录
    - DL3004 # 不要使用 sudo
    - DL3005 # 不要使用 apt-get upgrade
    - DL3006 # 总是标记镜像版本
    - DL3007 # 使用 --no-cache 开关
    - DL3008 # 固定 apt 包版本
    - DL3009 # 删除 apt 缓存
    - DL3010 # 使用 ADD 复制远程文件
    - DL3011 # 有效的 EXPOSE 端口
    - DL3012 # 提供 HEALTHCHECK
    - DL3013 # 固定 pip 包版本
    - DL3014 # 使用 -y 开关
    - DL3015 # 避免额外的包
    - DL3016 # 固定 npm 包版本
    - DL3018 # 固定 apk 包版本
    - DL3019 # 使用 --no-cache 开关
    - DL3020 # 使用 COPY 而不是 ADD
    - DL3021 # COPY 使用多个参数
    - DL3022 # COPY --from 应该引用之前的阶段
    - DL3023 # COPY --from 应该引用特定阶段
    - DL3024 # FROM 别名大小写
    - DL3025 # 使用 JSON 数组格式
    - DL3026 # 使用 --mount 缓存
    - DL3027 # 不要使用 apt
    - DL3028 # 固定 gem 版本
    - DL3029 # 不要使用 --platform
    - DL3030 # 使用 COPY --link
    - DL4000 # MAINTAINER 已弃用
    - DL4001 # wget 或 curl 或 ADD
    - DL4003 # 多个 CMD
    - DL4004 # 多个 ENTRYPOINT
    - DL4005 # 使用 SHELL
    - DL4006 # 设置 SHELL 选项

  # 禁用的规则 (有特殊原因时)
  disable:
    # DL3008  # 在开发环境中可能需要灵活的包版本
    # DL3013  # 在开发环境中可能需要灵活的 pip 版本

# ==========================================
# 忽略配置
# ==========================================
ignore:
  # 全局忽略的规则
  global: []

  # 按文件忽略的规则
  files:
    # 开发环境 Dockerfile
    "Dockerfile.dev":
      - DL3008 # 开发环境允许不固定包版本
      - DL3013 # 开发环境允许不固定 pip 版本

    # 测试环境 Dockerfile
    "Dockerfile.test":
      - DL3008
      - DL3013

# ==========================================
# 严重性配置
# ==========================================
severity:
  # 错误级别
  error:
    - DL3001 # 避免使用 sudo
    - DL3002 # 最后一个 USER 不应该是 root
    - DL3004 # 不要使用 sudo
    - DL4000 # MAINTAINER 已弃用
    - DL4003 # 多个 CMD
    - DL4004 # 多个 ENTRYPOINT

  # 警告级别
  warning:
    - DL3005 # 不要使用 apt-get upgrade
    - DL3006 # 总是标记镜像版本
    - DL3007 # 使用 --no-cache 开关
    - DL3008 # 固定 apt 包版本
    - DL3009 # 删除 apt 缓存
    - DL3012 # 提供 HEALTHCHECK
    - DL3013 # 固定 pip 包版本
    - DL3016 # 固定 npm 包版本
    - DL3018 # 固定 apk 包版本
    - DL3020 # 使用 COPY 而不是 ADD

  # 信息级别
  info:
    - DL3000 # 使用绝对 WORKDIR
    - DL3003 # 使用 WORKDIR 切换目录
    - DL3010 # 使用 ADD 复制远程文件
    - DL3011 # 有效的 EXPOSE 端口
    - DL3014 # 使用 -y 开关
    - DL3015 # 避免额外的包

# ==========================================
# 自定义规则
# ==========================================
custom_rules:
  # 安全相关自定义检查
  security:
    - name: "no-root-user"
      description: "确保不以 root 用户运行"
      pattern: "USER root"
      severity: "error"

    - name: "healthcheck-required"
      description: "要求提供健康检查"
      pattern: "HEALTHCHECK"
      required: true
      severity: "warning"

    - name: "minimal-base-image"
      description: "推荐使用最小化基础镜像"
      pattern: "FROM.*alpine"
      recommended: true
      severity: "info"

# ==========================================
# 报告配置
# ==========================================
reporting:
  # 报告格式
  formats:
    - json
    - checkstyle
    - codeclimate
    - gitlab_codeclimate
    - gnu
    - sarif
    - sonarqube
    - tty

  # 输出文件
  outputs:
    json: "hadolint-report.json"
    sarif: "hadolint-report.sarif"
    checkstyle: "hadolint-checkstyle.xml"

  # 包含统计信息
  include_stats: true

  # 详细输出
  verbose: true

# ==========================================
# 集成配置
# ==========================================
integrations:
  # GitLab CI/CD
  gitlab:
    # 生成 GitLab 代码质量报告
    code_quality: true

    # 报告文件路径
    report_path: "hadolint-gitlab-report.json"

  # SonarQube
  sonarqube:
    # 启用 SonarQube 集成
    enabled: false

    # 项目键
    project_key: "hege-tech-web"

# ==========================================
# 环境特定配置
# ==========================================
environments:
  # 开发环境
  development:
    strict: false
    ignore_warnings: true
    custom_rules:
      security:
        enabled: false

  # 测试环境
  staging:
    strict: true
    ignore_warnings: false
    custom_rules:
      security:
        enabled: true

  # 生产环境
  production:
    strict: true
    ignore_warnings: false
    fail_on_warning: true
    custom_rules:
      security:
        enabled: true
        fail_on_violation: true

# ==========================================
# 缓存配置
# ==========================================
cache:
  # 启用缓存
  enabled: true

  # 缓存目录
  directory: ".hadolint-cache"

  # 缓存过期时间 (小时)
  ttl: 24

# ==========================================
# 通知配置
# ==========================================
notifications:
  # 启用通知
  enabled: true

  # 通知条件
  conditions:
    - severity: "error"
      count: "> 0"
    - severity: "warning"
      count: "> 10"

  # 通知渠道
  channels:
    email:
      recipients:
        - "<EMAIL>"
        - "<EMAIL>"

    slack:
      webhook: "${SLACK_DEVOPS_WEBHOOK}"
      channel: "#devops-alerts"
