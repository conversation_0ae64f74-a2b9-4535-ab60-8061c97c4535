# 🚀 上海荷阁科技 - 本地开发环境 Docker Compose 配置
# 用于本地开发和测试

# 注意：Docker Compose v2 不再需要 version 字段

services:
  # ==========================================
  # Next.js 应用服务
  # ==========================================
  app:
    build:
      context: ../../
      dockerfile: .cicd/docker/Dockerfile
    container_name: hege-tech-web-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_SITE_URL=http://localhost:3000
      - NEXT_PUBLIC_SITE_NAME=上海荷阁科技有限公司
    volumes:
      - ../../src:/app/src:ro
      - ../../public:/app/public:ro
      - ../../package.json:/app/package.json:ro
      - ../../next.config.js:/app/next.config.js:ro
      - ../../tailwind.config.js:/app/tailwind.config.js:ro
      - ../../tsconfig.json:/app/tsconfig.json:ro
    networks:
      - hege-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ==========================================
  # PostgreSQL 数据库服务
  # ==========================================
  database:
    image: postgres:15-alpine
    pull_policy: missing
    container_name: hege-tech-db-dev
    environment:
      - POSTGRES_DB=hege_tech_db
      - POSTGRES_USER=hege_user
      - POSTGRES_PASSWORD=hege_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    ports:
      - "5432:5432"
    networks:
      - hege-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hege_user -d hege_tech_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ==========================================
  # Redis 缓存服务
  # ==========================================
  redis:
    image: redis:7-alpine
    pull_policy: missing
    container_name: hege-tech-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - hege-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # ==========================================
  # Nginx 反向代理服务
  # ==========================================
  nginx:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    container_name: hege-tech-nginx-dev
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - hege-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

# ==========================================
# 网络配置
# ==========================================
networks:
  hege-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ==========================================
# 数据卷配置
# ==========================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
