import { NextRequest, NextResponse } from "next/server";
import {
  sanitizeInput,
  escapeHtml,
  isValidEmail,
  isValidPhone,
  getClientIP,
  checkRateLimit,
  logSecurityEvent,
} from "@/lib/security";

interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  company: string;
  message: string;
}

export async function POST(request: NextRequest) {
  const ip = getClientIP(request);
  const userAgent = request.headers.get("user-agent") || "unknown";

  try {
    // 速率限制检查 - 每个IP每小时最多5次提交
    const rateLimit = checkRateLimit(`contact_${ip}`, 5, 3600000);
    if (!rateLimit.allowed) {
      logSecurityEvent({
        type: "rate_limit_exceeded",
        ip,
        userAgent,
        details: { endpoint: "/api/contact", limit: 5 },
      });

      return NextResponse.json(
        { success: false, message: "提交过于频繁，请1小时后再试" },
        { status: 429 },
      );
    }

    const data: ContactFormData = await request.json();

    // 输入验证和清理
    const cleanData = {
      name: sanitizeInput(data.name),
      email: sanitizeInput(data.email),
      phone: sanitizeInput(data.phone),
      company: sanitizeInput(data.company),
      message: sanitizeInput(data.message),
    };

    // 必填字段验证
    if (!cleanData.name || !cleanData.email || !cleanData.message) {
      return NextResponse.json(
        { success: false, message: "请填写必填字段" },
        { status: 400 },
      );
    }

    // 字段长度验证
    if (cleanData.name.length > 50) {
      return NextResponse.json(
        { success: false, message: "姓名长度不能超过50个字符" },
        { status: 400 },
      );
    }

    if (cleanData.message.length > 1000) {
      return NextResponse.json(
        { success: false, message: "留言内容不能超过1000个字符" },
        { status: 400 },
      );
    }

    // 邮箱格式验证
    if (!isValidEmail(cleanData.email)) {
      return NextResponse.json(
        { success: false, message: "请输入有效的邮箱地址" },
        { status: 400 },
      );
    }

    // 电话号码验证（如果提供）
    if (cleanData.phone && !isValidPhone(cleanData.phone)) {
      return NextResponse.json(
        { success: false, message: "请输入有效的手机号码" },
        { status: 400 },
      );
    }

    // 检查垃圾信息
    if (isSpamContent(cleanData.message)) {
      logSecurityEvent({
        type: "suspicious_activity",
        ip,
        userAgent,
        details: { reason: "spam_content", data: cleanData },
      });

      return NextResponse.json(
        { success: false, message: "检测到可疑内容，请重新填写" },
        { status: 400 },
      );
    }

    // HTML转义处理
    const safeData = {
      name: escapeHtml(cleanData.name),
      email: escapeHtml(cleanData.email),
      phone: escapeHtml(cleanData.phone),
      company: escapeHtml(cleanData.company),
      message: escapeHtml(cleanData.message),
    };

    // 记录成功提交
    logSecurityEvent({
      type: "suspicious_activity",
      ip,
      userAgent,
      details: { action: "contact_form_submit", email: safeData.email },
    });

    // 这里可以添加发送邮件或保存到数据库的逻辑
    console.log("收到联系表单:", safeData);

    // 模拟处理时间
    await new Promise((resolve) => setTimeout(resolve, 1000));

    return NextResponse.json({
      success: true,
      message: "感谢您的留言，我们会尽快与您联系！",
    });
  } catch (error) {
    console.error("联系表单处理失败:", error);

    logSecurityEvent({
      type: "suspicious_activity",
      ip,
      userAgent,
      details: { error: "contact_form_error", message: String(error) },
    });

    return NextResponse.json(
      { success: false, message: "提交失败，请稍后重试" },
      { status: 500 },
    );
  }
}

/**
 * 检查是否为垃圾信息
 */
function isSpamContent(content: string): boolean {
  const spamPatterns = [
    /viagra/i,
    /casino/i,
    /lottery/i,
    /winner/i,
    /congratulations/i,
    /click here/i,
    /free money/i,
    /make money/i,
    /work from home/i,
    /http[s]?:\/\//i, // 包含链接
    /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/, // 信用卡号码模式
  ];

  return spamPatterns.some((pattern) => pattern.test(content));
}
