# 🚀 上海荷阁科技 - Next.js 应用 Docker 镜像
# 多阶段构建，优化镜像大小和安全性

# ==========================================
# 阶段 1: 依赖安装
# ==========================================
FROM node:18-alpine AS deps

# 设置工作目录
WORKDIR /app

# 配置Alpine镜像源（使用国内镜像）
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装系统依赖
RUN apk add --no-cache libc6-compat

# 复制包管理文件
COPY package.json package-lock.json* ./

# 安装依赖 - 如果 lock 文件不同步，先重新生成
RUN if [ -f package-lock.json ]; then \
        npm ci --omit=dev --cache .npm --prefer-offline || \
        (rm -f package-lock.json && npm install --omit=dev --cache .npm --prefer-offline); \
    else \
        npm install --omit=dev --cache .npm --prefer-offline; \
    fi

# ==========================================
# 阶段 2: 构建应用
# ==========================================
FROM node:18-alpine AS builder

WORKDIR /app

# 复制依赖
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 设置环境变量
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# 构建应用
RUN npm run build

# ==========================================
# 阶段 3: 运行时镜像
# ==========================================
FROM node:18-alpine AS runner

WORKDIR /app

# 配置Alpine镜像源（使用国内镜像）并安装必要软件
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk update && \
    apk add --no-cache curl ca-certificates && \
    addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 复制必要文件
COPY --from=builder /app/public ./public

# 设置正确的权限
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# 创建健康检查脚本
RUN echo '#!/bin/sh\ncurl -f http://localhost:3000/api/health || exit 1' > /app/health-check.sh && \
    chmod +x /app/health-check.sh

# 切换到非 root 用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD /app/health-check.sh

# 启动应用
CMD ["node", "server.js"]

# 元数据标签
LABEL maintainer="<EMAIL>"
LABEL version="1.0.0"
LABEL description="上海荷阁科技有限公司企业官网"
LABEL org.opencontainers.image.source="https://github.com/hege-tech/hege-tech-web"
LABEL org.opencontainers.image.vendor="上海荷阁科技有限公司"
LABEL org.opencontainers.image.title="Hege Tech Web"
LABEL org.opencontainers.image.description="企业级 Next.js 应用容器镜像"
