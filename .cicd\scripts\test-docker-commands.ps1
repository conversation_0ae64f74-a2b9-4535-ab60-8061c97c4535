# Shanghai Hege Technology - Docker Commands Test
# Test the exact Docker commands from GitLab CI/CD

$ErrorActionPreference = "Stop"

Write-Host "Testing Docker commands syntax..." -ForegroundColor Green

try {
    # Set up test environment variables (same as in GitLab CI/CD)
    $env:LOCAL_IMAGE_NAME = "hege-tech-web"
    $env:LOCAL_IMAGE_TAG = "test-tag"
    $env:CONTAINER_NAME = "hege-tech-web-local"
    $env:HOST_PORT = "3000"
    $env:CONTAINER_PORT = "3000"

    Write-Host "Environment variables:" -ForegroundColor Yellow
    Write-Host "LOCAL_IMAGE_NAME: $env:LOCAL_IMAGE_NAME" -ForegroundColor White
    Write-Host "LOCAL_IMAGE_TAG: $env:LOCAL_IMAGE_TAG" -ForegroundColor White
    Write-Host "CONTAINER_NAME: $env:CONTAINER_NAME" -ForegroundColor White
    Write-Host "HOST_PORT: $env:HOST_PORT" -ForegroundColor White
    Write-Host "CONTAINER_PORT: $env:CONTAINER_PORT" -ForegroundColor White

    Write-Host "`nTesting Docker build command..." -ForegroundColor Yellow
    $buildCmd1 = "docker build -f .cicd/docker/Dockerfile -t `"$($env:LOCAL_IMAGE_NAME):$($env:LOCAL_IMAGE_TAG)`" -t `"$($env:LOCAL_IMAGE_NAME):latest`" ."
    Write-Host "Build command 1: $buildCmd1" -ForegroundColor Cyan

    Write-Host "`nTesting Docker run command..." -ForegroundColor Yellow
    $runCmd = "docker run -d --name $env:CONTAINER_NAME -p `"$($env:HOST_PORT):$($env:CONTAINER_PORT)`" `"$($env:LOCAL_IMAGE_NAME):latest`""
    Write-Host "Run command: $runCmd" -ForegroundColor Cyan

    Write-Host "`nTesting Docker inspect commands..." -ForegroundColor Yellow
    $inspectCmd1 = "docker inspect `"$($env:LOCAL_IMAGE_NAME):latest`""
    Write-Host "Inspect command: $inspectCmd1" -ForegroundColor Cyan

    Write-Host "`nTesting Docker management commands..." -ForegroundColor Yellow
    $stopCmd = "docker stop $env:CONTAINER_NAME"
    $rmCmd = "docker rm $env:CONTAINER_NAME"
    $imagesCmd = "docker images $env:LOCAL_IMAGE_NAME"
    
    Write-Host "Stop command: $stopCmd" -ForegroundColor Cyan
    Write-Host "Remove command: $rmCmd" -ForegroundColor Cyan
    Write-Host "Images command: $imagesCmd" -ForegroundColor Cyan

    Write-Host "`nTesting PowerShell script call..." -ForegroundColor Yellow
    $scriptCmd = "PowerShell.exe -ExecutionPolicy Bypass -File `".cicd\scripts\verify-local-deployment.ps1`" -ContainerName $env:CONTAINER_NAME -HostPort $env:HOST_PORT"
    Write-Host "Script command: $scriptCmd" -ForegroundColor Cyan

    Write-Host "`nAll Docker command syntax tests passed!" -ForegroundColor Green
    Write-Host "The commands should work correctly in GitLab CI/CD" -ForegroundColor Green
}
catch {
    Write-Host "Docker command test failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
