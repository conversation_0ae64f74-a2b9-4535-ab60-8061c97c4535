#!/bin/bash

# 上海荷阁科技网站每日自动提交脚本
# 使用方法: ./daily-commit.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取当前日期
CURRENT_DATE=$(date '+%Y-%m-%d')
CURRENT_TIME=$(date '+%H:%M:%S')

# 📌 版本号管理 - 内部项目规范
# 格式: V<主版本号>.<次版本号>.<修订号>[-阶段标识.迭代次数]
# 示例: V0.1.0-alpha.1, V0.5.0-beta.2, V0.9.0-rc.1, V1.0.0

# 获取最新的版本标签
LATEST_TAG=$(git tag -l "V*" | sort -V | tail -1)

# 版本号生成函数
generate_version() {
    if [ -z "$LATEST_TAG" ]; then
        # 首次发布，从 V1.0.0 开始（正式版本）
        echo "V1.0.0"
    else
        # 解析最新标签
        if [[ $LATEST_TAG =~ ^V([0-9]+)\.([0-9]+)\.([0-9]+)(-([a-z]+)\.([0-9]+))?$ ]]; then
            MAJOR=${BASH_REMATCH[1]}
            MINOR=${BASH_REMATCH[2]}
            PATCH=${BASH_REMATCH[3]}
            STAGE=${BASH_REMATCH[5]}
            ITERATION=${BASH_REMATCH[6]}

            # 每日更新策略：递增修订号（patch版本）
            NEW_PATCH=$((PATCH + 1))
            echo "V${MAJOR}.${MINOR}.${NEW_PATCH}"
        else
            # 标签格式不正确，使用默认版本
            echo "V1.0.0"
        fi
    fi
}

VERSION=$(generate_version)

log_info "开始执行每日提交任务 - $CURRENT_DATE $CURRENT_TIME"

# 检查是否在Git仓库中
if [ ! -d ".git" ]; then
    log_error "当前目录不是Git仓库！"
    exit 1
fi

# 检查是否有远程仓库
if ! git remote get-url origin > /dev/null 2>&1; then
    log_error "未配置远程仓库！"
    exit 1
fi

# 清理临时文件和缓存
log_info "清理临时文件和缓存..."
rm -rf .next/ out/ node_modules/.cache/ *.log 2>/dev/null || true
rm -f *.zip *.tar.gz *.tsbuildinfo 2>/dev/null || true

# 检查是否有变更
if git diff --quiet && git diff --staged --quiet; then
    log_warning "没有检测到文件变更，跳过提交"
    exit 0
fi

# 添加所有变更（.gitignore会自动排除不需要的文件）
log_info "添加文件变更..."
git add .

# 检查暂存区是否有文件
if git diff --staged --quiet; then
    log_warning "暂存区为空，没有需要提交的变更"
    exit 0
fi

# 生成提交信息 - 符合内部项目规范
COMMIT_MSG="📅 Daily Update $VERSION - $CURRENT_DATE

🔄 每日版本更新 (内部项目规范):
- 📝 代码优化和bug修复
- 🎨 UI/UX改进
- 🚀 性能优化
- 📱 响应式设计调整
- 🔧 配置文件更新

📊 变更统计:
$(git diff --staged --stat)

📋 版本信息:
- 🏷️  当前版本: $VERSION
- 📌 上一版本: ${LATEST_TAG:-"首次发布"}
- ⏰ 提交时间: $CURRENT_DATE $CURRENT_TIME
- 📎 版本规范: V<主版本>.<次版本>.<修订号>[-阶段标识.迭代次数]"

# 执行提交
log_info "执行Git提交..."
git commit -m "$COMMIT_MSG"

# 推送到远程仓库
log_info "推送到远程仓库..."
if git push origin main; then
    log_success "✅ 成功推送到远程仓库！"
else
    log_error "❌ 推送失败，请检查网络连接和仓库权限"
    exit 1
fi

# 创建版本标签 - 符合内部项目规范
log_info "创建版本标签 $VERSION (内部项目规范)..."
TAG_MESSAGE="📋 Release $VERSION - $CURRENT_DATE

🏷️  版本信息 (内部项目规范):
- 版本号: $VERSION
- 发布日期: $CURRENT_DATE $CURRENT_TIME
- 上一版本: ${LATEST_TAG:-"首次发布"}
- 版本类型: 正式版本 (每日更新)

📌 版本规范说明:
- 格式: V<主版本>.<次版本>.<修订号>[-阶段标识.迭代次数]
- 阶段标识: alpha(原型) → beta(测试) → rc(候选) → 正式版本
- 演进示例: V0.1.0-alpha.1 → V0.5.0-beta.2 → V0.9.0-rc.1 → V1.0.0
- 特别说明: 所有版本以大写V开头，严格递增

🔄 更新内容:
- 每日代码同步和优化
- Bug修复和性能改进
- 功能增强和界面优化
- 配置文件和依赖更新

📊 变更统计:
$(git diff HEAD~1 --stat 2>/dev/null || echo "首次提交，包含完整项目文件")"

if git tag -a "$VERSION" -m "$TAG_MESSAGE"; then
    log_info "推送标签到远程仓库..."
    if git push origin "$VERSION"; then
        log_success "✅ 版本标签 $VERSION 创建并推送成功！"
        log_info "📎 版本规范: V<主版本>.<次版本>.<修订号>[-阶段标识.迭代次数]"
        log_info "🚦 当前阶段: 正式版本 (无阶段标识)"
        log_info "🔁 推荐演进: V1.0.1 → V1.1.0 → V2.0.0"
    else
        log_warning "⚠️  标签推送失败，但代码已成功提交"
    fi
else
    log_warning "⚠️  标签创建失败（可能已存在），但代码已成功提交"
fi

# 显示仓库状态
log_info "当前仓库状态:"
git status --short
echo ""
git log --oneline -5

log_success "🎉 每日提交任务完成！"
log_info "📍 远程仓库: $(git remote get-url origin)"
log_info "🏷️  最新版本: $VERSION"
log_info "📅 提交日期: $CURRENT_DATE $CURRENT_TIME"
