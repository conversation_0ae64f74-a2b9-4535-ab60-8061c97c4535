'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import Navigation from '@/components/Navigation'
import { 
  Calendar, 
  Eye, 
  User, 
  Tag, 
  Search, 
  Filter, 
  ChevronDown,
  RefreshCw,
  ArrowRight,
  Clock,
  TrendingUp,
  BookOpen,
  ExternalLink
} from 'lucide-react'

interface NewsItem {
  id: number
  title: string
  summary: string
  content: string
  category: string
  author: string
  publishDate: string
  readCount: number
  tags: string[]
  image?: string
  source: string
  url?: string
}

interface NewsData {
  news: NewsItem[]
  statistics: {
    totalNews: number
    categories: string[]
    latestUpdate: string
    totalViews: number
  }
}

export default function NewsPage() {
  const [newsData, setNewsData] = useState<NewsItem[]>([])
  const [statistics, setStatistics] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedNews, setSelectedNews] = useState<number | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('全部')
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [dataSource, setDataSource] = useState<string>('')

  // 从API获取企业资讯数据
  const fetchNewsData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // 尝试从荷阁科技官网获取数据
      const response = await fetch('/api/hege-news')
      const result = await response.json()

      if (result.success) {
        setNewsData(result.data.news)
        setStatistics(result.data.statistics)
        setDataSource(result.data.source || '内部数据')
      } else {
        setError(result.message || '获取数据失败')
      }
    } catch (err) {
      setError('网络请求失败，请稍后重试')
      console.error('获取企业资讯数据失败:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchNewsData()
  }, [])

  // 获取所有类别
  const categories = ['全部', ...Array.from(new Set(newsData.map(n => n.category)))]

  // 过滤新闻
  const filteredNews = newsData.filter(newsItem => {
    const matchesSearch = newsItem.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         newsItem.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         newsItem.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === '全部' || newsItem.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  // 根据类别映射颜色
  const getCategoryColor = (category: string) => {
    const colorMap: { [key: string]: string } = {
      '合作动态': 'bg-blue-100 text-blue-800',
      '技术发布': 'bg-green-100 text-green-800',
      '展会活动': 'bg-purple-100 text-purple-800',
      '资质认证': 'bg-orange-100 text-orange-800',
      '企业动态': 'bg-indigo-100 text-indigo-800',
      '行业资讯': 'bg-teal-100 text-teal-800'
    }
    return colorMap[category] || 'bg-gray-100 text-gray-800'
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // 计算阅读时间
  const getReadingTime = (content: string) => {
    const wordsPerMinute = 200
    const wordCount = content.replace(/<[^>]*>/g, '').length
    const minutes = Math.ceil(wordCount / wordsPerMinute)
    return `${minutes} 分钟阅读`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        {/* 导航栏 */}
        <Navigation />
        
        {/* 页面头部 */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">企业资讯</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              了解荷阁科技最新动态，掌握汽车行业技术发展趋势
            </p>
          </div>
        </div>

        {/* 加载状态 */}
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <RefreshCw className="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4" />
            <p className="text-gray-600 text-lg">正在加载企业资讯...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        {/* 导航栏 */}
        <Navigation />
        
        {/* 页面头部 */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">企业资讯</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              了解荷阁科技最新动态，掌握汽车行业技术发展趋势
            </p>
          </div>
        </div>

        {/* 错误状态 */}
        <div className="flex items-center justify-center py-20">
          <div className="bg-red-50 border border-red-200 rounded-lg p-8 max-w-md mx-auto text-center">
            <div className="text-red-600 mb-4">⚠️</div>
            <h3 className="text-red-800 font-medium text-lg mb-2">数据加载失败</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={fetchNewsData}
              className="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* 导航栏 */}
      <Navigation />
      
      {/* 页面头部 */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <h1 className="text-4xl md:text-5xl font-bold">企业资讯</h1>
              <button
                onClick={fetchNewsData}
                disabled={loading}
                className="ml-4 p-2 text-white/80 hover:text-white transition-colors disabled:opacity-50"
                title="刷新数据"
              >
                <RefreshCw className={`w-6 h-6 ${loading ? 'animate-spin' : ''}`} />
              </button>
            </div>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto mb-4">
              了解荷阁科技最新动态，掌握汽车行业技术发展趋势
            </p>
            {dataSource && (
              <div className="text-sm text-blue-200 mb-8">
                数据来源: {dataSource === 'hege-tech.cn' ? '荷阁科技官网' : dataSource}
                {statistics?.latestUpdate && (
                  <span className="ml-4">
                    更新时间: {new Date(statistics.latestUpdate).toLocaleString('zh-CN')}
                  </span>
                )}
              </div>
            )}
            
            {/* 统计数据 */}
            {statistics && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">{statistics.totalNews}</div>
                  <div className="text-blue-200">资讯文章</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">{statistics.categories?.length || 0}</div>
                  <div className="text-blue-200">资讯分类</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">{Math.floor(statistics.totalViews / 1000)}K+</div>
                  <div className="text-blue-200">总阅读量</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">24/7</div>
                  <div className="text-blue-200">实时更新</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          {/* 搜索框 */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="搜索资讯标题、内容或标签..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* 类别筛选 */}
          <div className="relative">
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="flex items-center gap-2 px-6 py-3 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Filter className="w-5 h-5" />
              {selectedCategory}
              <ChevronDown className="w-4 h-4" />
            </button>
            
            {isFilterOpen && (
              <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10">
                {categories.map(category => (
                  <button
                    key={category}
                    onClick={() => {
                      setSelectedCategory(category)
                      setIsFilterOpen(false)
                    }}
                    className={`w-full text-left px-4 py-2 hover:bg-blue-50 transition-colors ${
                      selectedCategory === category ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 资讯结果统计 */}
        <div className="mb-8">
          <p className="text-gray-600">
            找到 <span className="font-semibold text-blue-600">{filteredNews.length}</span> 篇相关资讯
          </p>
        </div>

        {/* 资讯列表 */}
        {filteredNews.length > 0 ? (
          <div className="space-y-8">
            {filteredNews.map((newsItem, index) => (
              <article
                key={newsItem.id}
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
                onClick={() => setSelectedNews(selectedNews === newsItem.id ? null : newsItem.id)}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="md:flex">
                  {/* 资讯图片 */}
                  <div className="md:w-1/3 bg-gradient-to-br from-blue-500 to-indigo-600 relative overflow-hidden">
                    <div className="absolute inset-0 bg-black/20"></div>
                    <div className="absolute top-4 left-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(newsItem.category)}`}>
                        {newsItem.category}
                      </span>
                    </div>
                    {newsItem.image && (
                      <Image
                        src={newsItem.image}
                        alt={newsItem.title}
                        fill
                        className="object-contain p-8 opacity-60"
                      />
                    )}
                    <div className="absolute bottom-4 left-4 text-white">
                      <div className="flex items-center space-x-4 text-sm">
                        <div className="flex items-center">
                          <Eye className="w-4 h-4 mr-1" />
                          {newsItem.readCount}
                        </div>
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {getReadingTime(newsItem.content)}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 资讯内容 */}
                  <div className="md:w-2/3 p-8">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center text-sm text-gray-500">
                        <User className="w-4 h-4 mr-1" />
                        {newsItem.author}
                        <span className="mx-2">•</span>
                        <Calendar className="w-4 h-4 mr-1" />
                        {formatDate(newsItem.publishDate)}
                      </div>
                    </div>

                    <h2 className="text-2xl font-bold text-gray-900 mb-4 line-clamp-2 hover:text-blue-600 transition-colors">
                      {newsItem.title}
                    </h2>

                    <p className="text-gray-600 mb-6 line-clamp-3 leading-relaxed">
                      {newsItem.summary}
                    </p>

                    {/* 标签 */}
                    <div className="flex flex-wrap gap-2 mb-6">
                      {newsItem.tags.slice(0, 4).map((tag, idx) => (
                        <span
                          key={idx}
                          className="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-600 rounded-md text-xs"
                        >
                          <Tag className="w-3 h-3 mr-1" />
                          {tag}
                        </span>
                      ))}
                    </div>

                    {/* 阅读更多按钮 */}
                    <button
                      className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium transition-colors group"
                      onClick={(e) => {
                        e.stopPropagation()
                        setSelectedNews(selectedNews === newsItem.id ? null : newsItem.id)
                      }}
                    >
                      {selectedNews === newsItem.id ? '收起' : '阅读全文'}
                      <ArrowRight className={`w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform ${selectedNews === newsItem.id ? 'rotate-90' : ''}`} />
                    </button>
                  </div>
                </div>

                {/* 展开的完整内容 */}
                {selectedNews === newsItem.id && (
                  <div className="border-t border-gray-200 bg-gray-50 p-8">
                    <div className="max-w-4xl mx-auto">
                      <h3 className="text-xl font-semibold text-gray-900 mb-6">完整内容</h3>
                      <div
                        className="prose prose-lg max-w-none text-gray-700 leading-relaxed"
                        dangerouslySetInnerHTML={{ __html: newsItem.content }}
                      />

                      {/* 文章底部信息 */}
                      <div className="mt-8 pt-6 border-t border-gray-300">
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center space-x-4">
                            <span>作者：{newsItem.author}</span>
                            <span>发布时间：{formatDate(newsItem.publishDate)}</span>
                            <span>阅读量：{newsItem.readCount}</span>
                          </div>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              setSelectedNews(null)
                            }}
                            className="text-blue-600 hover:text-blue-700 font-medium"
                          >
                            收起内容
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </article>
            ))}
          </div>
        ) : (
          <div className="text-center py-20">
            <div className="text-gray-400 mb-4">
              <BookOpen className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">未找到相关资讯</h3>
            <p className="text-gray-500">请尝试调整搜索条件或筛选类别</p>
          </div>
        )}
      </div>
    </div>
  )
}
