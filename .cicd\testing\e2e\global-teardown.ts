// 🚀 上海荷阁科技 - Playwright 全局清理
// 在所有测试结束后执行

import { FullConfig } from "@playwright/test";
import fs from "fs";
import path from "path";

async function globalTeardown(config: FullConfig) {
  console.log("🧹 开始 E2E 测试全局清理...");

  try {
    // 生成测试报告摘要
    console.log("📊 生成测试报告摘要...");
    await generateTestSummary();

    // 清理临时文件
    console.log("🗑️ 清理临时文件...");
    await cleanupTempFiles();

    // 压缩测试结果
    console.log("📦 压缩测试结果...");
    await compressTestResults();

    console.log("✅ E2E 测试全局清理完成");
  } catch (error) {
    console.error("❌ E2E 测试全局清理失败:", error);
  }
}

// 生成测试报告摘要
async function generateTestSummary() {
  const resultsPath = path.join(
    process.cwd(),
    "coverage",
    "playwright-results.json",
  );

  if (!fs.existsSync(resultsPath)) {
    console.warn("⚠️ 测试结果文件不存在");
    return;
  }

  try {
    const results = JSON.parse(fs.readFileSync(resultsPath, "utf8"));

    const summary = {
      project: "上海荷阁科技企业官网",
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || "test",
      stats: {
        total:
          results.suites?.reduce(
            (acc: number, suite: any) => acc + (suite.specs?.length || 0),
            0,
          ) || 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        duration: 0,
      },
      browsers: [],
      coverage: {
        enabled: false,
        threshold: 80,
      },
    };

    // 计算统计信息
    if (results.suites) {
      results.suites.forEach((suite: any) => {
        if (suite.specs) {
          suite.specs.forEach((spec: any) => {
            if (spec.tests) {
              spec.tests.forEach((test: any) => {
                if (test.results) {
                  test.results.forEach((result: any) => {
                    switch (result.status) {
                      case "passed":
                        summary.stats.passed++;
                        break;
                      case "failed":
                        summary.stats.failed++;
                        break;
                      case "skipped":
                        summary.stats.skipped++;
                        break;
                    }
                    summary.stats.duration += result.duration || 0;
                  });
                }
              });
            }
          });
        }
      });
    }

    // 保存摘要
    const summaryPath = path.join(
      process.cwd(),
      "coverage",
      "e2e-summary.json",
    );
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));

    console.log("📋 测试摘要:");
    console.log(`   总计: ${summary.stats.total}`);
    console.log(`   通过: ${summary.stats.passed}`);
    console.log(`   失败: ${summary.stats.failed}`);
    console.log(`   跳过: ${summary.stats.skipped}`);
    console.log(`   耗时: ${(summary.stats.duration / 1000).toFixed(2)}s`);
  } catch (error) {
    console.error("❌ 生成测试摘要失败:", error);
  }
}

// 清理临时文件
async function cleanupTempFiles() {
  const tempDirs = [
    path.join(process.cwd(), ".playwright"),
    path.join(process.cwd(), "test-results"),
  ];

  for (const dir of tempDirs) {
    if (fs.existsSync(dir)) {
      try {
        fs.rmSync(dir, { recursive: true, force: true });
        console.log(`🗑️ 已清理: ${dir}`);
      } catch (error) {
        console.warn(`⚠️ 清理失败: ${dir}`, error);
      }
    }
  }
}

// 压缩测试结果
async function compressTestResults() {
  const coverageDir = path.join(process.cwd(), "coverage");

  if (!fs.existsSync(coverageDir)) {
    return;
  }

  try {
    // 这里可以添加压缩逻辑
    // 例如使用 tar 或 zip 压缩测试结果
    console.log("📦 测试结果已准备完成");
  } catch (error) {
    console.error("❌ 压缩测试结果失败:", error);
  }
}

export default globalTeardown;
