#!/bin/bash
# 🚀 上海荷阁科技 - 生产环境部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
DEPLOY_DIR="/opt/hege-tech-web"
BACKUP_DIR="/opt/backups/hege-tech-web"
DOCKER_COMPOSE_FILE="$PROJECT_ROOT/.cicd/docker/docker-compose.prod.yml"

# 环境变量
ENVIRONMENT="production"
DOCKER_REGISTRY="${DOCKER_REGISTRY:-registry.gitlab.com/hege-tech/hege-tech-web}"
IMAGE_TAG="${CI_COMMIT_SHA:-latest}"
PRODUCTION_HOST="${PRODUCTION_HOST:-hege-tech.cn}"

# 安全检查
REQUIRE_CONFIRMATION=true
MAINTENANCE_MODE=false

# 帮助信息
show_help() {
    cat << EOF
🚀 上海荷阁科技生产环境部署脚本

用法: $0 [选项]

选项:
    -t, --tag TAG              Docker 镜像标签 (默认: $IMAGE_TAG)
    -h, --host HOST            目标主机 (默认: $PRODUCTION_HOST)
    -b, --backup               部署前创建备份 (强制)
    -r, --rollback             回滚到上一个版本
    -c, --check                仅检查部署状态
    -m, --maintenance          启用维护模式
    -f, --force                跳过确认提示 (危险)
    --help                     显示此帮助信息

示例:
    $0 -t v1.0.0                       # 部署指定版本
    $0 -r                              # 回滚到上一个版本
    $0 -c                              # 检查当前部署状态
    $0 -m                              # 启用维护模式

⚠️  警告: 这是生产环境部署脚本，请谨慎操作！

EOF
}

# 解析命令行参数
BACKUP=true  # 生产环境强制备份
ROLLBACK=false
CHECK_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -h|--host)
            PRODUCTION_HOST="$2"
            shift 2
            ;;
        -b|--backup)
            BACKUP=true
            shift
            ;;
        -r|--rollback)
            ROLLBACK=true
            shift
            ;;
        -c|--check)
            CHECK_ONLY=true
            shift
            ;;
        -m|--maintenance)
            MAINTENANCE_MODE=true
            shift
            ;;
        -f|--force)
            REQUIRE_CONFIRMATION=false
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 安全确认
security_confirmation() {
    if [[ "$REQUIRE_CONFIRMATION" == "true" ]]; then
        echo ""
        log_warning "⚠️  您即将在生产环境执行部署操作！"
        log_warning "环境: $ENVIRONMENT"
        log_warning "主机: $PRODUCTION_HOST"
        log_warning "镜像: $DOCKER_REGISTRY:$IMAGE_TAG"
        echo ""
        
        read -p "请输入 'DEPLOY' 确认继续: " confirmation
        
        if [[ "$confirmation" != "DEPLOY" ]]; then
            log_info "部署已取消"
            exit 0
        fi
        
        echo ""
        log_info "确认收到，开始部署..."
    fi
}

log_info "🚀 生产环境部署脚本"
log_info "环境: $ENVIRONMENT"
log_info "主机: $PRODUCTION_HOST"
log_info "镜像标签: $IMAGE_TAG"
log_info "部署目录: $DEPLOY_DIR"

# 检查部署状态
check_deployment_status() {
    log_info "检查生产环境部署状态..."
    
    if docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "Up"; then
        log_success "✅ 应用正在运行"
        
        # 检查健康状态
        if curl -f -s "https://$PRODUCTION_HOST/api/health" >/dev/null; then
            log_success "✅ 健康检查通过"
        else
            log_warning "⚠️ 健康检查失败"
        fi
        
        # 显示容器状态
        log_info "容器状态:"
        docker-compose -f "$DOCKER_COMPOSE_FILE" ps
        
        # 显示资源使用情况
        log_info "资源使用情况:"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
        
    else
        log_warning "⚠️ 应用未运行"
    fi
}

# 如果只是检查状态，直接返回
if [[ "$CHECK_ONLY" == "true" ]]; then
    check_deployment_status
    exit 0
fi

# 维护模式
enable_maintenance_mode() {
    log_info "启用维护模式..."
    
    # 创建维护页面
    cat > /tmp/maintenance.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统维护中 - 上海荷阁科技</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .logo { width: 200px; margin-bottom: 30px; }
        h1 { color: #333; }
        p { color: #666; line-height: 1.6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 系统维护中</h1>
        <p>上海荷阁科技官网正在进行系统升级维护，预计很快恢复正常。</p>
        <p>给您带来的不便，我们深表歉意。</p>
        <p>如有紧急事务，请联系：18221165813</p>
        <p>或发送邮件至：<EMAIL></p>
    </div>
</body>
</html>
EOF
    
    # 部署维护页面 (这里需要根据实际的负载均衡器配置)
    # nginx 配置或其他方式
    
    log_success "✅ 维护模式已启用"
}

disable_maintenance_mode() {
    log_info "禁用维护模式..."
    
    # 恢复正常服务 (这里需要根据实际配置)
    
    log_success "✅ 维护模式已禁用"
}

# 如果启用维护模式
if [[ "$MAINTENANCE_MODE" == "true" ]]; then
    security_confirmation
    enable_maintenance_mode
    exit 0
fi

# 创建生产环境备份
create_production_backup() {
    log_info "创建生产环境备份..."
    
    BACKUP_NAME="production-backup-$(date +%Y%m%d-%H%M%S)"
    BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"
    
    mkdir -p "$BACKUP_DIR"
    
    # 备份应用文件
    if [[ -d "$DEPLOY_DIR" ]]; then
        cp -r "$DEPLOY_DIR" "$BACKUP_PATH"
        log_success "✅ 应用文件备份完成"
    fi
    
    # 备份数据库 (如果有)
    if command -v pg_dump >/dev/null 2>&1; then
        log_info "备份数据库..."
        pg_dump -h localhost -U postgres hege_tech_db > "$BACKUP_PATH/database.sql"
        log_success "✅ 数据库备份完成"
    fi
    
    # 压缩备份
    tar -czf "$BACKUP_PATH.tar.gz" -C "$BACKUP_DIR" "$BACKUP_NAME"
    rm -rf "$BACKUP_PATH"
    
    log_success "✅ 生产环境备份创建完成: $BACKUP_PATH.tar.gz"
    
    # 清理旧备份 (保留最近30个)
    find "$BACKUP_DIR" -name "production-backup-*.tar.gz" | sort -r | tail -n +31 | xargs rm -f
}

# 回滚生产环境
rollback_production() {
    log_info "开始生产环境回滚..."
    
    # 安全确认
    echo ""
    log_warning "⚠️  您即将回滚生产环境！"
    read -p "请输入 'ROLLBACK' 确认继续: " confirmation
    
    if [[ "$confirmation" != "ROLLBACK" ]]; then
        log_info "回滚已取消"
        exit 0
    fi
    
    # 查找最新的备份
    LATEST_BACKUP=$(find "$BACKUP_DIR" -name "production-backup-*.tar.gz" | sort -r | head -n 1)
    
    if [[ -z "$LATEST_BACKUP" ]]; then
        log_error "❌ 未找到可用的生产环境备份"
        exit 1
    fi
    
    log_info "回滚到备份: $LATEST_BACKUP"
    
    # 启用维护模式
    enable_maintenance_mode
    
    # 停止当前服务
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # 恢复备份
    TEMP_DIR="/tmp/rollback-$(date +%s)"
    mkdir -p "$TEMP_DIR"
    tar -xzf "$LATEST_BACKUP" -C "$TEMP_DIR"
    
    rm -rf "$DEPLOY_DIR"
    mv "$TEMP_DIR"/* "$DEPLOY_DIR"
    rm -rf "$TEMP_DIR"
    
    # 启动服务
    cd "$DEPLOY_DIR"
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    # 等待服务启动
    sleep 60
    
    # 禁用维护模式
    disable_maintenance_mode
    
    log_success "✅ 生产环境回滚完成"
}

# 如果是回滚操作
if [[ "$ROLLBACK" == "true" ]]; then
    rollback_production
    check_deployment_status
    exit 0
fi

# 生产环境预检查
production_pre_checks() {
    log_info "执行生产环境预检查..."
    
    # 检查镜像安全扫描结果
    log_info "检查镜像安全扫描结果..."
    # 这里应该集成安全扫描工具的结果检查
    
    # 检查性能测试结果
    log_info "检查性能测试结果..."
    # 这里应该检查 Lighthouse 等性能测试结果
    
    # 检查依赖漏洞
    log_info "检查依赖漏洞..."
    # 这里应该检查 npm audit 结果
    
    log_success "✅ 生产环境预检查通过"
}

# 蓝绿部署
blue_green_deployment() {
    log_info "执行蓝绿部署..."
    
    # 启用维护模式
    enable_maintenance_mode
    
    # 部署新版本到绿色环境
    log_info "部署到绿色环境..."
    
    # 这里实现蓝绿部署逻辑
    # 1. 启动新版本容器
    # 2. 健康检查
    # 3. 切换流量
    # 4. 停止旧版本
    
    # 禁用维护模式
    disable_maintenance_mode
    
    log_success "✅ 蓝绿部署完成"
}

# 生产环境部署后验证
production_post_verification() {
    log_info "执行生产环境部署后验证..."
    
    # 全面健康检查
    local max_attempts=20
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "健康检查 (第 $attempt 次)..."
        
        if curl -f -s "https://$PRODUCTION_HOST/api/health" >/dev/null; then
            log_success "✅ 健康检查通过"
            break
        else
            if [[ $attempt -eq $max_attempts ]]; then
                log_error "❌ 健康检查失败，准备回滚"
                rollback_production
                exit 1
            fi
            log_warning "⚠️ 健康检查失败，15秒后重试..."
            sleep 15
            ((attempt++))
        fi
    done
    
    # 功能验证
    log_info "执行关键功能验证..."
    
    # 测试关键页面
    local pages=("/" "/about" "/services" "/contact")
    for page in "${pages[@]}"; do
        if curl -f -s "https://$PRODUCTION_HOST$page" >/dev/null; then
            log_success "✅ 页面 $page 访问正常"
        else
            log_error "❌ 页面 $page 访问失败"
            rollback_production
            exit 1
        fi
    done
    
    # 性能检查
    log_info "执行性能检查..."
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' "https://$PRODUCTION_HOST/")
    if (( $(echo "$response_time < 3.0" | bc -l) )); then
        log_success "✅ 响应时间正常: ${response_time}s"
    else
        log_warning "⚠️ 响应时间较慢: ${response_time}s"
    fi
}

# 主部署流程
main() {
    # 安全确认
    security_confirmation
    
    # 创建备份
    create_production_backup
    
    # 生产环境预检查
    production_pre_checks
    
    # 蓝绿部署
    blue_green_deployment
    
    # 生产环境部署后验证
    production_post_verification
    
    # 最终状态检查
    check_deployment_status
    
    log_success "🎉 生产环境部署完成！"
    log_info "访问地址: https://$PRODUCTION_HOST"
    log_info "部署时间: $(date)"
    log_info "镜像版本: $DOCKER_REGISTRY:$IMAGE_TAG"
    
    # 发送部署通知
    log_info "发送部署通知..."
    # 这里可以集成邮件、Slack 等通知
}

# 错误处理
trap 'log_error "❌ 生产环境部署过程中发生错误"; rollback_production; exit 1' ERR

# 执行主流程
main
