<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Code Quality Checks" tests="2" failures="1" errors="0" time="0" timestamp="2025-07-18T15:51:56">
  <testsuite name="ESLint" tests="1" failures="0" errors="0" time="0">
    <testcase name="ESLint Code Analysis" classname="lint">
      <!-- ESLint passed with 4 warnings -->
    </testcase>
  </testsuite>
  <testsuite name="Prettier" tests="1" failures="1" errors="0" time="0">
    <testcase name="Code Format Check" classname="format">
      <failure message="Prettier found formatting issues in 155 files" type="FormatError">
        <![CDATA[npm WARN config cache-max This option has been deprecated in favor of `--prefer-online`

&gt; enterprise-website@0.1.0 format:check
&gt; prettier --check .

Checking formatting...
[warn] .cicd/docker/daemon.json
[warn] .cicd/docker/docker-compose.prod.yml
[warn] .cicd/docker/docker-compose.yml
[warn] .cicd/docker/README-NETWORK-FIX.md
[warn] .cicd/docs/best-practices.md
[warn] .cicd/docs/dependency-fix-summary.md
[warn] .cicd/docs/gitlab-ci-yaml-fix-summary.md
[warn] .cicd/docs/powershell-encoding-fix.md
[warn] .cicd/docs/powershell-syntax-fixes.md
[warn] .cicd/docs/README.md
[warn] .cicd/docs/runner-tags-configuration.md
[warn] .cicd/docs/troubleshooting-dependencies.md
[warn] .cicd/docs/troubleshooting.md
[warn] .cicd/docs/windows-unified-cicd.md
[warn] .cicd/README.md
[warn] .cicd/security/container/hadolint.yml
[warn] .cicd/security/container/trivy.yml
[warn] .cicd/security/dependency/dependency-check.yml
[warn] .cicd/security/sast/.eslintrc.security.js
[warn] .cicd/testing/e2e/global-setup.ts
[warn] .cicd/testing/e2e/global-teardown.ts
[warn] .cicd/testing/e2e/playwright.config.ts
[warn] .cicd/testing/jest/__mocks__/fileMock.js
[warn] .cicd/testing/jest/jest.config.js
[warn] .cicd/testing/jest/jest.setup.js
[warn] .cicd/testing/performance/lighthouse.config.js
[warn] .eslintrc.json
[error] .gitlab-ci.yml: SyntaxError: All collection items must start at the same column (180:7)
[error]   178 |     - echo "Lint and format check completed"
[error]   179 |     - echo "Checking final status..."
[error] &gt; 180 |     - PowerShell.exe -ExecutionPolicy Bypass -Command "
[error]       |       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 181 |         if (Test-Path 'lint-results.json') {
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 182 |           $report = Get-Content 'lint-results.json' | ConvertFrom-Json;
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 183 |           $overallStatus = $report.summary.overall.status;
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 184 |           Write-Host \"Overall status: $overallStatus\" -ForegroundColor $(if ($overallStatus -eq 'passed') { 'Green' } else { 'Red' });
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 185 |           if ($overallStatus -eq 'failed') {
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 186 |             Write-Host 'Code quality checks failed. Please review the reports.' -ForegroundColor Red;
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 187 |             exit 1;
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 188 |           }
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 189 |         }
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 190 |       "
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 191 |   artifacts:
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 192 |     when: always
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 193 |     reports:
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 194 |       junit: lint-results.xml
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 195 |     paths:
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 196 |       - lint-results.json
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 197 |       - lint-results.xml
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 198 |       - lint-summary.md
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 199 |       - lint-output.txt
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 200 |       - format-check-output.txt
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 201 |     expire_in: 1 week
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 202 |   only:
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 203 |     - main
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 204 |     - develop
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 205 |     - merge_requests
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 206 |
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 207 | test:type-check:
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 208 |   stage: test
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 209 |   tags:
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 210 |     - windows
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 211 |     - shell
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 212 |   dependencies:
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 213 |     - prepare:dependencies
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 214 |   before_script:
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 215 |     - PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\setup-powershell-environment.ps1"
[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[error] &gt; 216 |     - echo "Starting CI/CD Pipeline - $(Get-Date)"
[error]       | ^
[error]   217 |     - echo "Project - $env:APP_NAME"
[error]   218 |     - echo "Branch - $env:CI_COMMIT_REF_NAME"
[error]   219 |     - echo "Commit - $env:CI_COMMIT_SHORT_SHA"
[warn] GIT_SETUP.md
[warn] next-env.d.ts
[warn] next.config.js
[warn] next.config.security.js
[warn] package-lock.json
[warn] package.json
[warn] postcss.config.js
[warn] README.md
[warn] SECURITY.md
[warn] src/app/about/page.tsx
[warn] src/app/admin/stats/page.tsx
[warn] src/app/api/contact/route.ts
[warn] src/app/api/health/route.ts
[warn] src/app/api/hege-cases/route.ts
[warn] src/app/api/hege-news/route.ts
[warn] src/app/api/success-cases/route.ts
[warn] src/app/api/tencent-news/route.ts
[warn] src/app/api/upload/route.ts
[warn] src/app/api/visitor-stats/route.ts
[warn] src/app/api/wechat-integration/route.ts
[warn] src/app/cases/page.tsx
[warn] src/app/cloud-services/page.tsx
[warn] src/app/contact/page.tsx
[warn] src/app/layout.tsx
[warn] src/app/news-integrated/page.tsx
[warn] src/app/news/page.tsx
[warn] src/app/page.tsx
[warn] src/app/security-test/page.tsx
[warn] src/app/services/page.tsx
[warn] src/components/ChatBot.tsx
[warn] src/components/Navigation.tsx
[warn] src/components/OurCustomers.tsx
[warn] src/components/SuccessCases.tsx
[warn] src/components/TencentNews.tsx
[warn] src/components/Timeline.tsx
[warn] src/components/ui/button.tsx
[warn] src/components/ui/card.tsx
[warn] src/components/WeChatQR.tsx
[warn] src/lib/security-config.ts
[warn] src/lib/security.ts
[warn] src/lib/utils.ts
[warn] src/middleware.ts
[warn] src/styles/globals.css
[warn] src/types/index.ts
[warn] tailwind.config.js
[warn] tsconfig.json
[warn] VERSION_MANAGEMENT.md
[warn] WECHAT_INTEGRATION_SUMMARY.md
Error occurred when checking code style in the above file.
]]>
      </failure>
    </testcase>
  </testsuite>
</testsuites>
