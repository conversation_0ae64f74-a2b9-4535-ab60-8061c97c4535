'use client'

import { useState, useEffect, useRef } from 'react'
import { MessageCircle, X, Send, Bot, Phone, Mail, QrCode } from 'lucide-react'

interface Message {
  id: string
  type: 'user' | 'bot'
  content: string
  timestamp: Date
  options?: string[]
  showQR?: boolean
}

interface VisitorStats {
  visits: number
  firstVisit: Date
  lastVisit: Date
  currentPage: string
  timeOnPage: number
}

const ChatBot = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [visitorStats, setVisitorStats] = useState<VisitorStats | null>(null)
  const [showProactiveChat, setShowProactiveChat] = useState(false)
  const [timeOnPage, setTimeOnPage] = useState(0)
  const [hasShownProactive, setHasShownProactive] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const pageStartTime = useRef(Date.now())

  // 访问统计
  useEffect(() => {
    const initVisitorStats = () => {
      const stats: VisitorStats = {
        visits: parseInt(localStorage.getItem('visitor_visits') || '0') + 1,
        firstVisit: new Date(localStorage.getItem('visitor_first_visit') || Date.now()),
        lastVisit: new Date(),
        currentPage: window.location.pathname,
        timeOnPage: 0
      }
      
      localStorage.setItem('visitor_visits', stats.visits.toString())
      if (!localStorage.getItem('visitor_first_visit')) {
        localStorage.setItem('visitor_first_visit', stats.firstVisit.toISOString())
      }
      
      setVisitorStats(stats)
    }

    initVisitorStats()
  }, [])

  // 页面停留时间统计
  useEffect(() => {
    const interval = setInterval(() => {
      const currentTime = Math.floor((Date.now() - pageStartTime.current) / 1000)
      setTimeOnPage(currentTime)
      
      if (visitorStats) {
        setVisitorStats(prev => prev ? { ...prev, timeOnPage: currentTime } : null)
      }
    }, 1000)

    return () => clearInterval(interval)
  }, [visitorStats])

  // 主动触发机制
  useEffect(() => {
    const checkProactiveTrigger = () => {
      const isCloudServicesPage = window.location.pathname.includes('cloud-services')
      const shouldTrigger = (isCloudServicesPage && timeOnPage >= 15) || timeOnPage >= 30

      if (shouldTrigger && !hasShownProactive && !isOpen) {
        setShowProactiveChat(true)
        setHasShownProactive(true)
      }
    }

    const interval = setInterval(checkProactiveTrigger, 1000)
    return () => clearInterval(interval)
  }, [timeOnPage, hasShownProactive, isOpen])

  // 主动弹窗5秒后自动消失
  useEffect(() => {
    if (showProactiveChat) {
      const timer = setTimeout(() => {
        setShowProactiveChat(false)
      }, 5000) // 5秒后自动消失

      return () => clearTimeout(timer)
    }
  }, [showProactiveChat])

  // 鼠标离开窗口检测
  useEffect(() => {
    const handleMouseLeave = (e: MouseEvent) => {
      if (e.clientY <= 0 && !hasShownProactive && !isOpen) {
        setShowProactiveChat(true)
        setHasShownProactive(true)
      }
    }

    document.addEventListener('mouseleave', handleMouseLeave)
    return () => document.removeEventListener('mouseleave', handleMouseLeave)
  }, [hasShownProactive, isOpen])

  // 滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // 初始化欢迎消息
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        type: 'bot',
        content: `您好！欢迎来到荷阁科技！👋\n\n我是您的专属顾问，很高兴为您服务。请问您对以下哪个方面比较感兴趣？`,
        timestamp: new Date(),
        options: ['3D HMI解决方案', '云服务代理', '商务合作', '技术咨询']
      }
      setMessages([welcomeMessage])
    }
  }, [isOpen, messages.length])

  const addMessage = (content: string, type: 'user' | 'bot', options?: string[], showQR?: boolean) => {
    const message: Message = {
      id: Date.now().toString(),
      type,
      content,
      timestamp: new Date(),
      options,
      showQR
    }
    setMessages(prev => [...prev, message])
  }

  const handleOptionClick = (option: string) => {
    addMessage(option, 'user')
    
    setTimeout(() => {
      switch (option) {
        case '3D HMI解决方案':
          addMessage(
            '太好了！我们的3D HMI解决方案是行业领先的智能座舱技术。\n\n🚗 核心优势：\n• 全3D交互界面\n• 一镜到底体验\n• 高性能渲染引擎\n• 定制化开发\n\n您希望了解具体的技术细节还是看看成功案例？',
            'bot',
            ['技术细节', '成功案例', '获取报价', '联系专家']
          )
          break
        case '云服务代理':
          addMessage(
            '荷阁科技是腾讯云核心代理商！🎉\n\n☁️ 我们提供：\n• 腾讯云全线产品代理\n• 专业技术支持\n• 优惠价格政策\n• 7x24小时服务\n\n您需要哪种云服务？',
            'bot',
            ['云服务器', '数据库', 'CDN加速', '人工智能', '联系专家']
          )
          break
        case '商务合作':
          addMessage(
            '感谢您对合作的兴趣！🤝\n\n我们欢迎各种形式的合作：\n• 技术合作伙伴\n• 渠道代理商\n• 项目合作\n• 投资合作\n\n请问您代表的是哪类企业？',
            'bot',
            ['汽车制造商', '技术公司', '投资机构', '其他', '直接联系']
          )
          break
        case '技术咨询':
          addMessage(
            '我们的技术专家团队随时为您服务！👨‍💻\n\n🔧 咨询范围：\n• 3D HMI技术方案\n• 智能座舱开发\n• 云服务架构\n• 项目实施指导\n\n您遇到了什么技术挑战？',
            'bot',
            ['性能优化', '架构设计', '开发指导', '问题诊断', '联系专家']
          )
          break
        case '联系专家':
        case '直接联系':
          addMessage(
            '太好了！看起来您有明确的需求，我来为您安排专业顾问。\n\n为了更好地为您服务，请添加我们的企业微信，专业顾问将在5分钟内与您联系：',
            'bot',
            [],
            true
          )
          break
        default:
          addMessage(
            '感谢您的选择！我们的专业顾问可以为您提供更详细的信息。\n\n请添加企业微信，获得一对一专业服务：',
            'bot',
            [],
            true
          )
      }
    }, 1000)
  }

  const handleSendMessage = () => {
    if (!inputValue.trim()) return
    
    addMessage(inputValue, 'user')
    setInputValue('')
    
    setTimeout(() => {
      addMessage(
        '感谢您的留言！我们的专业顾问会尽快为您提供详细解答。\n\n为了更高效的沟通，建议您添加我们的企业微信：',
        'bot',
        [],
        true
      )
    }, 1000)
  }

  const handleProactiveAccept = () => {
    setShowProactiveChat(false)
    setIsOpen(true)
  }

  const handleProactiveDecline = () => {
    setShowProactiveChat(false)
  }

  return (
    <>
      {/* 主动弹窗 */}
      {showProactiveChat && (
        <div className="fixed bottom-24 left-6 z-50 animate-bounce">
          <div className="bg-white rounded-lg shadow-2xl border border-gray-200 p-4 max-w-sm">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  <Bot className="w-6 h-6 text-white" />
                </div>
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-800 mb-3">
                  您好！我注意到您对我们的服务很感兴趣，需要我为您介绍一下吗？😊
                </p>
                <div className="flex space-x-2">
                  <button
                    onClick={handleProactiveAccept}
                    className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors"
                  >
                    好的
                  </button>
                  <button
                    onClick={handleProactiveDecline}
                    className="bg-gray-200 text-gray-700 px-3 py-1 rounded text-xs hover:bg-gray-300 transition-colors"
                  >
                    稍后
                  </button>
                </div>
              </div>
              <button
                onClick={handleProactiveDecline}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 悬浮按钮 */}
      {!isOpen && (
        <button
          onClick={() => setIsOpen(true)}
          className="fixed bottom-6 left-6 z-50 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-2xl transition-all duration-300 hover:scale-110 group"
        >
          <MessageCircle className="w-6 h-6" />
          <div className="absolute -top-2 -right-2 w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
          <div className="absolute left-full ml-3 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-3 py-1 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
            有问题？点击咨询
          </div>
        </button>
      )}

      {/* 聊天窗口 */}
      {isOpen && (
        <div className="fixed bottom-6 left-6 z-50 w-80 h-96 bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col">
          {/* 头部 */}
          <div className="bg-blue-600 text-white p-4 rounded-t-lg flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <Bot className="w-5 h-5" />
              </div>
              <div>
                <h3 className="font-semibold">荷阁科技顾问</h3>
                <p className="text-xs text-blue-100">在线 • 通常几分钟内回复</p>
              </div>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="text-blue-100 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* 消息区域 */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs px-3 py-2 rounded-lg ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  <p className="text-sm whitespace-pre-line">{message.content}</p>

                  {/* 选项按钮 */}
                  {message.options && (
                    <div className="mt-2 space-y-1">
                      {message.options.map((option, index) => (
                        <button
                          key={index}
                          onClick={() => handleOptionClick(option)}
                          className="block w-full text-left px-2 py-1 text-xs bg-white text-gray-700 rounded border hover:bg-gray-50 transition-colors"
                        >
                          {option}
                        </button>
                      ))}
                    </div>
                  )}

                  {/* 企业微信二维码 */}
                  {message.showQR && (
                    <div className="mt-3 p-3 bg-white rounded border">
                      <div className="text-center">
                        <div className="w-32 h-32 bg-gray-200 rounded mx-auto mb-2 flex items-center justify-center">
                          <QrCode className="w-16 h-16 text-gray-400" />
                        </div>
                        <p className="text-xs text-gray-600 mb-2">扫码添加企业微信</p>
                        <div className="text-xs text-gray-500 space-y-1">
                          <div className="flex items-center justify-center space-x-1">
                            <Phone className="w-3 h-3" />
                            <span>18221165813</span>
                          </div>
                          <div className="flex items-center justify-center space-x-1">
                            <Mail className="w-3 h-3" />
                            <span><EMAIL></span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>

          {/* 输入区域 */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex space-x-2">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="输入您的问题..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={handleSendMessage}
                className="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-2 text-center">
              访问次数: {visitorStats?.visits || 0} • 停留时间: {Math.floor(timeOnPage / 60)}分{timeOnPage % 60}秒
            </p>
          </div>
        </div>
      )}
    </>
  )
}

export default ChatBot
