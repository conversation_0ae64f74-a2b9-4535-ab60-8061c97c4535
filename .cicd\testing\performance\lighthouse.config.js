// 🚀 上海荷阁科技 - Lighthouse 性能测试配置
// 用于网站性能、可访问性、SEO 等指标测试

module.exports = {
  // ==========================================
  // 基础配置
  // ==========================================
  ci: {
    collect: {
      // 测试 URL 列表
      url: [
        'http://localhost:3000/',
        'http://localhost:3000/about',
        'http://localhost:3000/services',
        'http://localhost:3000/cloud-services',
        'http://localhost:3000/contact',
        'http://localhost:3000/cases',
        'http://localhost:3000/news'
      ],
      
      // 每个 URL 运行次数
      numberOfRuns: 3,
      
      // 浏览器设置
      settings: {
        // 设备模拟
        emulatedFormFactor: 'desktop',
        
        // 网络节流
        throttling: {
          rttMs: 40,
          throughputKbps: 10240,
          cpuSlowdownMultiplier: 1,
          requestLatencyMs: 0,
          downloadThroughputKbps: 0,
          uploadThroughputKbps: 0
        },
        
        // 屏幕模拟
        screenEmulation: {
          mobile: false,
          width: 1920,
          height: 1080,
          deviceScaleFactor: 1,
          disabled: false
        },
        
        // 其他设置
        disableStorageReset: false,
        clearStorageTypes: ['cookies', 'localstorage', 'sessionstorage', 'websql', 'indexeddb'],
        skipAudits: null,
        onlyAudits: null,
        onlyCategories: null,
        skipCategories: null
      }
    },
    
    // ==========================================
    // 断言配置 - 性能阈值
    // ==========================================
    assert: {
      assertions: {
        // 性能指标
        'categories:performance': ['error', { minScore: 0.8 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['error', { minScore: 0.9 }],
        'categories:seo': ['error', { minScore: 0.9 }],
        'categories:pwa': ['warn', { minScore: 0.6 }],
        
        // Core Web Vitals
        'audits:first-contentful-paint': ['warn', { maxNumericValue: 2000 }],
        'audits:largest-contentful-paint': ['error', { maxNumericValue: 2500 }],
        'audits:cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
        'audits:total-blocking-time': ['warn', { maxNumericValue: 300 }],
        'audits:speed-index': ['warn', { maxNumericValue: 3000 }],
        
        // 其他重要指标
        'audits:interactive': ['warn', { maxNumericValue: 3000 }],
        'audits:max-potential-fid': ['warn', { maxNumericValue: 130 }],
        'audits:server-response-time': ['warn', { maxNumericValue: 600 }],
        
        // 资源优化
        'audits:unused-css-rules': ['warn', { maxNumericValue: 20000 }],
        'audits:unused-javascript': ['warn', { maxNumericValue: 20000 }],
        'audits:modern-image-formats': ['warn', { minScore: 0.8 }],
        'audits:offscreen-images': ['warn', { minScore: 0.8 }],
        'audits:render-blocking-resources': ['warn', { minScore: 0.8 }],
        
        // 安全性
        'audits:is-on-https': ['error', { minScore: 1 }],
        'audits:external-anchors-use-rel-noopener': ['error', { minScore: 1 }],
        
        // 可访问性
        'audits:color-contrast': ['error', { minScore: 1 }],
        'audits:image-alt': ['error', { minScore: 1 }],
        'audits:label': ['error', { minScore: 1 }],
        'audits:link-name': ['error', { minScore: 1 }],
        
        // SEO
        'audits:document-title': ['error', { minScore: 1 }],
        'audits:meta-description': ['error', { minScore: 1 }],
        'audits:http-status-code': ['error', { minScore: 1 }],
        'audits:crawlable-anchors': ['error', { minScore: 1 }]
      }
    },
    
    // ==========================================
    // 上传配置
    // ==========================================
    upload: {
      target: 'filesystem',
      outputDir: './coverage/lighthouse-reports',
      reportFilenamePattern: '%%PATHNAME%%-%%DATETIME%%-report.%%EXTENSION%%'
    },
    
    // ==========================================
    // 服务器配置
    // ==========================================
    server: {
      command: 'npm run start',
      url: 'http://localhost:3000',
      timeout: 120000
    }
  },
  
  // ==========================================
  // 移动设备配置
  // ==========================================
  mobile: {
    collect: {
      settings: {
        emulatedFormFactor: 'mobile',
        throttling: {
          rttMs: 150,
          throughputKbps: 1638.4,
          cpuSlowdownMultiplier: 4,
          requestLatencyMs: 0,
          downloadThroughputKbps: 0,
          uploadThroughputKbps: 0
        },
        screenEmulation: {
          mobile: true,
          width: 375,
          height: 667,
          deviceScaleFactor: 2,
          disabled: false
        }
      }
    },
    assert: {
      assertions: {
        // 移动设备性能要求相对宽松
        'categories:performance': ['error', { minScore: 0.7 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['error', { minScore: 0.9 }],
        'categories:seo': ['error', { minScore: 0.9 }],
        'categories:pwa': ['warn', { minScore: 0.8 }],
        
        // 移动设备 Core Web Vitals
        'audits:first-contentful-paint': ['warn', { maxNumericValue: 3000 }],
        'audits:largest-contentful-paint': ['error', { maxNumericValue: 4000 }],
        'audits:cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
        'audits:total-blocking-time': ['warn', { maxNumericValue: 600 }],
        'audits:speed-index': ['warn', { maxNumericValue: 5000 }]
      }
    }
  },
  
  // ==========================================
  // 自定义审计
  // ==========================================
  customAudits: [
    // 自定义性能审计
    {
      id: 'hege-custom-performance',
      title: '荷阁科技自定义性能检查',
      description: '检查特定的性能指标',
      requiredArtifacts: ['traces'],
      audit: function(artifacts, context) {
        // 自定义审计逻辑
        return {
          score: 1,
          displayValue: '通过',
          details: {
            type: 'table',
            headings: [
              { key: 'metric', itemType: 'text', text: '指标' },
              { key: 'value', itemType: 'text', text: '值' },
              { key: 'status', itemType: 'text', text: '状态' }
            ],
            items: [
              { metric: '自定义指标1', value: '100ms', status: '✅' },
              { metric: '自定义指标2', value: '200ms', status: '✅' }
            ]
          }
        }
      }
    }
  ],
  
  // ==========================================
  // 报告配置
  // ==========================================
  reporting: {
    // 报告格式
    formats: ['html', 'json', 'csv'],
    
    // 输出目录
    outputDir: './coverage/lighthouse-reports',
    
    // 报告模板
    template: {
      title: '上海荷阁科技 - 性能测试报告',
      description: '企业官网性能、可访问性、SEO 等指标测试报告',
      logo: './public/hege-logo.svg'
    },
    
    // 历史趋势
    trends: {
      enabled: true,
      maxReports: 50,
      compareWith: 'previous'
    }
  },
  
  // ==========================================
  // 通知配置
  // ==========================================
  notifications: {
    // 启用通知
    enabled: true,
    
    // 通知条件
    conditions: {
      performance: { threshold: 0.8, operator: 'lt' },
      accessibility: { threshold: 0.9, operator: 'lt' },
      'best-practices': { threshold: 0.9, operator: 'lt' },
      seo: { threshold: 0.9, operator: 'lt' }
    },
    
    // 通知渠道
    channels: {
      email: {
        enabled: true,
        recipients: ['<EMAIL>', '<EMAIL>']
      },
      slack: {
        enabled: true,
        webhook: process.env.SLACK_PERFORMANCE_WEBHOOK,
        channel: '#performance-alerts'
      }
    }
  }
}
