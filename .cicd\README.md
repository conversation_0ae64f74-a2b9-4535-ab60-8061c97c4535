# 🚀 CI/CD 配置目录

本目录包含了项目的所有 CI/CD 相关配置文件，统一管理以保持项目结构整洁。

## 📁 目录结构

```
.cicd/
├── README.md                    # 本文件 - CI/CD 配置说明
├── gitlab-ci/                   # GitLab CI/CD 配置
│   ├── .gitlab-ci.yml          # 主 CI/CD 配置文件
│   ├── stages/                 # 分阶段配置文件
│   │   ├── prepare.yml         # 准备阶段
│   │   ├── test.yml           # 测试阶段
│   │   ├── security.yml       # 安全扫描阶段
│   │   ├── build.yml          # 构建阶段
│   │   └── deploy.yml         # 部署阶段
│   └── variables/              # 环境变量配置
│       ├── common.yml         # 通用变量
│       ├── staging.yml        # 测试环境变量
│       └── production.yml     # 生产环境变量
├── docker/                     # Docker 容器化配置
│   ├── Dockerfile             # 应用容器镜像
│   ├── Dockerfile.nginx       # Nginx 反向代理镜像
│   ├── docker-compose.yml     # 本地开发环境
│   ├── docker-compose.prod.yml # 生产环境
│   ├── scripts/               # Docker 相关脚本
│   │   ├── build.sh          # 镜像构建脚本
│   │   ├── push.sh           # 镜像推送脚本
│   │   └── health-check.sh   # 健康检查脚本
│   └── nginx/                 # Nginx 配置
│       ├── nginx.conf        # 主配置
│       └── default.conf      # 站点配置
├── security/                   # 安全扫描配置
│   ├── sast/                  # 静态应用安全测试
│   │   ├── .eslintrc.security.js
│   │   └── sonarqube.properties
│   ├── dependency/            # 依赖漏洞扫描
│   │   ├── .npmauditrc
│   │   └── dependency-check.yml
│   ├── container/             # 容器安全扫描
│   │   ├── trivy.yml
│   │   └── hadolint.yml
│   └── reports/               # 安全报告模板
│       └── security-report.html
├── testing/                    # 测试配置
│   ├── jest/                  # Jest 单元测试
│   │   ├── jest.config.js
│   │   ├── jest.setup.js
│   │   └── coverage.config.js
│   ├── e2e/                   # 端到端测试
│   │   ├── playwright.config.ts
│   │   ├── cypress.config.js
│   │   └── tests/
│   ├── performance/           # 性能测试
│   │   ├── lighthouse.config.js
│   │   └── k6.config.js
│   └── reports/               # 测试报告模板
│       ├── test-report.html
│       └── coverage-report.html
├── deploy/                     # 部署配置
│   ├── scripts/               # 部署脚本
│   │   ├── deploy-staging.sh
│   │   ├── deploy-production.sh
│   │   ├── rollback.sh
│   │   └── health-check.sh
│   ├── k8s/                   # Kubernetes 配置
│   │   ├── namespace.yml
│   │   ├── deployment.yml
│   │   ├── service.yml
│   │   └── ingress.yml
│   ├── compose/               # Docker Compose 部署
│   │   ├── staging.yml
│   │   └── production.yml
│   └── monitoring/            # 监控配置
│       ├── prometheus.yml
│       ├── grafana/
│       └── alerts.yml
├── windows/                    # Windows 环境配置
│   ├── runner/                # GitLab Runner 配置
│   │   ├── config.toml
│   │   ├── install.ps1
│   │   └── register.ps1
│   ├── scripts/               # Windows 脚本
│   │   ├── setup.ps1
│   │   ├── build.ps1
│   │   └── deploy.ps1
│   └── docker/                # Windows Docker 配置
│       └── Dockerfile.windows
└── docs/                      # 文档
    ├── setup-guide.md         # 设置指南
    ├── troubleshooting.md     # 故障排除
    ├── best-practices.md      # 最佳实践
    └── examples/              # 示例配置
        ├── simple-pipeline.yml
        └── advanced-pipeline.yml
```

## 🎯 使用说明

### 1. 快速开始
```bash
# 复制主 CI/CD 配置到项目根目录
cp .cicd/gitlab-ci/.gitlab-ci.yml .gitlab-ci.yml

# 根据需要调整环境变量
cp .cicd/gitlab-ci/variables/common.yml.example .cicd/gitlab-ci/variables/common.yml
```

### 2. 环境配置
- 开发环境：使用 `docker-compose.yml`
- 测试环境：使用 `docker-compose.staging.yml`
- 生产环境：使用 `docker-compose.prod.yml`

### 3. 安全扫描
所有安全扫描工具的配置都在 `security/` 目录下，包括：
- 静态代码分析 (SAST)
- 依赖漏洞扫描
- 容器镜像安全扫描

### 4. 测试配置
测试相关配置在 `testing/` 目录下：
- 单元测试：Jest 配置
- 端到端测试：Playwright/Cypress 配置
- 性能测试：Lighthouse 配置

## 📋 配置清单

- [ ] 复制并配置 `.gitlab-ci.yml`
- [ ] 设置环境变量
- [ ] 配置 Docker 镜像仓库
- [ ] 设置安全扫描工具
- [ ] 配置测试环境
- [ ] 设置部署目标环境
- [ ] 配置监控和告警

## 🔗 相关文档

- [设置指南](docs/setup-guide.md)
- [故障排除](docs/troubleshooting.md)
- [最佳实践](docs/best-practices.md)

---
© 2024 上海荷阁科技有限公司 - CI/CD 配置
