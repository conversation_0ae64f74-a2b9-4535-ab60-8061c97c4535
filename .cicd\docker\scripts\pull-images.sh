#!/bin/bash
# 🚀 上海荷阁科技 - Docker 镜像预拉取脚本
# 解决网络连接问题，预先拉取所需镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 镜像列表 - 使用官方镜像名（通过镜像源加速）
IMAGES=(
    "postgres:15-alpine"
    "redis:7-alpine"
    "nginx:1.25-alpine"
    "node:18-alpine"
    "prom/prometheus:latest"
    "grafana/grafana:latest"
)

# 拉取单个镜像
pull_image() {
    local image=$1
    local max_retries=3
    local retry_count=0
    
    log_info "正在拉取镜像: $image"
    
    while [ $retry_count -lt $max_retries ]; do
        if docker pull "$image"; then
            log_success "✅ 镜像拉取成功: $image"
            return 0
        else
            retry_count=$((retry_count + 1))
            log_warning "⚠️  镜像拉取失败，重试 $retry_count/$max_retries: $image"
            sleep 5
        fi
    done
    
    log_error "❌ 镜像拉取失败: $image"
    return 1
}

# 拉取所有镜像
pull_all_images() {
    log_info "开始拉取所有必需的 Docker 镜像..."

    for image in "${IMAGES[@]}"; do
        pull_image "$image"
    done
}

# 检查 Docker 是否运行
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log_error "❌ Docker 未运行或无法访问"
        log_info "请确保 Docker 服务已启动"
        exit 1
    fi
    
    log_success "✅ Docker 服务正常运行"
}

# 显示镜像信息
show_images() {
    log_info "当前已拉取的镜像:"
    echo ""
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | head -20
}

# 清理悬空镜像
cleanup_images() {
    log_info "清理悬空镜像..."
    docker image prune -f
    log_success "✅ 清理完成"
}

# 主函数
main() {
    log_info "🚀 开始 Docker 镜像预拉取流程"
    
    # 检查 Docker
    check_docker
    
    # 拉取镜像
    pull_all_images
    
    # 显示镜像信息
    show_images
    
    # 清理悬空镜像
    cleanup_images
    
    log_success "🎉 镜像预拉取完成！"
    log_info "现在可以运行: docker-compose -f .cicd/docker/docker-compose.yml up -d"
}

# 帮助信息
show_help() {
    cat << EOF
🚀 上海荷阁科技 Docker 镜像预拉取脚本

用法: $0 [选项]

选项:
    -h, --help     显示此帮助信息
    -c, --cleanup  仅清理悬空镜像
    -s, --show     仅显示当前镜像

示例:
    $0              # 拉取所有镜像
    $0 -c           # 清理悬空镜像
    $0 -s           # 显示当前镜像

EOF
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -c|--cleanup)
        cleanup_images
        exit 0
        ;;
    -s|--show)
        show_images
        exit 0
        ;;
    "")
        main
        ;;
    *)
        log_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
