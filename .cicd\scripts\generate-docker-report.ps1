# Shanghai Hege Technology - Docker Build Report Generation Script
# Generate JUnit format Docker build report

param(
    [string]$ImageName = "hege-tech-web",
    [string]$ImageTag = "latest",
    [string]$OutputFile = "docker-build-report.xml"
)

$ErrorActionPreference = "Continue"

Write-Host "Generating Docker build report..." -ForegroundColor Green

# Check if image exists
$imageExists = docker images --format "{{.Repository}}:{{.Tag}}" | Select-String "${ImageName}:${ImageTag}"

if ($imageExists) {
    $testResult = "passed"
    $failureMessage = ""
    Write-Host "Docker image build successful" -ForegroundColor Green
} else {
    $testResult = "failed"
    $failureMessage = "Docker image build failed or image does not exist"
    Write-Host "Docker image build failed" -ForegroundColor Red
}

# Get image information
try {
    $imageInspectOutput = docker inspect "${ImageName}:${ImageTag}" 2>$null
    if ($imageInspectOutput) {
        $imageInfo = $imageInspectOutput | ConvertFrom-Json
        $imageSize = docker images --format "{{.Size}}" --filter "reference=${ImageName}:${ImageTag}"
        $createdTime = $imageInfo[0].Created
        $architecture = $imageInfo[0].Architecture
        $os = $imageInfo[0].Os
    } else {
        throw "Image not found"
    }
} catch {
    $imageSize = "Unknown"
    $createdTime = "Unknown"
    $architecture = "Unknown"
    $os = "Unknown"
}

# Generate JUnit XML report
$junitXml = @"
<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Docker Build Report" tests="1" failures="$(if($testResult -eq 'failed'){1}else{0})" time="0">
  <testsuite name="Docker Image Build" tests="1" failures="$(if($testResult -eq 'failed'){1}else{0})" time="0">
    <testcase name="Build ${ImageName}:${ImageTag}" classname="DockerBuild" time="0">
$(if($testResult -eq 'failed'){
"      <failure message=`"$failureMessage`">$failureMessage</failure>"
}else{
"      <system-out>
Image Name: ${ImageName}:${ImageTag}
Image Size: $imageSize
Created Time: $createdTime
Architecture: $architecture
Operating System: $os
Build Status: Success
      </system-out>"
})
    </testcase>
  </testsuite>
</testsuites>
"@

# Write report file
$junitXml | Out-File -FilePath $OutputFile -Encoding UTF8

Write-Host "Docker build report generated: $OutputFile" -ForegroundColor Green
Write-Host "Report content preview:" -ForegroundColor Yellow
Get-Content $OutputFile | Select-Object -First 10

# Display image list
Write-Host "`nCurrent Docker images list:" -ForegroundColor Yellow
docker images $ImageName --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
