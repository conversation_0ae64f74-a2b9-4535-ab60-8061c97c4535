// 🚀 上海荷阁科技 - Playwright 全局设置
// 在所有测试开始前执行

import { chromium, FullConfig } from "@playwright/test";

async function globalSetup(config: FullConfig) {
  console.log("🚀 开始 E2E 测试全局设置...");

  const { baseURL } = config.projects[0].use;

  // 启动浏览器进行预热
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 等待应用启动
    console.log(`⏳ 等待应用启动: ${baseURL}`);
    await page.goto(baseURL!);
    await page.waitForLoadState("networkidle");

    // 预热关键页面
    console.log("🔥 预热关键页面...");
    const pages = ["/", "/about", "/services", "/contact", "/cloud-services"];

    for (const path of pages) {
      try {
        await page.goto(`${baseURL}${path}`);
        await page.waitForLoadState("networkidle", { timeout: 10000 });
        console.log(`✅ 预热页面: ${path}`);
      } catch (error) {
        console.warn(`⚠️ 预热页面失败: ${path}`, error);
      }
    }

    // 检查 API 健康状态
    console.log("🏥 检查 API 健康状态...");
    try {
      const response = await page.request.get(`${baseURL}/api/health`);
      if (response.ok()) {
        console.log("✅ API 健康检查通过");
      } else {
        console.warn("⚠️ API 健康检查失败");
      }
    } catch (error) {
      console.warn("⚠️ API 健康检查异常:", error);
    }

    console.log("✅ E2E 测试全局设置完成");
  } catch (error) {
    console.error("❌ E2E 测试全局设置失败:", error);
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }
}

export default globalSetup;
