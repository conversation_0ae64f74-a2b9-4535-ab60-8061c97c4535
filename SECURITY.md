# 🛡️ 上海荷阁科技网站安全防护文档

## 概述

本文档详细说明了为上海荷阁科技网站实施的全面网络安全防护措施，旨在防止各种网络攻击和安全威胁。

## 🔒 已实施的安全措施

### 1. HTTP安全头部配置

#### 内容安全策略 (CSP)

- **功能**: 防止XSS攻击和代码注入
- **配置**: 严格的内容源控制，禁止内联脚本执行
- **文件**: `next.config.security.js`

#### 安全头部列表

- `X-Frame-Options: DENY` - 防止点击劫持
- `X-Content-Type-Options: nosniff` - 防止MIME类型嗅探
- `X-XSS-Protection: 1; mode=block` - 启用XSS过滤器
- `Referrer-Policy: strict-origin-when-cross-origin` - 控制引用信息
- `Strict-Transport-Security` - 强制HTTPS连接
- `Permissions-Policy` - 限制浏览器功能访问

### 2. 输入验证和过滤

#### 实施位置

- **联系表单API**: `/api/contact`
- **访客统计API**: `/api/visitor-stats`
- **文件上传API**: `/api/upload`

#### 验证措施

- HTML实体编码防止XSS
- 输入长度限制
- 邮箱和电话格式验证
- URL格式验证
- 垃圾信息检测
- 恶意内容过滤

### 3. 速率限制 (Rate Limiting)

#### 限制策略

- **联系表单**: 每IP每小时5次提交
- **访客统计**: 每IP每分钟60次请求
- **文件上传**: 每IP每小时10次上传
- **安全日志**: 每IP每分钟10次查询

#### 实现方式

- 基于IP地址的内存缓存
- 滑动窗口算法
- 自动清理过期记录

### 4. 文件上传安全

#### 安全检查

- 文件类型验证（MIME类型）
- 文件扩展名检查
- 文件大小限制（10MB）
- 魔数验证（文件头检查）
- 恶意内容扫描
- 安全文件名生成

#### 支持的文件类型

- 图片: JPEG, PNG, WebP
- 文档: PDF

### 5. API安全防护

#### 中间件保护

- IP黑名单检查
- 可疑User-Agent检测
- 恶意路径访问拦截
- CSRF保护
- Origin验证

#### API认证

- API密钥验证
- IP白名单控制
- 管理员权限检查

### 6. 安全监控和日志

#### 监控事件类型

- `login_attempt` - 登录尝试
- `failed_login` - 登录失败
- `suspicious_activity` - 可疑活动
- `rate_limit_exceeded` - 速率限制超出
- `file_upload` - 文件上传
- `xss_attempt` - XSS攻击尝试

#### 日志功能

- 实时安全事件记录
- 详细的攻击信息收集
- 自动日志轮转和清理
- 管理员日志查询接口

### 7. 环境变量安全

#### 敏感信息保护

- 数据库连接字符串加密
- API密钥环境变量化
- 会话密钥随机生成
- 生产环境配置分离

## 🚨 威胁检测

### 自动检测的威胁类型

#### 1. XSS攻击

- 脚本标签检测
- 事件处理器检测
- JavaScript协议检测

#### 2. SQL注入

- SQL关键字检测
- 联合查询检测
- 数据库操作检测

#### 3. 路径遍历

- 相对路径检测
- 编码路径检测
- 系统文件访问检测

#### 4. 恶意爬虫

- 可疑User-Agent检测
- 高频访问检测
- 允许的搜索引擎白名单

#### 5. 垃圾信息

- 关键词过滤
- 链接检测
- 信用卡号码检测

## 🔧 配置文件

### 主要配置文件

- `next.config.security.js` - Next.js安全配置
- `src/middleware.ts` - 请求中间件
- `src/lib/security.ts` - 安全工具函数
- `src/lib/security-config.ts` - 安全配置管理
- `.env.example` - 环境变量模板

### 环境变量配置

```bash
# 安全配置
NEXTAUTH_SECRET=your-super-secret-key
JWT_SECRET=your-jwt-secret-key
BCRYPT_ROUNDS=12
API_SECRET_KEY=your-api-secret-key

# 功能开关
ENABLE_SECURITY_LOGS=true
ENABLE_RATE_LIMITING=true
ENABLE_CSRF_PROTECTION=true
```

## 🧪 安全测试

### 测试页面

访问 `/security-test` 页面进行安全功能测试

### 测试项目

1. **联系表单安全测试**
   - 正常提交测试
   - XSS攻击测试
   - 垃圾信息测试
   - 超长内容测试

2. **速率限制测试**
   - 高频请求测试
   - 限制触发测试

3. **安全日志查看**
   - 实时日志监控
   - 事件统计分析

## 📊 安全监控

### 日志查询API

```bash
# 获取安全日志
GET /api/security/logs
Headers: X-API-Key: your-api-secret-key

# 查询参数
?limit=100          # 限制返回数量
?type=suspicious_activity  # 按类型过滤
?since=2024-01-01   # 按时间过滤
```

### 监控指标

- 总安全事件数
- 各类型事件分布
- 最近活动记录
- IP访问统计

## 🚀 部署建议

### 生产环境配置

1. **启用HTTPS**
   - 配置SSL证书
   - 强制HTTPS重定向
   - 启用HSTS

2. **数据库安全**
   - 使用连接池
   - 启用SSL连接
   - 定期备份

3. **服务器安全**
   - 防火墙配置
   - 定期安全更新
   - 访问日志监控

### 维护建议

1. **定期安全审计**
   - 代码安全扫描
   - 依赖漏洞检查
   - 配置安全评估

2. **日志管理**
   - 定期日志分析
   - 异常事件告警
   - 日志长期存储

3. **应急响应**
   - 安全事件响应流程
   - 备份恢复计划
   - 联系人信息更新

## 📞 安全联系方式

如发现安全问题，请联系：

- 邮箱: <EMAIL>
- 电话: 18221165813

---

**最后更新**: 2024年7月4日  
**版本**: V1.0.3-security  
**负责人**: 上海荷阁科技安全团队
