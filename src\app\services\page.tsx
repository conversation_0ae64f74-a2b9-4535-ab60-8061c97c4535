import { <PERSON>pu, Monitor, Cloud, Users } from "lucide-react";
import Link from "next/link";
import Navigation from "@/components/Navigation";

export default function ServicesPage() {
  return (
    <div className="min-h-screen">
      <Navigation />

      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">服务项目</h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              专业的技术服务，为您的业务发展提供强有力的支持
            </p>
          </div>
        </div>
      </section>

      {/* 服务详情 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {/* 3D引擎技术 */}
            <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-6">
                <Cpu className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                3D引擎技术
              </h3>
              <p className="text-gray-600 mb-6">
                基于先进的3D渲染技术，为汽车智能座舱提供高性能的图形处理解决方案。
                支持复杂的3D场景渲染、实时光影效果和流畅的用户交互体验。
              </p>
              <ul className="space-y-2 text-gray-600">
                <li>• 高性能3D渲染引擎</li>
                <li>• 实时光影和材质系统</li>
                <li>• 跨平台兼容性</li>
                <li>• 优化的内存管理</li>
              </ul>
            </div>

            {/* 智能座舱解决方案 */}
            <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-6">
                <Monitor className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                智能座舱解决方案
              </h3>
              <p className="text-gray-600 mb-6">
                提供完整的智能座舱系统，包括人机交互界面、多媒体系统和车载信息娱乐系统。
                打造沉浸式的驾驶体验。
              </p>
              <ul className="space-y-2 text-gray-600">
                <li>• 3D HMI工具链</li>
                <li>• 一镜到底解决方案</li>
                <li>• 自动泊车高精地图渲染</li>
                <li>• VPA全3D方案</li>
              </ul>
            </div>

            {/* 云服务代理 */}
            <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-6">
                <Cloud className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                云服务代理
              </h3>
              <p className="text-gray-600 mb-6">
                作为腾讯云官方合作伙伴，为企业提供专业的云计算服务和技术支持。
                帮助企业实现数字化转型。
              </p>
              <ul className="space-y-2 text-gray-600">
                <li>• 腾讯云产品代理</li>
                <li>• 云架构设计咨询</li>
                <li>• 云迁移服务</li>
                <li>• 技术支持与运维</li>
              </ul>
            </div>

            {/* 虚拟数字人解决方案 */}
            <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
              <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-6">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                虚拟数字人解决方案
              </h3>
              <p className="text-gray-600 mb-6">
                基于AI技术的虚拟数字人系统，为企业提供智能客服、虚拟主播等创新应用。
                提升用户体验和服务效率。
              </p>
              <ul className="space-y-2 text-gray-600">
                <li>• AI驱动的虚拟形象</li>
                <li>• 自然语言处理</li>
                <li>• 实时语音合成</li>
                <li>• 多场景应用支持</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA区域 */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            准备开始您的项目？
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            联系我们的专业团队，获取定制化的技术解决方案
          </p>
          <Link
            href="/contact"
            className="bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 inline-block"
          >
            联系我们
          </Link>
        </div>
      </section>
    </div>
  );
}
