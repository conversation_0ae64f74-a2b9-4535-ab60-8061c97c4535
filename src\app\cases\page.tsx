"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import Navigation from "@/components/Navigation";
import {
  Calendar,
  Users,
  Award,
  TrendingUp,
  Zap,
  Shield,
  Smartphone,
  RefreshCw,
  ArrowRight,
  ExternalLink,
  Search,
  Filter,
  ChevronDown,
} from "lucide-react";

interface CaseData {
  id: number;
  title: string;
  client: string;
  category: string;
  description: string;
  challenge: string;
  solution: string;
  results: string[];
  technologies: string[];
  duration: string;
  teamSize: string;
  image: string;
  achievements: {
    metric: string;
    value: string;
    description: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

interface CasesPageData {
  cases: CaseData[];
  statistics: {
    totalCases: number;
    totalClients: number;
    averageSatisfaction: string;
    totalExperts: string;
    lastUpdated: string;
  };
}

export default function CasesPage() {
  const [casesData, setCasesData] = useState<CaseData[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCase, setSelectedCase] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("全部");
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [dataSource, setDataSource] = useState<string>("");

  // 从API获取成功案例数据
  const fetchCasesData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 首先尝试从荷阁科技官网获取数据
      let response = await fetch("/api/hege-cases");
      let result = await response.json();

      // 如果官网数据获取失败，使用备用数据源
      if (!result.success || result.data.cases.length === 0) {
        console.log("官网数据获取失败，使用备用数据源");
        response = await fetch("/api/success-cases");
        result = await response.json();
      }

      if (result.success) {
        setCasesData(result.data.cases);
        setStatistics(result.data.statistics);
        setDataSource(result.data.source || "内部数据");
      } else {
        setError(result.message || "获取数据失败");
      }
    } catch (err) {
      setError("网络请求失败，请稍后重试");
      console.error("获取成功案例数据失败:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCasesData();
  }, []);

  // 获取所有类别
  const categories = [
    "全部",
    ...Array.from(new Set(casesData.map((c) => c.category))),
  ];

  // 过滤案例
  const filteredCases = casesData.filter((caseItem) => {
    const matchesSearch =
      caseItem.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      caseItem.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
      caseItem.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory =
      selectedCategory === "全部" || caseItem.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // 根据类别映射图标和渐变色
  const getCategoryIcon = (category: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      智能座舱: <Smartphone className="w-6 h-6" />,
      云服务: <TrendingUp className="w-6 h-6" />,
      人工智能: <Zap className="w-6 h-6" />,
      网络安全: <Shield className="w-6 h-6" />,
      数字化转型: <Award className="w-6 h-6" />,
      大数据: <Users className="w-6 h-6" />,
    };
    return iconMap[category] || <Award className="w-6 h-6" />;
  };

  const getCategoryGradient = (category: string) => {
    const gradientMap: { [key: string]: string } = {
      智能座舱: "from-blue-500 to-purple-600",
      云服务: "from-green-500 to-blue-600",
      人工智能: "from-purple-500 to-pink-600",
      网络安全: "from-red-500 to-orange-600",
      数字化转型: "from-indigo-500 to-blue-600",
      大数据: "from-teal-500 to-green-600",
    };
    return gradientMap[category] || "from-gray-500 to-gray-600";
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        {/* 页面头部 */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">成功案例</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              荷阁科技为汽车行业客户提供专业的技术解决方案，助力企业数字化转型
            </p>
          </div>
        </div>

        {/* 加载状态 */}
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <RefreshCw className="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4" />
            <p className="text-gray-600 text-lg">正在加载成功案例...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        {/* 页面头部 */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">成功案例</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              荷阁科技为汽车行业客户提供专业的技术解决方案，助力企业数字化转型
            </p>
          </div>
        </div>

        {/* 错误状态 */}
        <div className="flex items-center justify-center py-20">
          <div className="bg-red-50 border border-red-200 rounded-lg p-8 max-w-md mx-auto text-center">
            <div className="text-red-600 mb-4">⚠️</div>
            <h3 className="text-red-800 font-medium text-lg mb-2">
              数据加载失败
            </h3>
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={fetchCasesData}
              className="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* 导航栏 */}
      <Navigation />

      {/* 页面头部 */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <h1 className="text-4xl md:text-5xl font-bold">成功案例</h1>
              <button
                onClick={fetchCasesData}
                disabled={loading}
                className="ml-4 p-2 text-white/80 hover:text-white transition-colors disabled:opacity-50"
                title="刷新数据"
              >
                <RefreshCw
                  className={`w-6 h-6 ${loading ? "animate-spin" : ""}`}
                />
              </button>
            </div>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto mb-4">
              荷阁科技为汽车行业客户提供专业的技术解决方案，助力企业数字化转型
            </p>
            {dataSource && (
              <div className="text-sm text-blue-200 mb-8">
                数据来源:{" "}
                {dataSource === "hege-tech.cn" ? "荷阁科技官网" : dataSource}
                {statistics?.lastUpdated && (
                  <span className="ml-4">
                    更新时间:{" "}
                    {new Date(statistics.lastUpdated).toLocaleString("zh-CN")}
                  </span>
                )}
              </div>
            )}

            {/* 统计数据 */}
            {statistics && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">
                    {statistics.totalCases}+
                  </div>
                  <div className="text-blue-200">成功案例</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">
                    {statistics.totalClients}+
                  </div>
                  <div className="text-blue-200">合作客户</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">
                    {statistics.averageSatisfaction}
                  </div>
                  <div className="text-blue-200">客户满意度</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">
                    {statistics.totalExperts}
                  </div>
                  <div className="text-blue-200">专业团队</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          {/* 搜索框 */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="搜索案例标题、客户名称或描述..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* 类别筛选 */}
          <div className="relative">
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="flex items-center gap-2 px-6 py-3 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Filter className="w-5 h-5" />
              {selectedCategory}
              <ChevronDown className="w-4 h-4" />
            </button>

            {isFilterOpen && (
              <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => {
                      setSelectedCategory(category);
                      setIsFilterOpen(false);
                    }}
                    className={`w-full text-left px-4 py-2 hover:bg-blue-50 transition-colors ${
                      selectedCategory === category
                        ? "bg-blue-50 text-blue-600"
                        : "text-gray-700"
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 案例结果统计 */}
        <div className="mb-8">
          <p className="text-gray-600">
            找到{" "}
            <span className="font-semibold text-blue-600">
              {filteredCases.length}
            </span>{" "}
            个相关案例
          </p>
        </div>

        {/* 案例网格 */}
        {filteredCases.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredCases.map((caseItem, index) => (
              <div
                key={caseItem.id}
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer group"
                onClick={() =>
                  setSelectedCase(
                    selectedCase === caseItem.id ? null : caseItem.id,
                  )
                }
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* 案例图片 */}
                <div
                  className={`h-48 bg-gradient-to-br ${getCategoryGradient(caseItem.category)} relative overflow-hidden`}
                >
                  <div className="absolute inset-0 bg-black/20"></div>
                  <div className="absolute top-4 left-4 bg-white/20 backdrop-blur-sm rounded-lg p-2">
                    {getCategoryIcon(caseItem.category)}
                  </div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <div className="text-sm opacity-90">
                      {caseItem.category}
                    </div>
                    <div className="text-lg font-semibold">
                      {caseItem.client}
                    </div>
                  </div>
                  {caseItem.image && (
                    <Image
                      src={caseItem.image}
                      alt={caseItem.client}
                      fill
                      className="object-contain p-8 opacity-30 group-hover:opacity-50 transition-opacity"
                    />
                  )}
                </div>

                {/* 案例内容 */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                    {caseItem.title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {caseItem.description}
                  </p>

                  {/* 关键成果 */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    {caseItem.achievements
                      .slice(0, 2)
                      .map((achievement, idx) => (
                        <div key={idx} className="text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {achievement.value}
                          </div>
                          <div className="text-sm text-gray-500">
                            {achievement.metric}
                          </div>
                        </div>
                      ))}
                  </div>

                  {/* 项目信息 */}
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {caseItem.duration}
                    </div>
                    <div className="flex items-center">
                      <Users className="w-4 h-4 mr-1" />
                      {caseItem.teamSize}
                    </div>
                  </div>

                  {/* 查看详情按钮 */}
                  <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 px-4 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center group">
                    查看详情
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-20">
            <div className="text-gray-400 mb-4">
              <Search className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">
              未找到相关案例
            </h3>
            <p className="text-gray-500">请尝试调整搜索条件或筛选类别</p>
          </div>
        )}
      </div>

      {/* 详细信息弹窗 */}
      {selectedCase && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {(() => {
              const caseData = casesData.find((c) => c.id === selectedCase)!;
              return (
                <div>
                  {/* 弹窗头部 */}
                  <div
                    className={`bg-gradient-to-br ${getCategoryGradient(caseData.category)} text-white p-8 relative`}
                  >
                    <button
                      onClick={() => setSelectedCase(null)}
                      className="absolute top-4 right-4 text-white/80 hover:text-white transition-colors"
                    >
                      <svg
                        className="w-6 h-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>

                    <div className="flex items-start gap-6">
                      <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3">
                        {getCategoryIcon(caseData.category)}
                      </div>
                      <div className="flex-1">
                        <div className="text-sm opacity-90 mb-2">
                          {caseData.category}
                        </div>
                        <h2 className="text-3xl font-bold mb-2">
                          {caseData.title}
                        </h2>
                        <div className="text-xl opacity-90">
                          {caseData.client}
                        </div>
                      </div>
                      {caseData.image && (
                        <div className="w-24 h-24 relative">
                          <Image
                            src={caseData.image}
                            alt={caseData.client}
                            fill
                            className="object-contain opacity-80"
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 弹窗内容 */}
                  <div className="p-8">
                    {/* 项目概述 */}
                    <div className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        项目概述
                      </h3>
                      <p className="text-gray-700 leading-relaxed">
                        {caseData.description}
                      </p>
                    </div>

                    {/* 关键成果 */}
                    <div className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        关键成果
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {caseData.achievements.map((achievement, idx) => (
                          <div
                            key={idx}
                            className="text-center p-4 bg-gray-50 rounded-lg"
                          >
                            <div className="text-3xl font-bold text-blue-600 mb-2">
                              {achievement.value}
                            </div>
                            <div className="text-sm font-medium text-gray-900 mb-1">
                              {achievement.metric}
                            </div>
                            <div className="text-xs text-gray-500">
                              {achievement.description}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* 挑战与解决方案 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-4">
                          项目挑战
                        </h3>
                        <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg">
                          <p className="text-gray-700 leading-relaxed">
                            {caseData.challenge}
                          </p>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-4">
                          解决方案
                        </h3>
                        <div className="bg-green-50 border-l-4 border-green-400 p-4 rounded-r-lg">
                          <p className="text-gray-700 leading-relaxed">
                            {caseData.solution}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* 项目成果 */}
                    <div className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        项目成果
                      </h3>
                      <div className="space-y-2">
                        {caseData.results.map((result, idx) => (
                          <div key={idx} className="flex items-center">
                            <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                            <span className="text-gray-700">{result}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* 技术栈 */}
                    <div className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        技术栈
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {caseData.technologies.map((tech, idx) => (
                          <span
                            key={idx}
                            className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* 项目信息 */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6 p-6 bg-gray-50 rounded-lg">
                      <div className="text-center">
                        <Calendar className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                        <div className="text-sm text-gray-500 mb-1">
                          项目周期
                        </div>
                        <div className="font-semibold text-gray-900">
                          {caseData.duration}
                        </div>
                      </div>
                      <div className="text-center">
                        <Users className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                        <div className="text-sm text-gray-500 mb-1">
                          团队规模
                        </div>
                        <div className="font-semibold text-gray-900">
                          {caseData.teamSize}
                        </div>
                      </div>
                      <div className="text-center">
                        <Award className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                        <div className="text-sm text-gray-500 mb-1">
                          项目类型
                        </div>
                        <div className="font-semibold text-gray-900">
                          {caseData.category}
                        </div>
                      </div>
                      <div className="text-center">
                        <TrendingUp className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                        <div className="text-sm text-gray-500 mb-1">
                          项目状态
                        </div>
                        <div className="font-semibold text-green-600">
                          已完成
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
}
