'use client'

import Image from 'next/image'
import { useState, useEffect } from 'react'

// 客户数据配置 - 从 our customers 目录读取
const customersData = [
  // 汽车品牌客户
  {
    name: '比亚迪',
    logo: '/our customers/比亚迪.svg',
    website: 'https://www.byd.com',
    description: '新能源汽车技术伙伴',
    category: 'automotive'
  },
  {
    name: '吉利汽车',
    logo: '/our customers/吉利.svg',
    website: 'https://www.geely.com',
    description: '智能驾驶技术合作',
    category: 'automotive'
  },
  {
    name: '长安汽车',
    logo: '/our customers/长安.svg',
    website: 'https://www.changan.com.cn',
    description: '车载系统技术提供商',
    category: 'automotive'
  },
  {
    name: '长城汽车',
    logo: '/our customers/长城.svg',
    website: 'https://www.gwm.com.cn',
    description: '智能座舱解决方案',
    category: 'automotive'
  },
  {
    name: '一汽奔腾',
    logo: '/our customers/一汽奔腾.svg',
    website: 'https://www.bestune.com',
    description: '3D HMI技术合作',
    category: 'automotive'
  },
  {
    name: '东风汽车',
    logo: '/our customers/东风.svg',
    website: 'https://www.dfmc.com.cn',
    description: '智能网联汽车技术',
    category: 'automotive'
  },
  {
    name: '江淮汽车',
    logo: '/our customers/江淮.svg',
    website: 'https://www.jac.com.cn',
    description: '车载娱乐系统开发',
    category: 'automotive'
  },
  {
    name: '福特汽车',
    logo: '/our customers/福特.svg',
    website: 'https://www.ford.com.cn',
    description: '全球汽车技术合作',
    category: 'automotive'
  },
  {
    name: '红旗汽车',
    logo: '/our customers/红旗.svg',
    website: 'https://www.faw-hongqi.com',
    description: '豪华智能座舱',
    category: 'automotive'
  },
  {
    name: '岚图汽车',
    logo: '/our customers/岚图.svg',
    website: 'https://www.voyah.com',
    description: '高端电动车技术',
    category: 'automotive'
  },
  {
    name: '领克汽车',
    logo: '/our customers/领克.svg',
    website: 'https://www.lynkco.com.cn',
    description: '智能互联技术',
    category: 'automotive'
  },
  {
    name: '阿维塔',
    logo: '/our customers/阿维塔.svg',
    website: 'https://www.avatr.com',
    description: '智能电动汽车',
    category: 'automotive'
  },
  {
    name: '哪吒汽车',
    logo: '/our customers/哪吒.svg',
    website: 'https://www.neta.com.cn',
    description: '智能电动汽车',
    category: 'automotive'
  },
  {
    name: '极越汽车',
    logo: '/our customers/极越.svg',
    website: 'https://www.jiyue.com',
    description: '智能汽车技术',
    category: 'automotive'
  },
  {
    name: '高合汽车',
    logo: '/our customers/高合.svg',
    website: 'https://www.hiphi.com',
    description: '豪华智能电动车',
    category: 'automotive'
  },
  {
    name: '赛力斯',
    logo: '/our customers/赛力斯.svg',
    website: 'https://www.seres.cn',
    description: '智能电动汽车',
    category: 'automotive'
  },
  {
    name: '路特斯',
    logo: '/our customers/路特斯.svg',
    website: 'https://www.lotuscars.com.cn',
    description: '豪华跑车品牌',
    category: 'automotive'
  },
  // 技术合作伙伴
  {
    name: 'Unity',
    logo: '/our customers/unity.svg',
    website: 'https://unity.com',
    description: '3D引擎技术合作',
    category: 'technology'
  },
  {
    name: 'Unreal Engine',
    logo: '/our customers/unreal.svg',
    website: 'https://www.unrealengine.com',
    description: '虚幻引擎技术',
    category: 'technology'
  },
  {
    name: 'Qt',
    logo: '/our customers/qt.svg',
    website: 'https://www.qt.io',
    description: 'UI框架技术合作',
    category: 'technology'
  },
  {
    name: 'Cocos',
    logo: '/our customers/cocos.svg',
    website: 'https://www.cocos.com',
    description: '游戏引擎技术',
    category: 'technology'
  },
  {
    name: 'NXP',
    logo: '/our customers/NXP.svg',
    website: 'https://www.nxp.com',
    description: '芯片技术合作',
    category: 'technology'
  },
  {
    name: '博世',
    logo: '/our customers/博世.svg',
    website: 'https://www.bosch.com.cn',
    description: '汽车零部件技术',
    category: 'technology'
  },
  {
    name: 'TCL',
    logo: '/our customers/TCL.svg',
    website: 'https://www.tcl.com',
    description: '智能显示技术',
    category: 'technology'
  },
  {
    name: 'Kanzi',
    logo: '/our customers/kanzi.svg',
    website: 'https://www.rightware.com',
    description: 'HMI开发工具',
    category: 'technology'
  },
  {
    name: '中科创达',
    logo: '/our customers/中科创达.svg',
    website: 'https://www.thundersoft.com',
    description: '智能操作系统',
    category: 'technology'
  },
  {
    name: '四维图新',
    logo: '/our customers/四维图新.svg',
    website: 'https://www.navinfo.com',
    description: '地图导航技术',
    category: 'technology'
  }
]

export default function OurCustomers() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const element = document.getElementById('our-customers')
    if (element) {
      observer.observe(element)
    }

    return () => {
      if (element) {
        observer.unobserve(element)
      }
    }
  }, [])

  return (
    <section id="our-customers" className="py-20 bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-6">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-6">
            我们的客户
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            荷阁科技已为众多知名企业提供专业的技术解决方案，赢得了客户的信赖与好评
          </p>
        </div>

        {/* 汽车品牌客户 */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">汽车品牌客户</h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              为众多知名汽车品牌提供3D HMI、智能座舱等核心技术解决方案
            </p>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {customersData.filter(customer => customer.category === 'automotive').map((customer, index) => (
              <div
                key={customer.name}
                className={`group relative bg-white/90 backdrop-blur-sm rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-white/30 ${
                  isVisible ? 'animate-fade-in-up' : 'opacity-0'
                }`}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <a
                  href={customer.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block p-4 relative z-10"
                  title={customer.description}
                >
                  {/* LOGO容器 */}
                  <div className="flex items-center justify-center h-16 mb-3 relative">
                    <Image
                      src={customer.logo}
                      alt={customer.name}
                      width={120}
                      height={60}
                      className="h-12 w-auto object-contain group-hover:scale-110 transition-all duration-300 filter grayscale group-hover:grayscale-0"
                    />
                  </div>

                  {/* 客户名称 */}
                  <div className="text-center">
                    <h4 className="text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300">
                      {customer.name}
                    </h4>
                  </div>

                  {/* 悬停效果 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                </a>
              </div>
            ))}
          </div>
        </div>

        {/* 技术合作伙伴 */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">技术合作伙伴</h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              与全球领先的技术公司深度合作，共同推进3D引擎和智能技术发展
            </p>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {customersData.filter(customer => customer.category === 'technology').map((customer, index) => (
              <div
                key={customer.name}
                className={`group relative bg-white/90 backdrop-blur-sm rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-white/30 ${
                  isVisible ? 'animate-fade-in-up' : 'opacity-0'
                }`}
                style={{ animationDelay: `${(index + 12) * 50}ms` }}
              >
                <a
                  href={customer.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block p-4 relative z-10"
                  title={customer.description}
                >
                  {/* LOGO容器 */}
                  <div className="flex items-center justify-center h-16 mb-3 relative">
                    <Image
                      src={customer.logo}
                      alt={customer.name}
                      width={120}
                      height={60}
                      className="h-12 w-auto object-contain group-hover:scale-110 transition-all duration-300 filter grayscale group-hover:grayscale-0"
                    />
                  </div>

                  {/* 客户名称 */}
                  <div className="text-center">
                    <h4 className="text-sm font-medium text-gray-700 group-hover:text-purple-600 transition-colors duration-300">
                      {customer.name}
                    </h4>
                  </div>

                  {/* 悬停效果 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                </a>
              </div>
            ))}
          </div>
        </div>

        {/* 客户统计数据 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          <div className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-white/30 shadow-lg">
            <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">60+</div>
            <div className="text-gray-600 text-sm md:text-base">合作客户</div>
          </div>
          <div className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-white/30 shadow-lg">
            <div className="text-3xl md:text-4xl font-bold text-purple-600 mb-2">18+</div>
            <div className="text-gray-600 text-sm md:text-base">汽车品牌</div>
          </div>
          <div className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-white/30 shadow-lg">
            <div className="text-3xl md:text-4xl font-bold text-green-600 mb-2">50+</div>
            <div className="text-gray-600 text-sm md:text-base">量产项目</div>
          </div>
          <div className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-white/30 shadow-lg">
            <div className="text-3xl md:text-4xl font-bold text-orange-600 mb-2">98%</div>
            <div className="text-gray-600 text-sm md:text-base">客户满意度</div>
          </div>
        </div>

        {/* 客户评价 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">客户评价</h3>
            <div className="flex justify-center space-x-1 mb-4">
              {[...Array(5)].map((_, i) => (
                <svg key={i} className="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
            <blockquote className="text-lg text-gray-700 italic max-w-3xl mx-auto">
              "荷阁科技为我们提供了专业的3D HMI解决方案，技术实力强，服务态度好，是值得信赖的合作伙伴。"
            </blockquote>
            <cite className="text-gray-500 mt-4 block">—— 某知名汽车企业技术总监</cite>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-fade-in-up {
          animation: fade-in-up 0.6s ease-out forwards;
        }
      `}</style>
    </section>
  )
}
