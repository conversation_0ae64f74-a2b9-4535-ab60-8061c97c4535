# 📌 版本号管理规范 - 上海荷阁科技内部项目

## 🎯 格式说明

版本号格式如下：

```
V<主版本号>.<次版本号>.<修订号>[-阶段标识.迭代次数]
```

### 📋 示例说明

| 版本号 | 说明 | 使用场景 |
|--------|------|----------|
| `V0.1.0-alpha.1` | 初版原型第一轮 | 原型设计阶段 |
| `V0.5.0-beta.2` | 开发期测试第二轮 | 功能测试阶段 |
| `V0.9.0-rc.1` | 上线候选版本 | 发布前稳定版 |
| `V1.0.0` | 正式上线版本 | 生产环境 |
| `V1.0.1` | 小修复版本 | Bug修复 |

## 🚦 阶段说明

| 阶段标识 | 描述 | 示例 | 特点 |
|----------|------|------|------|
| `alpha` | 原型设计阶段，功能未完善 | `V0.1.0-alpha.1` | 内部开发测试 |
| `beta` | 功能基本完成，进入测试 | `V0.5.0-beta.2` | 功能测试阶段 |
| `rc` | Release Candidate，上线前稳定版本 | `V0.9.0-rc.1` | 发布候选版本 |
| 无 | 正式版本 | `V1.0.0` | 生产环境版本 |
| patch | Bug 修复、小功能更新 | `V1.0.1`, `V1.1.0` | 维护更新 |

## 🔁 推荐版本号演进流程

### 开发阶段演进
```
V0.1.0-alpha.1 → V0.3.0-beta.1 → V0.9.0-rc.1 → V1.0.0
```

### 后续版本演进
```
V1.0.1 → V1.1.0 → V2.0.0 → ...
```

## 🛠️ Git Tag 使用方式

每次发布一个版本，打一个 Git tag，Tag 名称使用上述格式（以 V 开头）。

### 示例命令：

```bash
# 创建标签
git tag V0.5.0-beta.2

# 推送标签到远程
git push origin V0.5.0-beta.2

# 查看所有标签
git tag -l "V*"

# 查看标签详情
git show V1.0.0
```

## 🤖 自动化版本管理

### 每日自动提交脚本

项目已配置 `daily-commit.sh` 脚本，自动按照版本规范管理：

```bash
# 手动执行
./daily-commit.sh

# 设置定时任务 (每天晚上11点)
crontab -e
0 23 * * * cd /path/to/HomePage && ./daily-commit.sh >> commit.log 2>&1
```

### 版本号生成策略

- **首次发布**: 从 `V1.0.0` 开始
- **每日更新**: 自动递增修订号 (`V1.0.0` → `V1.0.1` → `V1.0.2`)
- **功能更新**: 手动调整次版本号 (`V1.0.x` → `V1.1.0`)
- **重大更新**: 手动调整主版本号 (`V1.x.x` → `V2.0.0`)

## 📎 特别说明

### ✅ 规范要求
- 所有版本都以**大写 V 开头**，统一识别
- 所有版本号都必须**严格递增**，避免回退
- 建议使用 **Git tag** 管理每个版本
- 可集成到 **CI/CD 流程**中用于自动部署

### 🎨 版本号含义
- **主版本号**: 重大功能变更或架构调整
- **次版本号**: 新功能添加或重要改进
- **修订号**: Bug修复、小功能更新、日常维护

### 🔄 阶段转换规则
1. **alpha → beta**: 基本功能完成，开始测试
2. **beta → rc**: 测试通过，准备发布
3. **rc → 正式版**: 稳定性验证通过，正式发布
4. **正式版 → patch**: 发现问题，修复更新

## 📊 当前项目状态

### 🏷️ 版本信息
- **当前版本**: 根据最新Git标签确定
- **版本类型**: 正式版本 (每日更新)
- **更新频率**: 每日自动提交
- **管理方式**: 自动化脚本 + 手动调整

### 📁 项目结构
```
HomePage/
├── daily-commit.sh          # 自动提交脚本
├── VERSION_MANAGEMENT.md    # 版本管理说明 (本文件)
├── GIT_SETUP.md            # Git配置说明
├── .gitignore              # Git忽略规则
└── src/                    # 源代码目录
```

## 🚀 最佳实践

### 1. 版本发布流程
```bash
# 1. 开发完成
git add .
git commit -m "feat: 新功能开发完成"

# 2. 创建版本标签
git tag V1.1.0 -m "Release V1.1.0 - 新功能发布"

# 3. 推送到远程
git push origin main
git push origin V1.1.0
```

### 2. 版本回退处理
```bash
# 查看版本历史
git tag -l "V*" | sort -V

# 回退到指定版本
git checkout V1.0.0

# 创建修复分支
git checkout -b hotfix-v1.0.1
```

### 3. 版本对比
```bash
# 对比两个版本的差异
git diff V1.0.0..V1.1.0

# 查看版本间的提交记录
git log V1.0.0..V1.1.0 --oneline
```

---

**版本管理规范已配置完成，支持自动化版本控制！** 🎉
