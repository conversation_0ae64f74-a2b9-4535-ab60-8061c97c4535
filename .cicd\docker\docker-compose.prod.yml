# 🚀 上海荷阁科技 - 生产环境 Docker Compose 配置
# 用于生产环境部署

version: "3.8"

services:
  # ==========================================
  # Next.js 应用服务
  # ==========================================
  app:
    image: ${DOCKER_REGISTRY}/${DOCKER_IMAGE}:${IMAGE_TAG:-latest}
    container_name: hege-tech-web-prod
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_SITE_URL=${SITE_URL}
      - NEXT_PUBLIC_SITE_NAME=上海荷阁科技有限公司
      - DATABASE_URL=${DATABASE_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - JWT_SECRET=${JWT_SECRET}
      - BCRYPT_ROUNDS=${BCRYPT_ROUNDS:-12}
      - CSRF_SECRET=${CSRF_SECRET}
      - API_SECRET_KEY=${API_SECRET_KEY}
      - ENABLE_SECURITY_LOGS=${ENABLE_SECURITY_LOGS:-true}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/public/uploads
    networks:
      - hege-network
    restart: always
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: "1.0"
          memory: 1G
        reservations:
          cpus: "0.5"
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ==========================================
  # PostgreSQL 数据库服务
  # ==========================================
  database:
    image: postgres:15-alpine
    container_name: hege-tech-db-prod
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - postgres_backups:/backups
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    networks:
      - hege-network
    restart: always
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 256M
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ==========================================
  # Redis 缓存服务
  # ==========================================
  redis:
    image: redis:7-alpine
    container_name: hege-tech-redis-prod
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    networks:
      - hege-network
    restart: always
    deploy:
      resources:
        limits:
          cpus: "0.25"
          memory: 256M
        reservations:
          cpus: "0.1"
          memory: 128M
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

  # ==========================================
  # Nginx 反向代理服务
  # ==========================================
  nginx:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    container_name: hege-tech-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/prod.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - hege-network
    restart: always
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 256M
        reservations:
          cpus: "0.1"
          memory: 128M
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ==========================================
  # 监控服务 - Prometheus
  # ==========================================
  prometheus:
    image: prom/prometheus:latest
    container_name: hege-tech-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--storage.tsdb.path=/prometheus"
      - "--web.console.libraries=/etc/prometheus/console_libraries"
      - "--web.console.templates=/etc/prometheus/consoles"
      - "--storage.tsdb.retention.time=200h"
      - "--web.enable-lifecycle"
    networks:
      - hege-network
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

  # ==========================================
  # 监控服务 - Grafana
  # ==========================================
  grafana:
    image: grafana/grafana:latest
    container_name: hege-tech-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus
    networks:
      - hege-network
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

# ==========================================
# 网络配置
# ==========================================
networks:
  hege-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ==========================================
# 数据卷配置
# ==========================================
volumes:
  postgres_data:
    driver: local
  postgres_backups:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
