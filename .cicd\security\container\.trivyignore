# 🚀 上海荷阁科技 - Trivy 忽略文件
# 用于忽略特定的漏洞或误报

# ==========================================
# 格式说明
# ==========================================
# CVE-YYYY-NNNNN        # 忽略特定 CVE
# pkg:type/name@version  # 忽略特定包版本
# file:path/to/file      # 忽略特定文件

# ==========================================
# 临时忽略的 CVE
# ==========================================
# 注意: 只有在确认风险可接受时才添加
# 需要定期审查并移除已修复的漏洞

# 示例: 忽略特定的 CVE (需要业务理由)
# CVE-2021-44228  # Log4j 漏洞 - 项目中未使用 Java

# ==========================================
# 开发依赖相关忽略
# ==========================================
# 开发和测试依赖中的低风险漏洞可以临时忽略

# 测试框架相关
pkg:npm/jest@*
pkg:npm/@testing-library/*

# 开发工具相关
pkg:npm/eslint@*
pkg:npm/prettier@*
pkg:npm/@types/*

# ==========================================
# 基础镜像相关忽略
# ==========================================
# Alpine Linux 基础镜像的一些已知但低风险的漏洞

# 示例: Alpine 包管理器相关
# CVE-2023-XXXXX  # Alpine apk 工具的低风险漏洞

# ==========================================
# 文件路径忽略
# ==========================================
# 忽略测试文件和示例代码中的漏洞

file:test/**
file:tests/**
file:**/*.test.js
file:**/*.spec.ts
file:examples/**
file:docs/**

# ==========================================
# 特定包版本忽略
# ==========================================
# 当升级包版本会破坏兼容性时的临时措施

# 示例: 特定版本的已知问题
# pkg:npm/lodash@4.17.20  # 等待兼容性测试完成

# ==========================================
# 配置文件相关忽略
# ==========================================
# 配置文件中的敏感信息检测误报

file:.env.example
file:config-example.toml
file:**/*.example.*

# ==========================================
# 注意事项
# ==========================================
# 1. 每个忽略项都应该有明确的业务理由
# 2. 定期审查此文件，移除已修复的漏洞
# 3. 高危和严重漏洞不应被忽略
# 4. 生产环境部署前必须解决所有严重漏洞
# 5. 建议每月审查忽略列表的有效性

# ==========================================
# 审查记录
# ==========================================
# 最后审查日期: 2024-01-01
# 审查人员: 安全团队
# 下次审查: 2024-02-01
