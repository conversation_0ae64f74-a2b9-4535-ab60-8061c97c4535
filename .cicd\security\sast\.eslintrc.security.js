// 🚀 上海荷阁科技 - ESLint 安全规则配置
// 用于静态应用安全测试 (SAST)

module.exports = {
  extends: ["next/core-web-vitals", "plugin:security/recommended"],
  plugins: ["security"],
  rules: {
    // ==========================================
    // 安全相关规则
    // ==========================================

    // 禁止使用 eval()
    "no-eval": "error",

    // 禁止使用 Function 构造函数
    "no-new-func": "error",

    // 禁止使用 with 语句
    "no-with": "error",

    // 禁止使用 __proto__
    "no-proto": "error",

    // 禁止使用 caller 和 callee
    "no-caller": "error",

    // 禁止使用 arguments.caller 和 arguments.callee
    "no-restricted-properties": [
      "error",
      {
        object: "arguments",
        property: "callee",
        message: "arguments.callee is deprecated",
      },
      {
        object: "arguments",
        property: "caller",
        message: "arguments.caller is deprecated",
      },
    ],

    // ==========================================
    // Security Plugin 规则
    // ==========================================

    // 检测可能的 XSS 漏洞
    "security/detect-unsafe-regex": "error",
    "security/detect-non-literal-regexp": "warn",
    "security/detect-non-literal-fs-filename": "warn",
    "security/detect-eval-with-expression": "error",
    "security/detect-pseudoRandomBytes": "warn",
    "security/detect-possible-timing-attacks": "warn",
    "security/detect-no-csrf-before-method-override": "warn",
    "security/detect-buffer-noassert": "warn",
    "security/detect-child-process": "warn",
    "security/detect-disable-mustache-escape": "warn",
    "security/detect-object-injection": "warn",
    "security/detect-new-buffer": "warn",
    "security/detect-unsafe-regex": "error",

    // ==========================================
    // Next.js 特定安全规则
    // ==========================================

    // 禁止在客户端组件中使用服务器端 API
    "@next/next/no-server-import-in-page": "error",

    // 确保正确使用 Image 组件
    "@next/next/no-img-element": "warn",

    // 禁止使用危险的 HTML 属性
    "react/no-danger": "warn",
    "react/no-danger-with-children": "error",

    // ==========================================
    // 自定义安全规则
    // ==========================================

    // 禁止使用不安全的随机数生成
    "no-restricted-globals": [
      "error",
      {
        name: "Math.random",
        message: "使用 crypto.randomBytes() 或其他加密安全的随机数生成器",
      },
    ],

    // 禁止使用不安全的字符串方法
    "no-restricted-syntax": [
      "error",
      {
        selector: "CallExpression[callee.property.name='innerHTML']",
        message: "避免使用 innerHTML，使用 textContent 或 React 的 JSX",
      },
      {
        selector: "CallExpression[callee.property.name='outerHTML']",
        message: "避免使用 outerHTML，使用安全的 DOM 操作方法",
      },
    ],

    // 要求使用严格模式
    strict: ["error", "global"],

    // 禁止使用 console.log 在生产环境
    "no-console": process.env.NODE_ENV === "production" ? "error" : "warn",

    // 禁止使用 debugger
    "no-debugger": process.env.NODE_ENV === "production" ? "error" : "warn",

    // 禁止使用 alert, confirm, prompt
    "no-alert": "error",

    // ==========================================
    // TypeScript 安全规则
    // ==========================================

    // 禁止使用 any 类型
    "@typescript-eslint/no-explicit-any": "warn",

    // 禁止使用 @ts-ignore
    "@typescript-eslint/ban-ts-comment": "error",

    // 要求明确的函数返回类型
    "@typescript-eslint/explicit-function-return-type": "warn",

    // 禁止使用不安全的成员访问
    "@typescript-eslint/no-unsafe-member-access": "warn",
    "@typescript-eslint/no-unsafe-call": "warn",
    "@typescript-eslint/no-unsafe-assignment": "warn",
    "@typescript-eslint/no-unsafe-return": "warn",
  },

  // ==========================================
  // 环境配置
  // ==========================================
  env: {
    browser: true,
    node: true,
    es2022: true,
  },

  // ==========================================
  // 解析器配置
  // ==========================================
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: "module",
    ecmaFeatures: {
      jsx: true,
    },
    project: "./tsconfig.json",
  },

  // ==========================================
  // 忽略模式
  // ==========================================
  ignorePatterns: [
    "node_modules/",
    ".next/",
    "out/",
    "dist/",
    "build/",
    "*.config.js",
    "*.config.ts",
  ],

  // ==========================================
  // 覆盖配置
  // ==========================================
  overrides: [
    {
      files: ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"],
      rules: {
        // 测试文件中允许使用 any
        "@typescript-eslint/no-explicit-any": "off",
        // 测试文件中允许使用 console
        "no-console": "off",
      },
    },
    {
      files: ["**/*.config.js", "**/*.config.ts"],
      rules: {
        // 配置文件中允许使用 require
        "@typescript-eslint/no-var-requires": "off",
      },
    },
  ],
};
