/** @type {import('next').NextConfig} */
const nextConfig = {
  // 图片配置
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**",
      },
      {
        protocol: "http",
        hostname: "localhost",
      },
      {
        protocol: "http",
        hostname: "192.168.*",
      },
    ],
    // 图片优化安全配置
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: false,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // 环境变量
  env: {
    CUSTOM_KEY: "my-value",
  },

  // 安全HTTP头部配置
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          // 内容安全策略 (CSP)
          {
            key: "Content-Security-Policy",
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
              "font-src 'self' https://fonts.gstatic.com",
              "img-src 'self' data: https: http:",
              "connect-src 'self' https://www.google-analytics.com https://cloud.tencent.com",
              "frame-src 'none'",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'",
              "upgrade-insecure-requests",
            ].join("; "),
          },
          // 防止点击劫持
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          // 防止MIME类型嗅探
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          // XSS防护
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          // 引用策略
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
          // 权限策略
          {
            key: "Permissions-Policy",
            value: [
              "camera=()",
              "microphone=()",
              "geolocation=()",
              "payment=()",
              "usb=()",
              "magnetometer=()",
              "gyroscope=()",
              "accelerometer=()",
            ].join(", "),
          },
          // HSTS (仅在HTTPS环境下)
          {
            key: "Strict-Transport-Security",
            value: "max-age=31536000; includeSubDomains; preload",
          },
          // 防止DNS预取
          {
            key: "X-DNS-Prefetch-Control",
            value: "off",
          },
          // 防止下载执行
          {
            key: "X-Download-Options",
            value: "noopen",
          },
          // 防止内容类型嗅探
          {
            key: "X-Permitted-Cross-Domain-Policies",
            value: "none",
          },
        ],
      },
    ];
  },

  // 重写规则 - 隐藏敏感路径
  async rewrites() {
    return [
      {
        source: "/admin-panel/:path*",
        destination: "/admin/:path*",
      },
    ];
  },

  // 重定向规则 - 安全重定向
  async redirects() {
    return [
      // 禁止访问敏感文件
      {
        source: "/.env",
        destination: "/404",
        permanent: false,
      },
      {
        source: "/.env.local",
        destination: "/404",
        permanent: false,
      },
      {
        source: "/package.json",
        destination: "/404",
        permanent: false,
      },
    ];
  },

  // 编译配置
  compiler: {
    // 移除console.log (生产环境)
    removeConsole:
      process.env.NODE_ENV === "production"
        ? {
            exclude: ["error", "warn"],
          }
        : false,
  },

  // 实验性功能
  experimental: {
    // 启用严格模式
    strictNextHead: true,
    // 启用SWC压缩
    swcMinify: true,
  },

  // 禁用X-Powered-By头部
  poweredByHeader: false,

  // 启用压缩
  compress: true,

  // 生成ETag
  generateEtags: true,
};

module.exports = nextConfig;
