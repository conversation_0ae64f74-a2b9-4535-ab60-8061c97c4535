# 🚀 上海荷阁科技 - Windows Docker 镜像源配置脚本
# PowerShell 脚本，用于在 Windows 环境下配置 Docker 镜像源

param(
    [switch]$Help,
    [switch]$Test,
    [switch]$Verify
)

# 颜色输出函数
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] ✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] ⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] ❌ $Message" -ForegroundColor Red
}

# 显示帮助信息
function Show-Help {
    Write-Host @"
🚀 上海荷阁科技 Windows Docker 镜像源配置脚本

用法: .\windows-setup.ps1 [选项]

选项:
    -Help     显示此帮助信息
    -Test     测试镜像拉取
    -Verify   验证 Docker 配置

示例:
    .\windows-setup.ps1           # 配置镜像源
    .\windows-setup.ps1 -Test     # 测试镜像拉取
    .\windows-setup.ps1 -Verify   # 验证配置

注意:
    - 需要管理员权限运行
    - 配置后需要重启 Docker Desktop

"@
}

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 检查 Docker 是否安装
function Test-DockerInstalled {
    try {
        $null = Get-Command docker -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# 获取 Docker 配置目录
function Get-DockerConfigPath {
    $paths = @(
        "$env:ProgramData\Docker\config",
        "$env:USERPROFILE\.docker"
    )
    
    foreach ($path in $paths) {
        if (Test-Path $path -PathType Container) {
            return $path
        }
    }
    
    # 如果都不存在，返回默认路径
    return "$env:ProgramData\Docker\config"
}

# 备份现有配置
function Backup-DockerConfig {
    param([string]$ConfigPath)
    
    $daemonJsonPath = Join-Path $ConfigPath "daemon.json"
    
    if (Test-Path $daemonJsonPath) {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupPath = "$daemonJsonPath.backup.$timestamp"
        Copy-Item $daemonJsonPath $backupPath
        Write-Success "已备份现有配置: $backupPath"
    }
}

# 应用 Docker 配置
function Set-DockerMirrors {
    Write-Info "开始配置 Docker 镜像源..."
    
    # 检查管理员权限
    if (-not (Test-Administrator)) {
        Write-Error "需要管理员权限运行此脚本"
        Write-Info "请以管理员身份运行 PowerShell"
        return $false
    }
    
    # 检查 Docker 是否安装
    if (-not (Test-DockerInstalled)) {
        Write-Error "Docker 未安装"
        return $false
    }
    
    # 获取配置路径
    $configPath = Get-DockerConfigPath
    Write-Info "Docker 配置目录: $configPath"
    
    # 创建配置目录
    if (-not (Test-Path $configPath)) {
        Write-Info "创建配置目录: $configPath"
        New-Item -Path $configPath -ItemType Directory -Force | Out-Null
    }
    
    # 备份现有配置
    Backup-DockerConfig -ConfigPath $configPath
    
    # 创建新的 daemon.json 配置
    $daemonConfig = @{
        "registry-mirrors" = @(
            "https://docker.mirrors.ustc.edu.cn",
            "https://hub-mirror.c.163.com",
            "https://mirror.baidubce.com",
            "https://ccr.ccs.tencentyun.com",
            "https://dockerproxy.com",
            "https://docker.nju.edu.cn"
        )
        "insecure-registries" = @()
        "debug" = $false
        "experimental" = $false
        "features" = @{
            "buildkit" = $true
        }
        "max-concurrent-downloads" = 10
        "max-concurrent-uploads" = 5
        "max-download-attempts" = 5
    }
    
    # 写入配置文件
    $daemonJsonPath = Join-Path $configPath "daemon.json"
    $daemonConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $daemonJsonPath -Encoding UTF8
    
    Write-Success "Docker 配置已更新: $daemonJsonPath"
    Write-Warning "请重启 Docker Desktop 使配置生效"
    
    return $true
}

# 重启 Docker Desktop
function Restart-DockerDesktop {
    Write-Info "尝试重启 Docker Desktop..."
    
    try {
        # 停止 Docker Desktop
        $dockerProcess = Get-Process -Name "Docker Desktop" -ErrorAction SilentlyContinue
        if ($dockerProcess) {
            Write-Info "停止 Docker Desktop..."
            $dockerProcess | Stop-Process -Force
            Start-Sleep -Seconds 5
        }
        
        # 启动 Docker Desktop
        $dockerPath = "${env:ProgramFiles}\Docker\Docker\Docker Desktop.exe"
        if (Test-Path $dockerPath) {
            Write-Info "启动 Docker Desktop..."
            Start-Process -FilePath $dockerPath
            Write-Success "Docker Desktop 已启动"
            Write-Info "请等待 Docker 服务完全启动..."
            return $true
        } else {
            Write-Warning "未找到 Docker Desktop 可执行文件"
            return $false
        }
    }
    catch {
        Write-Warning "无法自动重启 Docker Desktop，请手动重启"
        return $false
    }
}

# 验证配置
function Test-DockerConfig {
    Write-Info "验证 Docker 配置..."
    
    try {
        $dockerInfo = docker info 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Docker 服务正常运行"
            return $true
        } else {
            Write-Error "Docker 服务异常"
            return $false
        }
    }
    catch {
        Write-Error "无法连接到 Docker 服务"
        return $false
    }
}

# 测试镜像拉取
function Test-ImagePull {
    Write-Info "测试镜像拉取..."
    
    $testImage = "hello-world:latest"
    
    try {
        Write-Info "拉取测试镜像: $testImage"
        docker pull $testImage 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "镜像拉取测试成功"
            # 清理测试镜像
            docker rmi $testImage 2>$null | Out-Null
            return $true
        } else {
            Write-Warning "镜像拉取测试失败"
            return $false
        }
    }
    catch {
        Write-Error "镜像拉取测试异常"
        return $false
    }
}

# 主函数
function Main {
    Write-Info "🚀 上海荷阁科技 - Windows Docker 镜像源配置"
    Write-Host ""
    
    if ($Help) {
        Show-Help
        return
    }
    
    if ($Verify) {
        Test-DockerConfig
        return
    }
    
    if ($Test) {
        Test-ImagePull
        return
    }
    
    # 执行配置
    if (Set-DockerMirrors) {
        Write-Host ""
        Write-Info "配置完成！下一步操作："
        Write-Host "  1. 重启 Docker Desktop"
        Write-Host "  2. 等待 Docker 服务启动"
        Write-Host "  3. 运行测试: .\windows-setup.ps1 -Test"
        Write-Host "  4. 启动服务: docker-compose -f .cicd\docker\docker-compose.yml up -d"
        Write-Host ""
        
        # 询问是否自动重启
        $restart = Read-Host "是否自动重启 Docker Desktop? (y/N)"
        if ($restart -eq 'y' -or $restart -eq 'Y') {
            Restart-DockerDesktop
        }
    }
}

# 执行主函数
Main
