# Shanghai Hege Technology - Test Runner with Report Generation
# Runs tests and ensures reports are generated regardless of test results

$ErrorActionPreference = "Continue"

Write-Host "Starting test execution with report generation..." -ForegroundColor Green

# Create coverage directory if it doesn't exist
if (-not (Test-Path "coverage")) {
    New-Item -ItemType Directory -Path "coverage" -Force | Out-Null
    Write-Host "Created coverage directory" -ForegroundColor Yellow
}

# Initialize variables
$testExitCode = 0
$testOutput = ""
$testStartTime = Get-Date

try {
    Write-Host "Running Jest tests..." -ForegroundColor Cyan
    
    # Run tests and capture output
    $testOutput = npm run test:ci 2>&1 | Out-String
    $testExitCode = $LASTEXITCODE
    
    Write-Host "Test execution completed with exit code: $testExitCode" -ForegroundColor $(if ($testExitCode -eq 0) { "Green" } else { "Red" })
    
} catch {
    Write-Host "Test execution failed with exception: $($_.Exception.Message)" -ForegroundColor Red
    $testExitCode = 1
    $testOutput = $_.Exception.Message
}

$testEndTime = Get-Date
$testDuration = ($testEndTime - $testStartTime).TotalSeconds

# Generate basic test report if Jest didn't create one
if (-not (Test-Path "coverage/junit.xml")) {
    Write-Host "Generating fallback JUnit report..." -ForegroundColor Yellow
    
    $junitXml = @"
<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Unit Tests" tests="1" failures="$(if ($testExitCode -eq 0) { 0 } else { 1 })" errors="0" time="$testDuration" timestamp="$(Get-Date -Format 'yyyy-MM-ddTHH:mm:ss')">
  <testsuite name="Test Execution" tests="1" failures="$(if ($testExitCode -eq 0) { 0 } else { 1 })" errors="0" time="$testDuration">
    <testcase name="Jest Test Runner" classname="test.execution" time="$testDuration">
$(if ($testExitCode -ne 0) {
"      <failure message=""Test execution failed with exit code $testExitCode"" type=""TestExecutionError"">
        <![CDATA[$($testOutput -replace '&', '&amp;' -replace '<', '&lt;' -replace '>', '&gt;')]]>
      </failure>"
} else {
"      <!-- Tests passed successfully -->"
})
    </testcase>
  </testsuite>
</testsuites>
"@
    
    $junitXml | Out-File -FilePath "coverage/junit.xml" -Encoding UTF8
    Write-Host "Fallback JUnit report generated" -ForegroundColor Green
}

# Generate basic coverage report if it doesn't exist
if (-not (Test-Path "coverage/cobertura-coverage.xml")) {
    Write-Host "Generating fallback coverage report..." -ForegroundColor Yellow
    
    $coberturaXml = @"
<?xml version="1.0" encoding="UTF-8"?>
<coverage line-rate="0" branch-rate="0" lines-covered="0" lines-valid="0" branches-covered="0" branches-valid="0" complexity="0" version="0.1" timestamp="$(Get-Date -UFormat %s)">
  <sources>
    <source>src</source>
  </sources>
  <packages>
    <package name="fallback" line-rate="0" branch-rate="0" complexity="0">
      <classes>
        <class name="TestExecution" filename="test-execution" line-rate="0" branch-rate="0" complexity="0">
          <methods></methods>
          <lines></lines>
        </class>
      </classes>
    </package>
  </packages>
</coverage>
"@
    
    $coberturaXml | Out-File -FilePath "coverage/cobertura-coverage.xml" -Encoding UTF8
    Write-Host "Fallback coverage report generated" -ForegroundColor Green
}

# Generate test summary
$summaryReport = @"
# Test Execution Report

**Generated:** $(Get-Date -Format 'yyyy-MM-ddTHH:mm:ss')
**Duration:** $([math]::Round($testDuration, 2)) seconds

## Summary
- **Status:** $(if ($testExitCode -eq 0) { "✅ PASSED" } else { "❌ FAILED" })
- **Exit Code:** $testExitCode

## Test Output
``````
$testOutput
``````

## Files Generated
- coverage/junit.xml (JUnit test results)
- coverage/cobertura-coverage.xml (Coverage report)
- coverage/test-summary.md (This summary)

$(if ($testExitCode -ne 0) {
"## Troubleshooting
The tests failed. Common issues:
1. **Jest Configuration**: Check .cicd/testing/jest/jest.config.js
2. **Missing Dependencies**: Ensure all test dependencies are installed
3. **Path Issues**: Verify all paths in Jest config are correct
4. **Test Files**: Ensure test files exist and are properly written
"
})
"@

$summaryReport | Out-File -FilePath "coverage/test-summary.md" -Encoding UTF8
Write-Host "Test summary report generated" -ForegroundColor Green

# Display summary
Write-Host "`n=== TEST EXECUTION SUMMARY ===" -ForegroundColor Cyan
Write-Host "Status: $(if ($testExitCode -eq 0) { "PASSED" } else { "FAILED" })" -ForegroundColor $(if ($testExitCode -eq 0) { "Green" } else { "Red" })
Write-Host "Duration: $([math]::Round($testDuration, 2)) seconds" -ForegroundColor White
Write-Host "Exit Code: $testExitCode" -ForegroundColor White
Write-Host "Reports generated in coverage/ directory" -ForegroundColor White
Write-Host "===============================" -ForegroundColor Cyan

# Exit with the original test exit code
exit $testExitCode
