// 🚀 上海荷阁科技 - Jest 测试配置
// 用于单元测试和集成测试

const path = require('path');

// 获取项目根目录
const rootDir = path.resolve(__dirname, '../../../');

// Jest 配置
const jestConfig = {
  // 设置根目录
  rootDir: rootDir,
  // ==========================================
  // 基础配置
  // ==========================================
  displayName: "上海荷阁科技 - 单元测试",
  testEnvironment: "jsdom",

  // ==========================================
  // 文件匹配模式
  // ==========================================
  testMatch: [
    "<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}",
    "<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}",
    "<rootDir>/tests/**/*.{test,spec}.{js,jsx,ts,tsx}",
  ],

  // 忽略的测试文件
  testPathIgnorePatterns: [
    "<rootDir>/.next/",
    "<rootDir>/node_modules/",
    "<rootDir>/out/",
    "<rootDir>/build/",
    "<rootDir>/dist/",
  ],

  // ==========================================
  // 模块解析配置
  // ==========================================
  moduleNameMapper: {
    // 路径别名映射
    "^@/(.*)$": "<rootDir>/src/$1",
    "^@/components/(.*)$": "<rootDir>/src/components/$1",
    "^@/lib/(.*)$": "<rootDir>/src/lib/$1",
    "^@/types/(.*)$": "<rootDir>/src/types/$1",
    "^@/styles/(.*)$": "<rootDir>/src/styles/$1",

    // 静态资源模拟
    "\\.(css|less|scss|sass)$": "identity-obj-proxy",
    "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$":
      "<rootDir>/.cicd/testing/jest/__mocks__/fileMock.js",
  },

  // 模块文件扩展名
  moduleFileExtensions: ["js", "jsx", "ts", "tsx", "json"],

  // ==========================================
  // 设置文件
  // ==========================================
  setupFilesAfterEnv: ["<rootDir>/.cicd/testing/jest/jest.setup.js"],

  // ==========================================
  // 覆盖率配置
  // ==========================================
  collectCoverage: true,
  collectCoverageFrom: [
    "src/**/*.{js,jsx,ts,tsx}",
    "!src/**/*.d.ts",
    "!src/**/*.stories.{js,jsx,ts,tsx}",
    "!src/**/__tests__/**",
    "!src/**/*.test.{js,jsx,ts,tsx}",
    "!src/**/*.spec.{js,jsx,ts,tsx}",
    "!src/app/**/layout.tsx",
    "!src/app/**/loading.tsx",
    "!src/app/**/error.tsx",
    "!src/app/**/not-found.tsx",
    "!src/middleware.ts",
  ],

  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
    // 组件特定阈值
    "src/components/": {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
    // 工具函数特定阈值
    "src/lib/": {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
  },

  // 覆盖率报告格式
  coverageReporters: [
    "text",
    "text-summary",
    "html",
    "lcov",
    "cobertura",
    "json",
    "json-summary",
  ],

  // 覆盖率输出目录
  coverageDirectory: "<rootDir>/coverage",

  // ==========================================
  // 转换配置
  // ==========================================
  transform: {
    "^.+\\.(js|jsx|ts|tsx)$": ["babel-jest", { presets: ["next/babel"] }],
  },

  // 转换忽略模式
  transformIgnorePatterns: [
    "/node_modules/",
    "^.+\\.module\\.(css|sass|scss)$",
  ],

  // ==========================================
  // 测试环境配置
  // ==========================================
  testEnvironmentOptions: {
    url: "http://localhost:3000",
  },

  // 全局变量
  globals: {
    "ts-jest": {
      tsconfig: "tsconfig.json",
    },
  },

  // ==========================================
  // 报告配置
  // ==========================================
  reporters: [
    "default",
    [
      "jest-junit",
      {
        outputDirectory: "<rootDir>/coverage",
        outputName: "junit.xml",
        classNameTemplate: "{classname}",
        titleTemplate: "{title}",
        ancestorSeparator: " › ",
        usePathForSuiteName: true,
      },
    ],
    [
      "jest-html-reporters",
      {
        publicPath: "<rootDir>/coverage",
        filename: "test-report.html",
        expand: true,
        hideIcon: false,
        pageTitle: "上海荷阁科技 - 测试报告",
        // logoImgPath: "<rootDir>/public/hege-logo.svg", // 暂时注释掉避免路径问题
      },
    ],
  ],

  // ==========================================
  // 性能配置
  // ==========================================
  maxWorkers: "50%",
  cache: true,
  cacheDirectory: "<rootDir>/.jest-cache",

  // ==========================================
  // 监视模式配置
  // ==========================================
  watchman: true,
  watchPathIgnorePatterns: [
    "<rootDir>/.next/",
    "<rootDir>/node_modules/",
    "<rootDir>/coverage/",
    "<rootDir>/.git/",
  ],

  // ==========================================
  // 错误处理
  // ==========================================
  errorOnDeprecated: true,
  verbose: true,

  // ==========================================
  // 自定义配置
  // ==========================================
  // 测试超时 (毫秒)
  testTimeout: 30000,

  // 清除模拟
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true,

  // ==========================================
  // 项目特定配置
  // ==========================================
  // 暂时移除 projects 配置以简化调试
};

// 导出 Jest 配置
module.exports = jestConfig;
