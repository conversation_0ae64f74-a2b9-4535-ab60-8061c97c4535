# 🚀 上海荷阁科技 - npm 配置文件
# 使用国内镜像源提高依赖安装速度和稳定性

# 主镜像源 - 使用淘宝镜像
registry=https://registry.npmmirror.com/

# 特定包的镜像源配置
@types:registry=https://registry.npmmirror.com/
@testing-library:registry=https://registry.npmmirror.com/
@playwright:registry=https://registry.npmmirror.com/
@lhci:registry=https://registry.npmmirror.com/

# 性能优化配置
audit=false
fund=false
prefer-offline=true
cache-max=86400000

# 安全配置
package-lock=true
save-exact=false
