# Shanghai Hege Technology - Local Docker Deployment Script
# Deploy Docker container to local environment with proper error handling

param(
    [string]$ImageName = "hege-tech-web",
    [string]$ImageTag = "latest",
    [string]$ContainerName = "hege-tech-web-local",
    [string]$HostPort = "3000",
    [string]$ContainerPort = "3000"
)

$ErrorActionPreference = "Continue"

Write-Host "Starting local Docker deployment..." -ForegroundColor Green
Write-Host "Image: ${ImageName}:${ImageTag}" -ForegroundColor Yellow
Write-Host "Container: $ContainerName" -ForegroundColor Yellow
Write-Host "Port mapping: ${HostPort}:${ContainerPort}" -ForegroundColor Yellow

# Function to safely execute Docker commands
function Invoke-DockerCommand {
    param(
        [string]$Command,
        [string]$SuccessMessage = "",
        [string]$FailureMessage = "Command failed"
    )
    
    Write-Host "Executing: $Command" -ForegroundColor Cyan
    
    try {
        $result = Invoke-Expression $Command 2>$null
        if ($LASTEXITCODE -eq 0) {
            if ($SuccessMessage) {
                Write-Host $SuccessMessage -ForegroundColor Green
            }
            return $result
        } else {
            Write-Host $FailureMessage -ForegroundColor Yellow
            return $null
        }
    } catch {
        Write-Host "$FailureMessage - $($_.Exception.Message)" -ForegroundColor Yellow
        return $null
    }
}

# Step 1: Stop existing container
Write-Host "`n=== Step 1: Stopping existing container ===" -ForegroundColor Magenta
Invoke-DockerCommand -Command "docker stop $ContainerName" -FailureMessage "Container does not exist or already stopped"

# Step 2: Remove existing container
Write-Host "`n=== Step 2: Removing existing container ===" -ForegroundColor Magenta
Invoke-DockerCommand -Command "docker rm $ContainerName" -FailureMessage "Container does not exist"

# Step 3: Verify image exists
Write-Host "`n=== Step 3: Verifying Docker image ===" -ForegroundColor Magenta
$imageExists = Invoke-DockerCommand -Command "docker images --format `"{{.Repository}}:{{.Tag}}`" --filter `"reference=${ImageName}:${ImageTag}`""
if (-not $imageExists) {
    Write-Host "ERROR: Docker image ${ImageName}:${ImageTag} not found!" -ForegroundColor Red
    Write-Host "Available images:" -ForegroundColor Yellow
    docker images $ImageName --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    exit 1
}
Write-Host "Docker image verified successfully" -ForegroundColor Green

# Step 4: Start new container
Write-Host "`n=== Step 4: Starting new container ===" -ForegroundColor Magenta
$runCommand = "docker run -d --name $ContainerName -p `"${HostPort}:${ContainerPort}`" `"${ImageName}:${ImageTag}`""
$containerId = Invoke-DockerCommand -Command $runCommand -SuccessMessage "Container started successfully"

if (-not $containerId) {
    Write-Host "ERROR: Failed to start container!" -ForegroundColor Red
    exit 1
}

Write-Host "Container ID: $containerId" -ForegroundColor Green

# Step 5: Wait for container to start
Write-Host "`n=== Step 5: Waiting for container to start ===" -ForegroundColor Magenta
Write-Host "Waiting 15 seconds for container to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Step 6: Verify container status
Write-Host "`n=== Step 6: Verifying container status ===" -ForegroundColor Magenta
$containerStatus = Invoke-DockerCommand -Command "docker ps -f name=$ContainerName --format `"{{.Status}}`""
if ($containerStatus) {
    Write-Host "Container status: $containerStatus" -ForegroundColor Green
} else {
    Write-Host "WARNING: Unable to get container status" -ForegroundColor Yellow
}

# Step 7: Display deployment summary
Write-Host "`n=== Deployment Summary ===" -ForegroundColor Green
Write-Host "✓ Container Name: $ContainerName" -ForegroundColor White
Write-Host "✓ Image: ${ImageName}:${ImageTag}" -ForegroundColor White
Write-Host "✓ Port Mapping: ${HostPort}:${ContainerPort}" -ForegroundColor White
Write-Host "✓ Access URL: http://localhost:${HostPort}" -ForegroundColor White

# Step 8: Display container information
Write-Host "`n=== Container Information ===" -ForegroundColor Cyan
docker ps -a -f name=$ContainerName --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.CreatedAt}}"

Write-Host "`nLocal Docker deployment completed successfully!" -ForegroundColor Green
