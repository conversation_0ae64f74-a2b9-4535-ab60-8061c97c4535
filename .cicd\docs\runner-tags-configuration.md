# GitLab Runner Tags Configuration

## 问题描述

在GitLab CI/CD运行时遇到"该项目没有分配任何可用Runner"的错误，这是因为作业没有正确配置runner标签，导致GitLab无法找到匹配的runner来执行作业。

## 解决方案

为所有CI/CD作业添加了适当的runner标签配置，确保作业能够被正确的Windows runner执行。

## 配置的Runner Tags

### 标准Windows Runner标签
```yaml
tags:
  - windows
  - shell
```

**说明**：
- `windows`: 指定需要Windows操作系统的runner
- `shell`: 指定使用shell执行器（PowerShell）

## 修改的作业

以下所有作业都已添加了runner标签配置：

### 1. 准备阶段 (prepare)
- `test:windows-runner` - Windows runner连接测试
- `prepare:dependencies` - 依赖安装

### 2. 测试阶段 (test)
- `test:unit` - 单元测试
- `test:lint` - 代码检查
- `test:type-check` - TypeScript类型检查

### 3. 安全扫描阶段 (security)
- `security:sast` - 静态应用安全测试
- `security:dependency-scan` - 依赖漏洞扫描

### 4. 构建阶段 (build)
- `build:app` - Next.js应用构建
- `build:docker` - Docker镜像构建

### 5. 部署阶段 (deploy)
- `deploy:local` - 本地Docker部署
- `cleanup:local` - 本地环境清理

## 配置示例

### 作业级别标签配置
```yaml
test:unit:
  stage: test
  tags:
    - windows
    - shell
  dependencies:
    - prepare:dependencies
  script:
    - echo "Running unit tests..."
    - npm run test:ci
```

### 全局默认标签配置
```yaml
default:
  tags:
    - windows
    - shell
  before_script:
    - PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\setup-powershell-environment.ps1"
```

## 验证结果

使用验证脚本`validate-runner-tags.ps1`检查结果：

```
=== Validation Summary ===
Total jobs: 11
Jobs with tags: 11
Jobs without tags: 0

🎉 All jobs have runner tags configured!
GitLab CI/CD should be able to find available runners.
```

## Runner要求

确保您的GitLab Runner满足以下要求：

### 1. Runner注册时的标签
在注册GitLab Runner时，需要包含以下标签：
```bash
gitlab-runner register \
  --url "https://********************.com/" \
  --registration-token "your-token" \
  --executor "shell" \
  --tag-list "windows,shell" \
  --description "Windows PowerShell Runner"
```

### 2. Runner配置文件
在`config.toml`中确认标签配置：
```toml
[[runners]]
  name = "Windows PowerShell Runner"
  url = "https://********************.com/"
  token = "your-token"
  executor = "shell"
  tags = ["windows", "shell"]
  shell = "powershell"
```

### 3. 系统要求
- Windows操作系统
- PowerShell 5.1 或更高版本
- Docker Desktop（用于Docker相关作业）
- Node.js 18+（用于应用构建和测试）

## 故障排除

### 1. 作业仍然被阻塞
如果作业仍然显示"没有可用Runner"：

1. **检查Runner状态**：
   - 在GitLab项目的Settings > CI/CD > Runners中查看runner状态
   - 确认runner是在线状态（绿色圆点）

2. **检查标签匹配**：
   - 确认runner的标签包含`windows`和`shell`
   - 检查作业的标签配置是否正确

3. **检查Runner权限**：
   - 确认runner可以访问您的项目
   - 检查是否有项目级别的runner限制

### 2. 验证Runner配置
使用以下命令验证runner配置：
```powershell
# 检查GitLab CI配置
PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\validate-runner-tags.ps1"

# 检查runner服务状态
Get-Service gitlab-runner
```

## 最佳实践

1. **统一标签命名**：使用一致的标签命名约定
2. **环境特定标签**：为不同环境使用特定标签（如`dev`, `staging`, `prod`）
3. **定期验证**：定期运行验证脚本确保配置正确
4. **文档维护**：保持runner配置文档的更新

## 相关文档

- [GitLab Runner安装指南](../windows/README.md)
- [PowerShell环境配置](powershell-syntax-fixes.md)
- [CI/CD最佳实践](best-practices.md)
