# Shanghai Hege Technology - GitLab CI/CD Fixes Validation Script
# Test all PowerShell syntax fixes for GitLab CI/CD pipeline

param(
    [switch]$SkipDockerTests = $false
)

$ErrorActionPreference = "Continue"

Write-Host "=== GitLab CI/CD Fixes Validation ===" -ForegroundColor Green
Write-Host "Testing all PowerShell syntax fixes..." -ForegroundColor Yellow

$testResults = @()

# Test 1: Variable expansion syntax
Write-Host "`n--- Test 1: Variable Expansion Syntax ---" -ForegroundColor Cyan
try {
    $LOCAL_IMAGE_NAME = "hege-tech-web"
    $LOCAL_IMAGE_TAG = "latest"
    $testCommand = "echo `"Image: ${LOCAL_IMAGE_NAME}:${LOCAL_IMAGE_TAG}`""
    $result = Invoke-Expression $testCommand
    Write-Host "✓ Variable expansion test passed: $result" -ForegroundColor Green
    $testResults += @{Test="Variable Expansion"; Status="PASS"; Details=$result}
} catch {
    Write-Host "✗ Variable expansion test failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += @{Test="Variable Expansion"; Status="FAIL"; Details=$_.Exception.Message}
}

# Test 2: Error handling with $LASTEXITCODE
Write-Host "`n--- Test 2: Error Handling with LASTEXITCODE ---" -ForegroundColor Cyan
try {
    # Test with a command that should fail
    docker inspect "non-existent-image:latest" 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✓ Error handling test passed: Command failed as expected" -ForegroundColor Green
        $testResults += @{Test="Error Handling"; Status="PASS"; Details="LASTEXITCODE correctly detected failure"}
    } else {
        Write-Host "✗ Error handling test failed: Command should have failed" -ForegroundColor Red
        $testResults += @{Test="Error Handling"; Status="FAIL"; Details="LASTEXITCODE did not detect failure"}
    }
} catch {
    Write-Host "✗ Error handling test failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += @{Test="Error Handling"; Status="FAIL"; Details=$_.Exception.Message}
}

# Test 3: File path validation
Write-Host "`n--- Test 3: File Path Validation ---" -ForegroundColor Cyan
$scriptsToTest = @(
    ".cicd\scripts\generate-docker-report.ps1",
    ".cicd\scripts\deploy-local-docker.ps1",
    ".cicd\scripts\cleanup-local-docker.ps1",
    ".cicd\scripts\setup-powershell-environment.ps1"
)

$allScriptsExist = $true
foreach ($script in $scriptsToTest) {
    if (Test-Path $script) {
        Write-Host "✓ Script found: $script" -ForegroundColor Green
    } else {
        Write-Host "✗ Script missing: $script" -ForegroundColor Red
        $allScriptsExist = $false
    }
}

if ($allScriptsExist) {
    $testResults += @{Test="File Path Validation"; Status="PASS"; Details="All required scripts found"}
} else {
    $testResults += @{Test="File Path Validation"; Status="FAIL"; Details="Some scripts are missing"}
}

# Test 4: GitLab CI YAML validation
Write-Host "`n--- Test 4: GitLab CI YAML Validation ---" -ForegroundColor Cyan
try {
    if (Test-Path ".gitlab-ci.yml") {
        # Basic YAML syntax check
        $yamlContent = Get-Content ".gitlab-ci.yml" -Raw
        if ($yamlContent -match "stages:" -and $yamlContent -match "script:") {
            Write-Host "✓ GitLab CI YAML structure is valid" -ForegroundColor Green
            $testResults += @{Test="YAML Validation"; Status="PASS"; Details="Basic YAML structure is correct"}
        } else {
            Write-Host "✗ GitLab CI YAML structure is invalid" -ForegroundColor Red
            $testResults += @{Test="YAML Validation"; Status="FAIL"; Details="Missing required YAML elements"}
        }
    } else {
        Write-Host "✗ .gitlab-ci.yml file not found" -ForegroundColor Red
        $testResults += @{Test="YAML Validation"; Status="FAIL"; Details=".gitlab-ci.yml file not found"}
    }
} catch {
    Write-Host "✗ YAML validation failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += @{Test="YAML Validation"; Status="FAIL"; Details=$_.Exception.Message}
}

# Test 5: Docker commands syntax (if Docker tests are not skipped)
if (-not $SkipDockerTests) {
    Write-Host "`n--- Test 5: Docker Commands Syntax ---" -ForegroundColor Cyan
    try {
        # Test Docker availability
        docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Docker is available" -ForegroundColor Green
            
            # Test Docker command construction
            $CONTAINER_NAME = "test-validation"
            $HOST_PORT = "3002"
            $CONTAINER_PORT = "3000"
            $dockerCommand = "docker run --help"
            
            Invoke-Expression $dockerCommand 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Docker command syntax test passed" -ForegroundColor Green
                $testResults += @{Test="Docker Commands"; Status="PASS"; Details="Docker commands work correctly"}
            } else {
                Write-Host "✗ Docker command syntax test failed" -ForegroundColor Red
                $testResults += @{Test="Docker Commands"; Status="FAIL"; Details="Docker command execution failed"}
            }
        } else {
            Write-Host "⚠ Docker is not available, skipping Docker tests" -ForegroundColor Yellow
            $testResults += @{Test="Docker Commands"; Status="SKIP"; Details="Docker not available"}
        }
    } catch {
        Write-Host "✗ Docker test failed: $($_.Exception.Message)" -ForegroundColor Red
        $testResults += @{Test="Docker Commands"; Status="FAIL"; Details=$_.Exception.Message}
    }
} else {
    Write-Host "`n--- Test 5: Docker Commands Syntax (SKIPPED) ---" -ForegroundColor Yellow
    $testResults += @{Test="Docker Commands"; Status="SKIP"; Details="Skipped by user request"}
}

# Test Summary
Write-Host "`n=== Test Summary ===" -ForegroundColor Green
$passCount = ($testResults | Where-Object { $_.Status -eq "PASS" }).Count
$failCount = ($testResults | Where-Object { $_.Status -eq "FAIL" }).Count
$skipCount = ($testResults | Where-Object { $_.Status -eq "SKIP" }).Count
$totalCount = $testResults.Count

Write-Host "Total Tests: $totalCount" -ForegroundColor White
Write-Host "Passed: $passCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red
Write-Host "Skipped: $skipCount" -ForegroundColor Yellow

# Detailed results
Write-Host "`n=== Detailed Results ===" -ForegroundColor Cyan
foreach ($result in $testResults) {
    $color = switch ($result.Status) {
        "PASS" { "Green" }
        "FAIL" { "Red" }
        "SKIP" { "Yellow" }
        default { "White" }
    }
    Write-Host "[$($result.Status)] $($result.Test): $($result.Details)" -ForegroundColor $color
}

# Final verdict
if ($failCount -eq 0) {
    Write-Host "`n🎉 All tests passed! GitLab CI/CD pipeline should work correctly." -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n❌ Some tests failed. Please review and fix the issues before running GitLab CI/CD." -ForegroundColor Red
    exit 1
}
