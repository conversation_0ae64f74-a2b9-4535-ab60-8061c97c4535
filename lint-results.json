﻿{
    "summary":  {
                    "overall":  {
                                    "status":  "failed"
                                },
                    "format":  {
                                   "status":  "failed",
                                   "issues":  155
                               },
                    "lint":  {
                                 "status":  "passed",
                                 "errors":  0,
                                 "warnings":  4
                             }
                },
    "timestamp":  "2025-07-18T15:51:56",
    "details":  {
                    "lint_output":  {
                                        "value":  "npm WARN config cache-max This option has been deprecated in favor of `--prefer-online`\r\n\r\n\u003e enterprise-website@0.1.0 lint\r\n\u003e next lint\r\n\r\n\r\n./src/app/about/page.tsx\r\n41:6  Warning: React Hook useEffect has a missing dependency: \u0027menuItems\u0027. Either include it or remove the dependency array.  react-hooks/exhaustive-deps\r\n\r\n./src/app/api/upload/route.ts\r\n139:11  Warning: Found writeFile from package \"fs/promises\" with non literal argument at index 0  security/detect-non-literal-fs-filename\r\n\r\n./src/app/news-integrated/page.tsx\r\n108:6  Warning: React Hook useEffect has a missing dependency: \u0027fetchIntegratedNews\u0027. Either include it or remove the dependency array.  react-hooks/exhaustive-deps\r\n\r\n./src/lib/security-config.ts\r\n251:1  Warning: Assign object to a variable before exporting as module default  import/no-anonymous-default-export\r\n\r\ninfo  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/basic-features/eslint#disabling-rules\r\n",
                                        "PSPath":  "D:\\Projects\\hege-tech-web\\lint-output.txt",
                                        "PSParentPath":  "D:\\Projects\\hege-tech-web",
                                        "PSChildName":  "lint-output.txt",
                                        "PSDrive":  {
                                                        "CurrentLocation":  "Projects\\hege-tech-web",
                                                        "Name":  "D",
                                                        "Provider":  {
                                                                         "ImplementingType":  "Microsoft.PowerShell.Commands.FileSystemProvider",
                                                                         "HelpFile":  "System.Management.Automation.dll-Help.xml",
                                                                         "Name":  "FileSystem",
                                                                         "PSSnapIn":  "Microsoft.PowerShell.Core",
                                                                         "ModuleName":  "Microsoft.PowerShell.Core",
                                                                         "Module":  null,
                                                                         "Description":  "",
                                                                         "Capabilities":  52,
                                                                         "Home":  "C:\\Users\\<USER>\\",
                                                        "Description":  "Data",
                                                        "MaximumSize":  null,
                                                        "Credential":  {
                                                                           "UserName":  null,
                                                                           "Password":  null
                                                                       },
                                                        "DisplayRoot":  null
                                                    },
                                        "PSProvider":  {
                                                           "ImplementingType":  {
                                                                                    "Module":  "System.Management.Automation.dll",
                                                                                    "Assembly":  "System.Management.Automation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35",
                                                                                    "TypeHandle":  "System.RuntimeTypeHandle",
                                                                                    "DeclaringMethod":  null,
                                                                                    "BaseType":  "System.Management.Automation.Provider.NavigationCmdletProvider",
                                                                                    "UnderlyingSystemType":  "Microsoft.PowerShell.Commands.FileSystemProvider",
                                                                                    "FullName":  "Microsoft.PowerShell.Commands.FileSystemProvider",
                                                                                    "AssemblyQualifiedName":  "Microsoft.PowerShell.Commands.FileSystemProvider, System.Management.Automation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35",
                                                                                    "Namespace":  "Microsoft.PowerShell.Commands",
                                                                                    "GUID":  "b4755d19-b6a7-38dc-ae06-4167f801062f",
                                                                                    "IsEnum":  false,
                                                                                    "GenericParameterAttributes":  null,
                                                                                    "IsSecurityCritical":  true,
                                                                                    "IsSecuritySafeCritical":  false,
                                                                                    "IsSecurityTransparent":  false,
                                                                                    "IsGenericTypeDefinition":  false,
                                                                                    "IsGenericParameter":  false,
                                                                                    "GenericParameterPosition":  null,
                                                                                    "IsGenericType":  false,
                                                                                    "IsConstructedGenericType":  false,
                                                                                    "ContainsGenericParameters":  false,
                                                                                    "StructLayoutAttribute":  "System.Runtime.InteropServices.StructLayoutAttribute",
                                                                                    "Name":  "FileSystemProvider",
                                                                                    "MemberType":  32,
                                                                                    "DeclaringType":  null,
                                                                                    "ReflectedType":  null,
                                                                                    "MetadataToken":  33554727,
                                                                                    "GenericTypeParameters":  "",
                                                                                    "DeclaredConstructors":  "Void .ctor() Void .cctor()",
                                                                                    "DeclaredEvents":  "",
                                                                                    "DeclaredFields":  "System.Collections.ObjectModel.Collection`1[System.Management.Automation.WildcardPattern] excludeMatcher System.Management.Automation.PSTraceSource tracer Int32 FILETRANSFERSIZE System.String ProviderName",
                                                                                    "DeclaredMembers":  "System.String NormalizePath(System.String) System.IO.FileSystemInfo GetFileSystemInfo(System.String, Boolean ByRef) Boolean IsFilterSet() System.Object GetChildNamesDynamicParameters(System.String) System.Object GetChildItemsDynamicParameters(System.String, Boolean) System.Object CopyItemDynamicParameters(System.String, System.String, Boolean) Boolean IsNetworkMappedDrive(System.Management.Automation.PSDriveInfo) Boolean IsSupportedDriveForPersistence(System.Management.Automation.PSDriveInfo) System.String GetRootPathForNetworkDriveOrDosDevice(System.IO.DriveInfo) System.Collections.ObjectModel.Collection`1[System.Management.Automation.PSDriveInfo] InitializeDefaultDrives() System.Object GetItemDynamicParameters(System.String) Void InvokeDefaultAction(System.String) Void GetChildItems(System.String, Boolean, UInt32) Void GetChildNames(System.String, System.Management.Automation.ReturnContainers) Boolean CheckItemExists(System.String, Boolean ByRef) System.Object RemoveItemDynamicParameters(System.String, Boolean) Void RemoveFileInfoItem(System.IO.FileInfo, Boolean) Boolean ItemExists(System.String) System.Object ItemExistsDynamicParameters(System.String) Boolean HasChildItems(System.String) Void CopyItemLocalOrToSession(System.String, System.String, Boolean, Boolean, System.Management.Automation.PowerShell) Void InitilizeFunctionPSCopyFileFromRemoteSession(System.Management.Automation.PowerShell) Boolean ValidRemoteSessionForScripting(System.Management.Automation.Runspaces.Runspace) Void InitilizeFunctionsPSCopyFileToRemoteSession(System.Management.Automation.PowerShell) Boolean PathIsReservedDeviceName(System.String, System.String) Boolean IsAbsolutePath(System.String) System.String GetCommonBase(System.String, System.String) System.String CreateNormalizedRelativePathFromStack(System.Collections.Generic.Stack`1[System.String]) Boolean IsItemContainer(System.String) Boolean IsSameVolume(System.String, System.String) System.Object GetPropertyDynamicParameters(System.String, System.Collections.ObjectModel.Collection`1[System.String]) System.Object SetPropertyDynamicParameters(System.String, System.Management.Automation.PSObject) System.Object ClearPropertyDynamicParameters(System.String, System.Collections.ObjectModel.Collection`1[System.String]) System.Object GetContentWriterDynamicParameters(System.String) System.Object ClearContentDynamicParameters(System.String) Int32 SafeGetFileAttributes(System.String) System.Security.AccessControl.ObjectSecurity NewSecurityDescriptorFromPath(System.String, System.Security.AccessControl.AccessControlSections) System.Security.AccessControl.ObjectSecurity NewSecurityDescriptorOfType(System.String, System.Security.AccessControl.AccessControlSections) System.Security.AccessControl.ObjectSecurity NewSecurityDescriptor(ItemType) System.Management.Automation.ErrorRecord CreateErrorRecord(System.String, System.String) System.String GetHelpMaml(System.String, System.String) System.Management.Automation.ProviderInfo Start(System.Management.Automation.ProviderInfo) System.Management.Automation.PSDriveInfo NewDrive(System.Management.Automation.PSDriveInfo) Void MapNetworkDrive(System.Management.Automation.PSDriveInfo) System.Management.Automation.PSDriveInfo RemoveDrive(System.Management.Automation.PSDriveInfo) System.String GetUNCForNetworkDrive(System.String) System.String GetSubstitutedPathForNetworkDosDevice(System.String) Boolean IsValidPath(System.String) Void GetItem(System.String) System.IO.FileSystemInfo GetFileSystemItem(System.String, Boolean ByRef, Boolean) Boolean ConvertPath(System.String, System.String, System.String ByRef, System.String ByRef) Void GetPathItems(System.String, Boolean, UInt32, Boolean, System.Management.Automation.ReturnContainers) Void Dir(System.IO.DirectoryInfo, Boolean, UInt32, Boolean, System.Management.Automation.ReturnContainers, InodeTracker) System.Management.Automation.FlagsExpression`1[System.IO.FileAttributes] FormatAttributeSwitchParamters() System.String Mode(System.Management.Automation.PSObject) Void RenameItem(System.String, System.String) Void NewItem(System.String, System.String, System.Object) ItemType GetItemType(System.String) Void CreateDirectory(System.String, Boolean) Boolean CreateIntermediateDirectories(System.String) Void RemoveItem(System.String, Boolean) Void RemoveDirectoryInfoItem(System.IO.DirectoryInfo, Boolean, Boolean, Boolean) Void RemoveFileSystemItem(System.IO.FileSystemInfo, Boolean) Boolean ItemExists(System.String, System.Management.Automation.ErrorRecord ByRef) Boolean DirectoryInfoHasChildItems(System.IO.DirectoryInfo) Void CopyItem(System.String, System.String, Boolean) Void CopyItemFromRemoteSession(System.String, System.String, Boolean, Boolean, System.Management.Automation.Runspaces.PSSession) Void CopyDirectoryInfoItem(System.IO.DirectoryInfo, System.String, Boolean, Boolean, System.Management.Automation.PowerShell) Void CopyFileInfoItem(System.IO.FileInfo, System.String, Boolean, System.Management.Automation.PowerShell) Void CopyDirectoryFromRemoteSession(System.String, System.String, System.String, Boolean, Boolean, System.Management.Automation.PowerShell) System.Collections.ArrayList GetRemoteSourceAlternateStreams(System.Management.Automation.PowerShell, System.String) Void RemoveFunctionsPSCopyFileFromRemoteSession(System.Management.Automation.PowerShell) System.Collections.Hashtable GetRemoteFileMetadata(System.String, System.Management.Automation.PowerShell) Void SetFileMetadata(System.String, System.IO.FileInfo, System.Management.Automation.PowerShell) Void CopyFileFromRemoteSession(System.String, System.String, System.String, Boolean, System.Management.Automation.PowerShell, Int64) Boolean PerformCopyFileFromRemoteSession(System.String, System.IO.FileInfo, System.String, Boolean, System.Management.Automation.PowerShell, Int64, Boolean, System.String) Void RemoveFunctionPSCopyFileToRemoteSession(System.Management.Automation.PowerShell) Boolean RemoteTargetSupportsAlternateStreams(System.Management.Automation.PowerShell, System.String) System.String MakeRemotePath(System.Management.Automation.PowerShell, System.String, System.String) Boolean RemoteDirectoryExist(System.Management.Automation.PowerShell, System.String) Boolean CopyFileStreamToRemoteSession(System.IO.FileInfo, System.String, System.Management.Automation.PowerShell, Boolean, System.String) System.Collections.Hashtable GetFileMetadata(System.IO.FileInfo) Void SetRemoteFileMetadata(System.IO.FileInfo, System.String, System.Management.Automation.PowerShell) Boolean PerformCopyFileToRemoteSession(System.IO.FileInfo, System.String, System.Management.Automation.PowerShell) Boolean RemoteDestinationPathIsFile(System.String, System.Management.Automation.PowerShell) System.String CreateDirectoryOnRemoteSession(System.String, Boolean, System.Management.Automation.PowerShell) System.String GetParentPath(System.String, System.String) Boolean IsUNCPath(System.String) Boolean IsUNCRoot(System.String) Boolean IsPathRoot(System.String) System.String NormalizeRelativePath(System.String, System.String) System.String NormalizeRelativePathHelper(System.String, System.String) System.String RemoveRelativeTokens(System.String) System.Collections.Generic.Stack`1[System.String] TokenizePathToStack(System.String, System.String) System.Collections.Generic.Stack`1[System.String] NormalizeThePath(System.String, System.Collections.Generic.Stack`1[System.String]) System.String GetChildName(System.String) System.String EnsureDriveIsRooted(System.String) Void MoveItem(System.String, System.String) Void MoveFileInfoItem(System.IO.FileInfo, System.String, Boolean, Boolean) Void MoveDirectoryInfoItem(System.IO.DirectoryInfo, System.String, Boolean) Void CopyAndDelete(System.IO.DirectoryInfo, System.String, Boolean) Void GetProperty(System.String, System.Collections.ObjectModel.Collection`1[System.String]) Void SetProperty(System.String, System.Management.Automation.PSObject) Void ClearProperty(System.String, System.Collections.ObjectModel.Collection`1[System.String]) System.Management.Automation.Provider.IContentReader GetContentReader(System.String) System.Object GetContentReaderDynamicParameters(System.String) System.Management.Automation.Provider.IContentWriter GetContentWriter(System.String) Void ClearContent(System.String) Void ValidateParameters(Boolean) Void GetSecurityDescriptor(System.String, System.Security.AccessControl.AccessControlSections) Void SetSecurityDescriptor(System.String, System.Security.AccessControl.ObjectSecurity) Void SetSecurityDescriptor(System.String, System.Security.AccessControl.ObjectSecurity, System.Security.AccessControl.AccessControlSections) Void \u003cRemoveDirectoryInfoItem\u003eg__WriteErrorHelper|43_0(System.Exception, \u003c\u003ec__DisplayClass43_0 ByRef) Void .ctor() Void .cctor() System.Collections.ObjectModel.Collection`1[System.Management.Automation.WildcardPattern] excludeMatcher System.Management.Automation.PSTraceSource tracer Int32 FILETRANSFERSIZE System.String ProviderName Microsoft.PowerShell.Commands.FileSystemProvider+ItemType Microsoft.PowerShell.Commands.FileSystemProvider+NativeMethods Microsoft.PowerShell.Commands.FileSystemProvider+NetResource Microsoft.PowerShell.Commands.FileSystemProvider+InodeTracker Microsoft.PowerShell.Commands.FileSystemProvider+\u003c\u003ec__DisplayClass43_0",
                                                                                    "DeclaredMethods":  "System.String Mode(System.Management.Automation.PSObject) System.String NormalizePath(System.String) System.IO.FileSystemInfo GetFileSystemInfo(System.String, Boolean ByRef) Boolean IsFilterSet() System.Object GetChildNamesDynamicParameters(System.String) System.Object GetChildItemsDynamicParameters(System.String, Boolean) System.Object CopyItemDynamicParameters(System.String, System.String, Boolean) Boolean IsNetworkMappedDrive(System.Management.Automation.PSDriveInfo) Boolean IsSupportedDriveForPersistence(System.Management.Automation.PSDriveInfo) System.String GetRootPathForNetworkDriveOrDosDevice(System.IO.DriveInfo) System.Collections.ObjectModel.Collection`1[System.Management.Automation.PSDriveInfo] InitializeDefaultDrives() System.Object GetItemDynamicParameters(System.String) Void InvokeDefaultAction(System.String) Void GetChildItems(System.String, Boolean, UInt32) Void GetChildNames(System.String, System.Management.Automation.ReturnContainers) Boolean CheckItemExists(System.String, Boolean ByRef) System.Object RemoveItemDynamicParameters(System.String, Boolean) Void RemoveFileInfoItem(System.IO.FileInfo, Boolean) Boolean ItemExists(System.String) System.Object ItemExistsDynamicParameters(System.String) Boolean HasChildItems(System.String) Void CopyItemLocalOrToSession(System.String, System.String, Boolean, Boolean, System.Management.Automation.PowerShell) Void InitilizeFunctionPSCopyFileFromRemoteSession(System.Management.Automation.PowerShell) Boolean ValidRemoteSessionForScripting(System.Management.Automation.Runspaces.Runspace) Void InitilizeFunctionsPSCopyFileToRemoteSession(System.Management.Automation.PowerShell) Boolean PathIsReservedDeviceName(System.String, System.String) Boolean IsAbsolutePath(System.String) System.String GetCommonBase(System.String, System.String) System.String CreateNormalizedRelativePathFromStack(System.Collections.Generic.Stack`1[System.String]) Boolean IsItemContainer(System.String) Boolean IsSameVolume(System.String, System.String) System.Object GetPropertyDynamicParameters(System.String, System.Collections.ObjectModel.Collection`1[System.String]) System.Object SetPropertyDynamicParameters(System.String, System.Management.Automation.PSObject) System.Object ClearPropertyDynamicParameters(System.String, System.Collections.ObjectModel.Collection`1[System.String]) System.Object GetContentWriterDynamicParameters(System.String) System.Object ClearContentDynamicParameters(System.String) Int32 SafeGetFileAttributes(System.String) System.Security.AccessControl.ObjectSecurity NewSecurityDescriptorFromPath(System.String, System.Security.AccessControl.AccessControlSections) System.Security.AccessControl.ObjectSecurity NewSecurityDescriptorOfType(System.String, System.Security.AccessControl.AccessControlSections) System.Security.AccessControl.ObjectSecurity NewSecurityDescriptor(ItemType) System.Management.Automation.ErrorRecord CreateErrorRecord(System.String, System.String) System.String GetHelpMaml(System.String, System.String) System.Management.Automation.ProviderInfo Start(System.Management.Automation.ProviderInfo) System.Management.Automation.PSDriveInfo NewDrive(System.Management.Automation.PSDriveInfo) Void MapNetworkDrive(System.Management.Automation.PSDriveInfo) System.Management.Automation.PSDriveInfo RemoveDrive(System.Management.Automation.PSDriveInfo) System.String GetUNCForNetworkDrive(System.String) System.String GetSubstitutedPathForNetworkDosDevice(System.String) Boolean IsValidPath(System.String) Void GetItem(System.String) System.IO.FileSystemInfo GetFileSystemItem(System.String, Boolean ByRef, Boolean) Boolean ConvertPath(System.String, System.String, System.String ByRef, System.String ByRef) Void GetPathItems(System.String, Boolean, UInt32, Boolean, System.Management.Automation.ReturnContainers) Void Dir(System.IO.DirectoryInfo, Boolean, UInt32, Boolean, System.Management.Automation.ReturnContainers, InodeTracker) System.Management.Automation.FlagsExpression`1[System.IO.FileAttributes] FormatAttributeSwitchParamters() Void RenameItem(System.String, System.String) Void NewItem(System.String, System.String, System.Object) ItemType GetItemType(System.String) Void CreateDirectory(System.String, Boolean) Boolean CreateIntermediateDirectories(System.String) Void RemoveItem(System.String, Boolean) Void RemoveDirectoryInfoItem(System.IO.DirectoryInfo, Boolean, Boolean, Boolean) Void RemoveFileSystemItem(System.IO.FileSystemInfo, Boolean) Boolean ItemExists(System.String, System.Management.Automation.ErrorRecord ByRef) Boolean DirectoryInfoHasChildItems(System.IO.DirectoryInfo) Void CopyItem(System.String, System.String, Boolean) Void CopyItemFromRemoteSession(System.String, System.String, Boolean, Boolean, System.Management.Automation.Runspaces.PSSession) Void CopyDirectoryInfoItem(System.IO.DirectoryInfo, System.String, Boolean, Boolean, System.Management.Automation.PowerShell) Void CopyFileInfoItem(System.IO.FileInfo, System.String, Boolean, System.Management.Automation.PowerShell) Void CopyDirectoryFromRemoteSession(System.String, System.String, System.String, Boolean, Boolean, System.Management.Automation.PowerShell) System.Collections.ArrayList GetRemoteSourceAlternateStreams(System.Management.Automation.PowerShell, System.String) Void RemoveFunctionsPSCopyFileFromRemoteSession(System.Management.Automation.PowerShell) System.Collections.Hashtable GetRemoteFileMetadata(System.String, System.Management.Automation.PowerShell) Void SetFileMetadata(System.String, System.IO.FileInfo, System.Management.Automation.PowerShell) Void CopyFileFromRemoteSession(System.String, System.String, System.String, Boolean, System.Management.Automation.PowerShell, Int64) Boolean PerformCopyFileFromRemoteSession(System.String, System.IO.FileInfo, System.String, Boolean, System.Management.Automation.PowerShell, Int64, Boolean, System.String) Void RemoveFunctionPSCopyFileToRemoteSession(System.Management.Automation.PowerShell) Boolean RemoteTargetSupportsAlternateStreams(System.Management.Automation.PowerShell, System.String) System.String MakeRemotePath(System.Management.Automation.PowerShell, System.String, System.String) Boolean RemoteDirectoryExist(System.Management.Automation.PowerShell, System.String) Boolean CopyFileStreamToRemoteSession(System.IO.FileInfo, System.String, System.Management.Automation.PowerShell, Boolean, System.String) System.Collections.Hashtable GetFileMetadata(System.IO.FileInfo) Void SetRemoteFileMetadata(System.IO.FileInfo, System.String, System.Management.Automation.PowerShell) Boolean PerformCopyFileToRemoteSession(System.IO.FileInfo, System.String, System.Management.Automation.PowerShell) Boolean RemoteDestinationPathIsFile(System.String, System.Management.Automation.PowerShell) System.String CreateDirectoryOnRemoteSession(System.String, Boolean, System.Management.Automation.PowerShell) System.String GetParentPath(System.String, System.String) Boolean IsUNCPath(System.String) Boolean IsUNCRoot(System.String) Boolean IsPathRoot(System.String) System.String NormalizeRelativePath(System.String, System.String) System.String NormalizeRelativePathHelper(System.String, System.String) System.String RemoveRelativeTokens(System.String) System.Collections.Generic.Stack`1[System.String] TokenizePathToStack(System.String, System.String) System.Collections.Generic.Stack`1[System.String] NormalizeThePath(System.String, System.Collections.Generic.Stack`1[System.String]) System.String GetChildName(System.String) System.String EnsureDriveIsRooted(System.String) Void MoveItem(System.String, System.String) Void MoveFileInfoItem(System.IO.FileInfo, System.String, Boolean, Boolean) Void MoveDirectoryInfoItem(System.IO.DirectoryInfo, System.String, Boolean) Void CopyAndDelete(System.IO.DirectoryInfo, System.String, Boolean) Void GetProperty(System.String, System.Collections.ObjectModel.Collection`1[System.String]) Void SetProperty(System.String, System.Management.Automation.PSObject) Void ClearProperty(System.String, System.Collections.ObjectModel.Collection`1[System.String]) System.Management.Automation.Provider.IContentReader GetContentReader(System.String) System.Object GetContentReaderDynamicParameters(System.String) System.Management.Automation.Provider.IContentWriter GetContentWriter(System.String) Void ClearContent(System.String) Void ValidateParameters(Boolean) Void GetSecurityDescriptor(System.String, System.Security.AccessControl.AccessControlSections) Void SetSecurityDescriptor(System.String, System.Security.AccessControl.ObjectSecurity) Void SetSecurityDescriptor(System.String, System.Security.AccessControl.ObjectSecurity, System.Security.AccessControl.AccessControlSections) Void \u003cRemoveDirectoryInfoItem\u003eg__WriteErrorHelper|43_0(System.Exception, \u003c\u003ec__DisplayClass43_0 ByRef)",
                                                                                    "DeclaredNestedTypes":  "Microsoft.PowerShell.Commands.FileSystemProvider+ItemType Microsoft.PowerShell.Commands.FileSystemProvider+NativeMethods Microsoft.PowerShell.Commands.FileSystemProvider+NetResource Microsoft.PowerShell.Commands.FileSystemProvider+InodeTracker Microsoft.PowerShell.Commands.FileSystemProvider+\u003c\u003ec__DisplayClass43_0",
                                                                                    "DeclaredProperties":  "",
                                                                                    "ImplementedInterfaces":  "System.Management.Automation.IResourceSupplier System.Management.Automation.Provider.IContentCmdletProvider System.Management.Automation.Provider.IPropertyCmdletProvider System.Management.Automation.Provider.ISecurityDescriptorCmdletProvider System.Management.Automation.Provider.ICmdletProviderSupportsHelp",
                                                                                    "TypeInitializer":  "Void .cctor()",
                                                                                    "IsNested":  false,
                                                                                    "Attributes":  1048833,
                                                                                    "IsVisible":  true,
                                                                                    "IsNotPublic":  false,
                                                                                    "IsPublic":  true,
                                                                                    "IsNestedPublic":  false,
                                                                                    "IsNestedPrivate":  false,
                                                                                    "IsNestedFamily":  false,
                                                                                    "IsNestedAssembly":  false,
                                                                                    "IsNestedFamANDAssem":  false,
                                                                                    "IsNestedFamORAssem":  false,
                                                                                    "IsAutoLayout":  true,
                                                                                    "IsLayoutSequential":  false,
                                                                                    "IsExplicitLayout":  false,
                                                                                    "IsClass":  true,
                                                                                    "IsInterface":  false,
                                                                                    "IsValueType":  false,
                                                                                    "IsAbstract":  false,
                                                                                    "IsSealed":  true,
                                                                                    "IsSpecialName":  false,
                                                                                    "IsImport":  false,
                                                                                    "IsSerializable":  false,
                                                                                    "IsAnsiClass":  true,
                                                                                    "IsUnicodeClass":  false,
                                                                                    "IsAutoClass":  false,
                                                                                    "IsArray":  false,
                                                                                    "IsByRef":  false,
                                                                                    "IsPointer":  false,
                                                                                    "IsPrimitive":  false,
                                                                                    "IsCOMObject":  false,
                                                                                    "HasElementType":  false,
                                                                                    "IsContextful":  false,
                                                                                    "IsMarshalByRef":  false,
                                                                                    "GenericTypeArguments":  "",
                                                                                    "CustomAttributes":  "[System.Management.Automation.Provider.CmdletProviderAttribute(\"FileSystem\", (System.Management.Automation.Provider.ProviderCapabilities)52)] [System.Management.Automation.OutputTypeAttribute(typeof(System.Security.AccessControl.FileSecurity), ProviderCmdlet = \"Set-Acl\")] [System.Management.Automation.OutputTypeAttribute(new Type[2] { typeof(System.String), typeof(System.Management.Automation.PathInfo) }, ProviderCmdlet = \"Resolve-Path\")] [System.Management.Automation.OutputTypeAttribute(typeof(System.Management.Automation.PathInfo), ProviderCmdlet = \"Push-Location\")] [System.Management.Automation.OutputTypeAttribute(new Type[2] { typeof(System.Byte), typeof(System.String) }, ProviderCmdlet = \"Get-Content\")] [System.Management.Automation.OutputTypeAttribute(typeof(System.IO.FileInfo), ProviderCmdlet = \"Get-Item\")] [System.Management.Automation.OutputTypeAttribute(new Type[2] { typeof(System.IO.FileInfo), typeof(System.IO.DirectoryInfo) }, ProviderCmdlet = \"Get-ChildItem\")] [System.Management.Automation.OutputTypeAttribute(new Type[2] { typeof(System.Security.AccessControl.FileSecurity), typeof(System.Security.AccessControl.DirectorySecurity) }, ProviderCmdlet = \"Get-Acl\")] [System.Management.Automation.OutputTypeAttribute(new Type[4] { typeof(System.Boolean), typeof(System.String), typeof(System.IO.FileInfo), typeof(System.IO.DirectoryInfo) }, ProviderCmdlet = \"Get-Item\")] [System.Management.Automation.OutputTypeAttribute(new Type[5] { typeof(System.Boolean), typeof(System.String), typeof(System.DateTime), typeof(System.IO.FileInfo), typeof(System.IO.DirectoryInfo) }, ProviderCmdlet = \"Get-ItemProperty\")] [System.Management.Automation.OutputTypeAttribute(new Type[2] { typeof(System.String), typeof(System.IO.FileInfo) }, ProviderCmdlet = \"New-Item\")]"
                                                                                },
                                                           "HelpFile":  "System.Management.Automation.dll-Help.xml",
                                                           "Name":  "FileSystem",
                                                           "PSSnapIn":  {
                                                                            "Name":  "Microsoft.PowerShell.Core",
                                                                            "IsDefault":  true,
                                                                            "ApplicationBase":  "C:\\Windows\\System32\\WindowsPowerShell\\v1.0",
                                                                            "AssemblyName":  "System.Management.Automation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, ProcessorArchitecture=MSIL",
                                                                            "ModuleName":  "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\System.Management.Automation.dll",
                                                                            "PSVersion":  "5.1.22621.4391",
                                                                            "Version":  "*******",
                                                                            "Types":  "types.ps1xml typesv3.ps1xml",
                                                                            "Formats":  "Certificate.format.ps1xml DotNetTypes.format.ps1xml FileSystem.format.ps1xml Help.format.ps1xml HelpV3.format.ps1xml PowerShellCore.format.ps1xml PowerShellTrace.format.ps1xml Registry.format.ps1xml",
                                                                            "Description":  "此 Windows PowerShell 管理单元包含用于管理 Windows PowerShell 组件的 cmdlet。",
                                                                            "Vendor":  "Microsoft Corporation",
                                                                            "LogPipelineExecutionDetails":  false
                                                                        },
                                                           "ModuleName":  "Microsoft.PowerShell.Core",
                                                           "Module":  null,
                                                           "Description":  "",
                                                           "Capabilities":  52,
                                                           "Home":  "C:\\Users\\<USER>\r\n\r\n\u003e enterprise-website@0.1.0 format:check\r\n\u003e prettier --check .\r\n\r\nChecking formatting...\r\n[warn] .cicd/docker/daemon.json\r\n[warn] .cicd/docker/docker-compose.prod.yml\r\n[warn] .cicd/docker/docker-compose.yml\r\n[warn] .cicd/docker/README-NETWORK-FIX.md\r\n[warn] .cicd/docs/best-practices.md\r\n[warn] .cicd/docs/dependency-fix-summary.md\r\n[warn] .cicd/docs/gitlab-ci-yaml-fix-summary.md\r\n[warn] .cicd/docs/powershell-encoding-fix.md\r\n[warn] .cicd/docs/powershell-syntax-fixes.md\r\n[warn] .cicd/docs/README.md\r\n[warn] .cicd/docs/runner-tags-configuration.md\r\n[warn] .cicd/docs/troubleshooting-dependencies.md\r\n[warn] .cicd/docs/troubleshooting.md\r\n[warn] .cicd/docs/windows-unified-cicd.md\r\n[warn] .cicd/README.md\r\n[warn] .cicd/security/container/hadolint.yml\r\n[warn] .cicd/security/container/trivy.yml\r\n[warn] .cicd/security/dependency/dependency-check.yml\r\n[warn] .cicd/security/sast/.eslintrc.security.js\r\n[warn] .cicd/testing/e2e/global-setup.ts\r\n[warn] .cicd/testing/e2e/global-teardown.ts\r\n[warn] .cicd/testing/e2e/playwright.config.ts\r\n[warn] .cicd/testing/jest/__mocks__/fileMock.js\r\n[warn] .cicd/testing/jest/jest.config.js\r\n[warn] .cicd/testing/jest/jest.setup.js\r\n[warn] .cicd/testing/performance/lighthouse.config.js\r\n[warn] .eslintrc.json\r\n[error] .gitlab-ci.yml: SyntaxError: All collection items must start at the same column (180:7)\r\n[error]   178 |     - echo \"Lint and format check completed\"\r\n[error]   179 |     - echo \"Checking final status...\"\r\n[error] \u003e 180 |     - PowerShell.exe -ExecutionPolicy Bypass -Command \"\r\n[error]       |       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 181 |         if (Test-Path \u0027lint-results.json\u0027) {\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 182 |           $report = Get-Content \u0027lint-results.json\u0027 | ConvertFrom-Json;\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 183 |           $overallStatus = $report.summary.overall.status;\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 184 |           Write-Host \\\"Overall status: $overallStatus\\\" -ForegroundColor $(if ($overallStatus -eq \u0027passed\u0027) { \u0027Green\u0027 } else { \u0027Red\u0027 });\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 185 |           if ($overallStatus -eq \u0027failed\u0027) {\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 186 |             Write-Host \u0027Code quality checks failed. Please review the reports.\u0027 -ForegroundColor Red;\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 187 |             exit 1;\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 188 |           }\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 189 |         }\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 190 |       \"\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 191 |   artifacts:\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 192 |     when: always\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 193 |     reports:\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 194 |       junit: lint-results.xml\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 195 |     paths:\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 196 |       - lint-results.json\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 197 |       - lint-results.xml\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 198 |       - lint-summary.md\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 199 |       - lint-output.txt\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 200 |       - format-check-output.txt\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 201 |     expire_in: 1 week\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 202 |   only:\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 203 |     - main\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 204 |     - develop\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 205 |     - merge_requests\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 206 |\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 207 | test:type-check:\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 208 |   stage: test\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 209 |   tags:\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 210 |     - windows\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 211 |     - shell\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 212 |   dependencies:\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 213 |     - prepare:dependencies\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 214 |   before_script:\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 215 |     - PowerShell.exe -ExecutionPolicy Bypass -File \".cicd\\scripts\\setup-powershell-environment.ps1\"\r\n[error]       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n[error] \u003e 216 |     - echo \"Starting CI/CD Pipeline - $(Get-Date)\"\r\n[error]       | ^\r\n[error]   217 |     - echo \"Project - $env:APP_NAME\"\r\n[error]   218 |     - echo \"Branch - $env:CI_COMMIT_REF_NAME\"\r\n[error]   219 |     - echo \"Commit - $env:CI_COMMIT_SHORT_SHA\"\r\n[warn] GIT_SETUP.md\r\n[warn] next-env.d.ts\r\n[warn] next.config.js\r\n[warn] next.config.security.js\r\n[warn] package-lock.json\r\n[warn] package.json\r\n[warn] postcss.config.js\r\n[warn] README.md\r\n[warn] SECURITY.md\r\n[warn] src/app/about/page.tsx\r\n[warn] src/app/admin/stats/page.tsx\r\n[warn] src/app/api/contact/route.ts\r\n[warn] src/app/api/health/route.ts\r\n[warn] src/app/api/hege-cases/route.ts\r\n[warn] src/app/api/hege-news/route.ts\r\n[warn] src/app/api/success-cases/route.ts\r\n[warn] src/app/api/tencent-news/route.ts\r\n[warn] src/app/api/upload/route.ts\r\n[warn] src/app/api/visitor-stats/route.ts\r\n[warn] src/app/api/wechat-integration/route.ts\r\n[warn] src/app/cases/page.tsx\r\n[warn] src/app/cloud-services/page.tsx\r\n[warn] src/app/contact/page.tsx\r\n[warn] src/app/layout.tsx\r\n[warn] src/app/news-integrated/page.tsx\r\n[warn] src/app/news/page.tsx\r\n[warn] src/app/page.tsx\r\n[warn] src/app/security-test/page.tsx\r\n[warn] src/app/services/page.tsx\r\n[warn] src/components/ChatBot.tsx\r\n[warn] src/components/Navigation.tsx\r\n[warn] src/components/OurCustomers.tsx\r\n[warn] src/components/SuccessCases.tsx\r\n[warn] src/components/TencentNews.tsx\r\n[warn] src/components/Timeline.tsx\r\n[warn] src/components/ui/button.tsx\r\n[warn] src/components/ui/card.tsx\r\n[warn] src/components/WeChatQR.tsx\r\n[warn] src/lib/security-config.ts\r\n[warn] src/lib/security.ts\r\n[warn] src/lib/utils.ts\r\n[warn] src/middleware.ts\r\n[warn] src/styles/globals.css\r\n[warn] src/types/index.ts\r\n[warn] tailwind.config.js\r\n[warn] tsconfig.json\r\n[warn] VERSION_MANAGEMENT.md\r\n[warn] WECHAT_INTEGRATION_SUMMARY.md\r\nError occurred when checking code style in the above file.\r\n",
                                          "PSPath":  "D:\\Projects\\hege-tech-web\\format-check-output.txt",
                                          "PSParentPath":  "D:\\Projects\\hege-tech-web",
                                          "PSChildName":  "format-check-output.txt",
                                          "PSDrive":  {
                                                          "CurrentLocation":  "Projects\\hege-tech-web",
                                                          "Name":  "D",
                                                          "Provider":  {
                                                                           "ImplementingType":  "Microsoft.PowerShell.Commands.FileSystemProvider",
                                                                           "HelpFile":  "System.Management.Automation.dll-Help.xml",
                                                                           "Name":  "FileSystem",
                                                                           "PSSnapIn":  "Microsoft.PowerShell.Core",
                                                                           "ModuleName":  "Microsoft.PowerShell.Core",
                                                                           "Module":  null,
                                                                           "Description":  "",
                                                                           "Capabilities":  52,
                                                                           "Home":  "C:\\Users\\<USER>\\",
                                                          "Description":  "Data",
                                                          "MaximumSize":  null,
                                                          "Credential":  {
                                                                             "UserName":  null,
                                                                             "Password":  null
                                                                         },
                                                          "DisplayRoot":  null
                                                      },
                                          "PSProvider":  {
                                                             "ImplementingType":  {
                                                                                      "Module":  "System.Management.Automation.dll",
                                                                                      "Assembly":  "System.Management.Automation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35",
                                                                                      "TypeHandle":  "System.RuntimeTypeHandle",
                                                                                      "DeclaringMethod":  null,
                                                                                      "BaseType":  "System.Management.Automation.Provider.NavigationCmdletProvider",
                                                                                      "UnderlyingSystemType":  "Microsoft.PowerShell.Commands.FileSystemProvider",
                                                                                      "FullName":  "Microsoft.PowerShell.Commands.FileSystemProvider",
                                                                                      "AssemblyQualifiedName":  "Microsoft.PowerShell.Commands.FileSystemProvider, System.Management.Automation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35",
                                                                                      "Namespace":  "Microsoft.PowerShell.Commands",
                                                                                      "GUID":  "b4755d19-b6a7-38dc-ae06-4167f801062f",
                                                                                      "IsEnum":  false,
                                                                                      "GenericParameterAttributes":  null,
                                                                                      "IsSecurityCritical":  true,
                                                                                      "IsSecuritySafeCritical":  false,
                                                                                      "IsSecurityTransparent":  false,
                                                                                      "IsGenericTypeDefinition":  false,
                                                                                      "IsGenericParameter":  false,
                                                                                      "GenericParameterPosition":  null,
                                                                                      "IsGenericType":  false,
                                                                                      "IsConstructedGenericType":  false,
                                                                                      "ContainsGenericParameters":  false,
                                                                                      "StructLayoutAttribute":  "System.Runtime.InteropServices.StructLayoutAttribute",
                                                                                      "Name":  "FileSystemProvider",
                                                                                      "MemberType":  32,
                                                                                      "DeclaringType":  null,
                                                                                      "ReflectedType":  null,
                                                                                      "MetadataToken":  33554727,
                                                                                      "GenericTypeParameters":  "",
                                                                                      "DeclaredConstructors":  "Void .ctor() Void .cctor()",
                                                                                      "DeclaredEvents":  "",
                                                                                      "DeclaredFields":  "System.Collections.ObjectModel.Collection`1[System.Management.Automation.WildcardPattern] excludeMatcher System.Management.Automation.PSTraceSource tracer Int32 FILETRANSFERSIZE System.String ProviderName",
                                                                                      "DeclaredMembers":  "System.String Mode(System.Management.Automation.PSObject) System.String NormalizePath(System.String) System.IO.FileSystemInfo GetFileSystemInfo(System.String, Boolean ByRef) Boolean IsFilterSet() System.Object GetChildNamesDynamicParameters(System.String) System.Object GetChildItemsDynamicParameters(System.String, Boolean) System.Object CopyItemDynamicParameters(System.String, System.String, Boolean) Boolean IsNetworkMappedDrive(System.Management.Automation.PSDriveInfo) Boolean IsSupportedDriveForPersistence(System.Management.Automation.PSDriveInfo) System.String GetRootPathForNetworkDriveOrDosDevice(System.IO.DriveInfo) System.Collections.ObjectModel.Collection`1[System.Management.Automation.PSDriveInfo] InitializeDefaultDrives() System.Object GetItemDynamicParameters(System.String) Void InvokeDefaultAction(System.String) Void GetChildItems(System.String, Boolean, UInt32) Void GetChildNames(System.String, System.Management.Automation.ReturnContainers) Boolean CheckItemExists(System.String, Boolean ByRef) System.Object RemoveItemDynamicParameters(System.String, Boolean) Void RemoveFileInfoItem(System.IO.FileInfo, Boolean) Boolean ItemExists(System.String) System.Object ItemExistsDynamicParameters(System.String) Boolean HasChildItems(System.String) Void CopyItemLocalOrToSession(System.String, System.String, Boolean, Boolean, System.Management.Automation.PowerShell) Void InitilizeFunctionPSCopyFileFromRemoteSession(System.Management.Automation.PowerShell) Boolean ValidRemoteSessionForScripting(System.Management.Automation.Runspaces.Runspace) Void InitilizeFunctionsPSCopyFileToRemoteSession(System.Management.Automation.PowerShell) Boolean PathIsReservedDeviceName(System.String, System.String) Boolean IsAbsolutePath(System.String) System.String GetCommonBase(System.String, System.String) System.String CreateNormalizedRelativePathFromStack(System.Collections.Generic.Stack`1[System.String]) Boolean IsItemContainer(System.String) Boolean IsSameVolume(System.String, System.String) System.Object GetPropertyDynamicParameters(System.String, System.Collections.ObjectModel.Collection`1[System.String]) System.Object SetPropertyDynamicParameters(System.String, System.Management.Automation.PSObject) System.Object ClearPropertyDynamicParameters(System.String, System.Collections.ObjectModel.Collection`1[System.String]) System.Object GetContentWriterDynamicParameters(System.String) System.Object ClearContentDynamicParameters(System.String) Int32 SafeGetFileAttributes(System.String) System.Security.AccessControl.ObjectSecurity NewSecurityDescriptorFromPath(System.String, System.Security.AccessControl.AccessControlSections) System.Security.AccessControl.ObjectSecurity NewSecurityDescriptorOfType(System.String, System.Security.AccessControl.AccessControlSections) System.Security.AccessControl.ObjectSecurity NewSecurityDescriptor(ItemType) System.Management.Automation.ErrorRecord CreateErrorRecord(System.String, System.String) System.String GetHelpMaml(System.String, System.String) System.Management.Automation.ProviderInfo Start(System.Management.Automation.ProviderInfo) System.Management.Automation.PSDriveInfo NewDrive(System.Management.Automation.PSDriveInfo) Void MapNetworkDrive(System.Management.Automation.PSDriveInfo) System.Management.Automation.PSDriveInfo RemoveDrive(System.Management.Automation.PSDriveInfo) System.String GetUNCForNetworkDrive(System.String) System.String GetSubstitutedPathForNetworkDosDevice(System.String) Boolean IsValidPath(System.String) Void GetItem(System.String) System.IO.FileSystemInfo GetFileSystemItem(System.String, Boolean ByRef, Boolean) Boolean ConvertPath(System.String, System.String, System.String ByRef, System.String ByRef) Void GetPathItems(System.String, Boolean, UInt32, Boolean, System.Management.Automation.ReturnContainers) Void Dir(System.IO.DirectoryInfo, Boolean, UInt32, Boolean, System.Management.Automation.ReturnContainers, InodeTracker) System.Management.Automation.FlagsExpression`1[System.IO.FileAttributes] FormatAttributeSwitchParamters() Void RenameItem(System.String, System.String) Void NewItem(System.String, System.String, System.Object) ItemType GetItemType(System.String) Void CreateDirectory(System.String, Boolean) Boolean CreateIntermediateDirectories(System.String) Void RemoveItem(System.String, Boolean) Void RemoveDirectoryInfoItem(System.IO.DirectoryInfo, Boolean, Boolean, Boolean) Void RemoveFileSystemItem(System.IO.FileSystemInfo, Boolean) Boolean ItemExists(System.String, System.Management.Automation.ErrorRecord ByRef) Boolean DirectoryInfoHasChildItems(System.IO.DirectoryInfo) Void CopyItem(System.String, System.String, Boolean) Void CopyItemFromRemoteSession(System.String, System.String, Boolean, Boolean, System.Management.Automation.Runspaces.PSSession) Void CopyDirectoryInfoItem(System.IO.DirectoryInfo, System.String, Boolean, Boolean, System.Management.Automation.PowerShell) Void CopyFileInfoItem(System.IO.FileInfo, System.String, Boolean, System.Management.Automation.PowerShell) Void CopyDirectoryFromRemoteSession(System.String, System.String, System.String, Boolean, Boolean, System.Management.Automation.PowerShell) System.Collections.ArrayList GetRemoteSourceAlternateStreams(System.Management.Automation.PowerShell, System.String) Void RemoveFunctionsPSCopyFileFromRemoteSession(System.Management.Automation.PowerShell) System.Collections.Hashtable GetRemoteFileMetadata(System.String, System.Management.Automation.PowerShell) Void SetFileMetadata(System.String, System.IO.FileInfo, System.Management.Automation.PowerShell) Void CopyFileFromRemoteSession(System.String, System.String, System.String, Boolean, System.Management.Automation.PowerShell, Int64) Boolean PerformCopyFileFromRemoteSession(System.String, System.IO.FileInfo, System.String, Boolean, System.Management.Automation.PowerShell, Int64, Boolean, System.String) Void RemoveFunctionPSCopyFileToRemoteSession(System.Management.Automation.PowerShell) Boolean RemoteTargetSupportsAlternateStreams(System.Management.Automation.PowerShell, System.String) System.String MakeRemotePath(System.Management.Automation.PowerShell, System.String, System.String) Boolean RemoteDirectoryExist(System.Management.Automation.PowerShell, System.String) Boolean CopyFileStreamToRemoteSession(System.IO.FileInfo, System.String, System.Management.Automation.PowerShell, Boolean, System.String) System.Collections.Hashtable GetFileMetadata(System.IO.FileInfo) Void SetRemoteFileMetadata(System.IO.FileInfo, System.String, System.Management.Automation.PowerShell) Boolean PerformCopyFileToRemoteSession(System.IO.FileInfo, System.String, System.Management.Automation.PowerShell) Boolean RemoteDestinationPathIsFile(System.String, System.Management.Automation.PowerShell) System.String CreateDirectoryOnRemoteSession(System.String, Boolean, System.Management.Automation.PowerShell) System.String GetParentPath(System.String, System.String) Boolean IsUNCPath(System.String) Boolean IsUNCRoot(System.String) Boolean IsPathRoot(System.String) System.String NormalizeRelativePath(System.String, System.String) System.String NormalizeRelativePathHelper(System.String, System.String) System.String RemoveRelativeTokens(System.String) System.Collections.Generic.Stack`1[System.String] TokenizePathToStack(System.String, System.String) System.Collections.Generic.Stack`1[System.String] NormalizeThePath(System.String, System.Collections.Generic.Stack`1[System.String]) System.String GetChildName(System.String) System.String EnsureDriveIsRooted(System.String) Void MoveItem(System.String, System.String) Void MoveFileInfoItem(System.IO.FileInfo, System.String, Boolean, Boolean) Void MoveDirectoryInfoItem(System.IO.DirectoryInfo, System.String, Boolean) Void CopyAndDelete(System.IO.DirectoryInfo, System.String, Boolean) Void GetProperty(System.String, System.Collections.ObjectModel.Collection`1[System.String]) Void SetProperty(System.String, System.Management.Automation.PSObject) Void ClearProperty(System.String, System.Collections.ObjectModel.Collection`1[System.String]) System.Management.Automation.Provider.IContentReader GetContentReader(System.String) System.Object GetContentReaderDynamicParameters(System.String) System.Management.Automation.Provider.IContentWriter GetContentWriter(System.String) Void ClearContent(System.String) Void ValidateParameters(Boolean) Void GetSecurityDescriptor(System.String, System.Security.AccessControl.AccessControlSections) Void SetSecurityDescriptor(System.String, System.Security.AccessControl.ObjectSecurity) Void SetSecurityDescriptor(System.String, System.Security.AccessControl.ObjectSecurity, System.Security.AccessControl.AccessControlSections) Void \u003cRemoveDirectoryInfoItem\u003eg__WriteErrorHelper|43_0(System.Exception, \u003c\u003ec__DisplayClass43_0 ByRef) Void .ctor() Void .cctor() System.Collections.ObjectModel.Collection`1[System.Management.Automation.WildcardPattern] excludeMatcher System.Management.Automation.PSTraceSource tracer Int32 FILETRANSFERSIZE System.String ProviderName Microsoft.PowerShell.Commands.FileSystemProvider+ItemType Microsoft.PowerShell.Commands.FileSystemProvider+NativeMethods Microsoft.PowerShell.Commands.FileSystemProvider+NetResource Microsoft.PowerShell.Commands.FileSystemProvider+InodeTracker Microsoft.PowerShell.Commands.FileSystemProvider+\u003c\u003ec__DisplayClass43_0",
                                                                                      "DeclaredMethods":  "System.String Mode(System.Management.Automation.PSObject) System.String NormalizePath(System.String) System.IO.FileSystemInfo GetFileSystemInfo(System.String, Boolean ByRef) Boolean IsFilterSet() System.Object GetChildNamesDynamicParameters(System.String) System.Object GetChildItemsDynamicParameters(System.String, Boolean) System.Object CopyItemDynamicParameters(System.String, System.String, Boolean) Boolean IsNetworkMappedDrive(System.Management.Automation.PSDriveInfo) Boolean IsSupportedDriveForPersistence(System.Management.Automation.PSDriveInfo) System.String GetRootPathForNetworkDriveOrDosDevice(System.IO.DriveInfo) System.Collections.ObjectModel.Collection`1[System.Management.Automation.PSDriveInfo] InitializeDefaultDrives() System.Object GetItemDynamicParameters(System.String) Void InvokeDefaultAction(System.String) Void GetChildItems(System.String, Boolean, UInt32) Void GetChildNames(System.String, System.Management.Automation.ReturnContainers) Boolean CheckItemExists(System.String, Boolean ByRef) System.Object RemoveItemDynamicParameters(System.String, Boolean) Void RemoveFileInfoItem(System.IO.FileInfo, Boolean) Boolean ItemExists(System.String) System.Object ItemExistsDynamicParameters(System.String) Boolean HasChildItems(System.String) Void CopyItemLocalOrToSession(System.String, System.String, Boolean, Boolean, System.Management.Automation.PowerShell) Void InitilizeFunctionPSCopyFileFromRemoteSession(System.Management.Automation.PowerShell) Boolean ValidRemoteSessionForScripting(System.Management.Automation.Runspaces.Runspace) Void InitilizeFunctionsPSCopyFileToRemoteSession(System.Management.Automation.PowerShell) Boolean PathIsReservedDeviceName(System.String, System.String) Boolean IsAbsolutePath(System.String) System.String GetCommonBase(System.String, System.String) System.String CreateNormalizedRelativePathFromStack(System.Collections.Generic.Stack`1[System.String]) Boolean IsItemContainer(System.String) Boolean IsSameVolume(System.String, System.String) System.Object GetPropertyDynamicParameters(System.String, System.Collections.ObjectModel.Collection`1[System.String]) System.Object SetPropertyDynamicParameters(System.String, System.Management.Automation.PSObject) System.Object ClearPropertyDynamicParameters(System.String, System.Collections.ObjectModel.Collection`1[System.String]) System.Object GetContentWriterDynamicParameters(System.String) System.Object ClearContentDynamicParameters(System.String) Int32 SafeGetFileAttributes(System.String) System.Security.AccessControl.ObjectSecurity NewSecurityDescriptorFromPath(System.String, System.Security.AccessControl.AccessControlSections) System.Security.AccessControl.ObjectSecurity NewSecurityDescriptorOfType(System.String, System.Security.AccessControl.AccessControlSections) System.Security.AccessControl.ObjectSecurity NewSecurityDescriptor(ItemType) System.Management.Automation.ErrorRecord CreateErrorRecord(System.String, System.String) System.String GetHelpMaml(System.String, System.String) System.Management.Automation.ProviderInfo Start(System.Management.Automation.ProviderInfo) System.Management.Automation.PSDriveInfo NewDrive(System.Management.Automation.PSDriveInfo) Void MapNetworkDrive(System.Management.Automation.PSDriveInfo) System.Management.Automation.PSDriveInfo RemoveDrive(System.Management.Automation.PSDriveInfo) System.String GetUNCForNetworkDrive(System.String) System.String GetSubstitutedPathForNetworkDosDevice(System.String) Boolean IsValidPath(System.String) Void GetItem(System.String) System.IO.FileSystemInfo GetFileSystemItem(System.String, Boolean ByRef, Boolean) Boolean ConvertPath(System.String, System.String, System.String ByRef, System.String ByRef) Void GetPathItems(System.String, Boolean, UInt32, Boolean, System.Management.Automation.ReturnContainers) Void Dir(System.IO.DirectoryInfo, Boolean, UInt32, Boolean, System.Management.Automation.ReturnContainers, InodeTracker) System.Management.Automation.FlagsExpression`1[System.IO.FileAttributes] FormatAttributeSwitchParamters() Void RenameItem(System.String, System.String) Void NewItem(System.String, System.String, System.Object) ItemType GetItemType(System.String) Void CreateDirectory(System.String, Boolean) Boolean CreateIntermediateDirectories(System.String) Void RemoveItem(System.String, Boolean) Void RemoveDirectoryInfoItem(System.IO.DirectoryInfo, Boolean, Boolean, Boolean) Void RemoveFileSystemItem(System.IO.FileSystemInfo, Boolean) Boolean ItemExists(System.String, System.Management.Automation.ErrorRecord ByRef) Boolean DirectoryInfoHasChildItems(System.IO.DirectoryInfo) Void CopyItem(System.String, System.String, Boolean) Void CopyItemFromRemoteSession(System.String, System.String, Boolean, Boolean, System.Management.Automation.Runspaces.PSSession) Void CopyDirectoryInfoItem(System.IO.DirectoryInfo, System.String, Boolean, Boolean, System.Management.Automation.PowerShell) Void CopyFileInfoItem(System.IO.FileInfo, System.String, Boolean, System.Management.Automation.PowerShell) Void CopyDirectoryFromRemoteSession(System.String, System.String, System.String, Boolean, Boolean, System.Management.Automation.PowerShell) System.Collections.ArrayList GetRemoteSourceAlternateStreams(System.Management.Automation.PowerShell, System.String) Void RemoveFunctionsPSCopyFileFromRemoteSession(System.Management.Automation.PowerShell) System.Collections.Hashtable GetRemoteFileMetadata(System.String, System.Management.Automation.PowerShell) Void SetFileMetadata(System.String, System.IO.FileInfo, System.Management.Automation.PowerShell) Void CopyFileFromRemoteSession(System.String, System.String, System.String, Boolean, System.Management.Automation.PowerShell, Int64) Boolean PerformCopyFileFromRemoteSession(System.String, System.IO.FileInfo, System.String, Boolean, System.Management.Automation.PowerShell, Int64, Boolean, System.String) Void RemoveFunctionPSCopyFileToRemoteSession(System.Management.Automation.PowerShell) Boolean RemoteTargetSupportsAlternateStreams(System.Management.Automation.PowerShell, System.String) System.String MakeRemotePath(System.Management.Automation.PowerShell, System.String, System.String) Boolean RemoteDirectoryExist(System.Management.Automation.PowerShell, System.String) Boolean CopyFileStreamToRemoteSession(System.IO.FileInfo, System.String, System.Management.Automation.PowerShell, Boolean, System.String) System.Collections.Hashtable GetFileMetadata(System.IO.FileInfo) Void SetRemoteFileMetadata(System.IO.FileInfo, System.String, System.Management.Automation.PowerShell) Boolean PerformCopyFileToRemoteSession(System.IO.FileInfo, System.String, System.Management.Automation.PowerShell) Boolean RemoteDestinationPathIsFile(System.String, System.Management.Automation.PowerShell) System.String CreateDirectoryOnRemoteSession(System.String, Boolean, System.Management.Automation.PowerShell) System.String GetParentPath(System.String, System.String) Boolean IsUNCPath(System.String) Boolean IsUNCRoot(System.String) Boolean IsPathRoot(System.String) System.String NormalizeRelativePath(System.String, System.String) System.String NormalizeRelativePathHelper(System.String, System.String) System.String RemoveRelativeTokens(System.String) System.Collections.Generic.Stack`1[System.String] TokenizePathToStack(System.String, System.String) System.Collections.Generic.Stack`1[System.String] NormalizeThePath(System.String, System.Collections.Generic.Stack`1[System.String]) System.String GetChildName(System.String) System.String EnsureDriveIsRooted(System.String) Void MoveItem(System.String, System.String) Void MoveFileInfoItem(System.IO.FileInfo, System.String, Boolean, Boolean) Void MoveDirectoryInfoItem(System.IO.DirectoryInfo, System.String, Boolean) Void CopyAndDelete(System.IO.DirectoryInfo, System.String, Boolean) Void GetProperty(System.String, System.Collections.ObjectModel.Collection`1[System.String]) Void SetProperty(System.String, System.Management.Automation.PSObject) Void ClearProperty(System.String, System.Collections.ObjectModel.Collection`1[System.String]) System.Management.Automation.Provider.IContentReader GetContentReader(System.String) System.Object GetContentReaderDynamicParameters(System.String) System.Management.Automation.Provider.IContentWriter GetContentWriter(System.String) Void ClearContent(System.String) Void ValidateParameters(Boolean) Void GetSecurityDescriptor(System.String, System.Security.AccessControl.AccessControlSections) Void SetSecurityDescriptor(System.String, System.Security.AccessControl.ObjectSecurity) Void SetSecurityDescriptor(System.String, System.Security.AccessControl.ObjectSecurity, System.Security.AccessControl.AccessControlSections) Void \u003cRemoveDirectoryInfoItem\u003eg__WriteErrorHelper|43_0(System.Exception, \u003c\u003ec__DisplayClass43_0 ByRef)",
                                                                                      "DeclaredNestedTypes":  "Microsoft.PowerShell.Commands.FileSystemProvider+ItemType Microsoft.PowerShell.Commands.FileSystemProvider+NativeMethods Microsoft.PowerShell.Commands.FileSystemProvider+NetResource Microsoft.PowerShell.Commands.FileSystemProvider+InodeTracker Microsoft.PowerShell.Commands.FileSystemProvider+\u003c\u003ec__DisplayClass43_0",
                                                                                      "DeclaredProperties":  "",
                                                                                      "ImplementedInterfaces":  "System.Management.Automation.IResourceSupplier System.Management.Automation.Provider.IContentCmdletProvider System.Management.Automation.Provider.IPropertyCmdletProvider System.Management.Automation.Provider.ISecurityDescriptorCmdletProvider System.Management.Automation.Provider.ICmdletProviderSupportsHelp",
                                                                                      "TypeInitializer":  "Void .cctor()",
                                                                                      "IsNested":  false,
                                                                                      "Attributes":  1048833,
                                                                                      "IsVisible":  true,
                                                                                      "IsNotPublic":  false,
                                                                                      "IsPublic":  true,
                                                                                      "IsNestedPublic":  false,
                                                                                      "IsNestedPrivate":  false,
                                                                                      "IsNestedFamily":  false,
                                                                                      "IsNestedAssembly":  false,
                                                                                      "IsNestedFamANDAssem":  false,
                                                                                      "IsNestedFamORAssem":  false,
                                                                                      "IsAutoLayout":  true,
                                                                                      "IsLayoutSequential":  false,
                                                                                      "IsExplicitLayout":  false,
                                                                                      "IsClass":  true,
                                                                                      "IsInterface":  false,
                                                                                      "IsValueType":  false,
                                                                                      "IsAbstract":  false,
                                                                                      "IsSealed":  true,
                                                                                      "IsSpecialName":  false,
                                                                                      "IsImport":  false,
                                                                                      "IsSerializable":  false,
                                                                                      "IsAnsiClass":  true,
                                                                                      "IsUnicodeClass":  false,
                                                                                      "IsAutoClass":  false,
                                                                                      "IsArray":  false,
                                                                                      "IsByRef":  false,
                                                                                      "IsPointer":  false,
                                                                                      "IsPrimitive":  false,
                                                                                      "IsCOMObject":  false,
                                                                                      "HasElementType":  false,
                                                                                      "IsContextful":  false,
                                                                                      "IsMarshalByRef":  false,
                                                                                      "GenericTypeArguments":  "",
                                                                                      "CustomAttributes":  "[System.Management.Automation.Provider.CmdletProviderAttribute(\"FileSystem\", (System.Management.Automation.Provider.ProviderCapabilities)52)] [System.Management.Automation.OutputTypeAttribute(typeof(System.Security.AccessControl.FileSecurity), ProviderCmdlet = \"Set-Acl\")] [System.Management.Automation.OutputTypeAttribute(new Type[2] { typeof(System.String), typeof(System.Management.Automation.PathInfo) }, ProviderCmdlet = \"Resolve-Path\")] [System.Management.Automation.OutputTypeAttribute(typeof(System.Management.Automation.PathInfo), ProviderCmdlet = \"Push-Location\")] [System.Management.Automation.OutputTypeAttribute(new Type[2] { typeof(System.Byte), typeof(System.String) }, ProviderCmdlet = \"Get-Content\")] [System.Management.Automation.OutputTypeAttribute(typeof(System.IO.FileInfo), ProviderCmdlet = \"Get-Item\")] [System.Management.Automation.OutputTypeAttribute(new Type[2] { typeof(System.IO.FileInfo), typeof(System.IO.DirectoryInfo) }, ProviderCmdlet = \"Get-ChildItem\")] [System.Management.Automation.OutputTypeAttribute(new Type[2] { typeof(System.Security.AccessControl.FileSecurity), typeof(System.Security.AccessControl.DirectorySecurity) }, ProviderCmdlet = \"Get-Acl\")] [System.Management.Automation.OutputTypeAttribute(new Type[4] { typeof(System.Boolean), typeof(System.String), typeof(System.IO.FileInfo), typeof(System.IO.DirectoryInfo) }, ProviderCmdlet = \"Get-Item\")] [System.Management.Automation.OutputTypeAttribute(new Type[5] { typeof(System.Boolean), typeof(System.String), typeof(System.DateTime), typeof(System.IO.FileInfo), typeof(System.IO.DirectoryInfo) }, ProviderCmdlet = \"Get-ItemProperty\")] [System.Management.Automation.OutputTypeAttribute(new Type[2] { typeof(System.String), typeof(System.IO.FileInfo) }, ProviderCmdlet = \"New-Item\")]"
                                                                                  },
                                                             "HelpFile":  "System.Management.Automation.dll-Help.xml",
                                                             "Name":  "FileSystem",
                                                             "PSSnapIn":  {
                                                                              "Name":  "Microsoft.PowerShell.Core",
                                                                              "IsDefault":  true,
                                                                              "ApplicationBase":  "C:\\Windows\\System32\\WindowsPowerShell\\v1.0",
                                                                              "AssemblyName":  "System.Management.Automation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, ProcessorArchitecture=MSIL",
                                                                              "ModuleName":  "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\System.Management.Automation.dll",
                                                                              "PSVersion":  "5.1.22621.4391",
                                                                              "Version":  "*******",
                                                                              "Types":  "types.ps1xml typesv3.ps1xml",
                                                                              "Formats":  "Certificate.format.ps1xml DotNetTypes.format.ps1xml FileSystem.format.ps1xml Help.format.ps1xml HelpV3.format.ps1xml PowerShellCore.format.ps1xml PowerShellTrace.format.ps1xml Registry.format.ps1xml",
                                                                              "Description":  "此 Windows PowerShell 管理单元包含用于管理 Windows PowerShell 组件的 cmdlet。",
                                                                              "Vendor":  "Microsoft Corporation",
                                                                              "LogPipelineExecutionDetails":  false
                                                                          },
                                                             "ModuleName":  "Microsoft.PowerShell.Core",
                                                             "Module":  null,
                                                             "Description":  "",
                                                             "Capabilities":  52,
                                                             "Home":  "C:\\Users\\<USER>